import {SVGProps} from "react";

function IconAdd({
  width = "20",
  height = "20",
  ...props
}: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox={`0 0 ${width} ${height}`}
      className={props.className}
      fill="none"
      {...props}
    >
      <path
        fill={props.fill || "#ffffff"}
        fillRule="evenodd"
        d="M10.75 5a.75.75 0 0 0-1.5 0v4.25H5a.75.75 0 0 0 0 1.5h4.25V15a.75.75 0 0 0 1.5 0v-4.25H15a.75.75 0 0 0 0-1.5h-4.25V5Z"
        clipRule="evenodd"
      />
    </svg>
  );
}

export default IconAdd;
