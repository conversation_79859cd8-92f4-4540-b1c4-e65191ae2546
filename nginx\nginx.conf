server {
    listen 80;
    server_name localhost;
    client_max_body_size 100M;
    root /usr/share/nginx/html;
    index index.html;

    location / {
        try_files $uri /index.html;
    }

    location /api/v1 {
        proxy_pass http://************:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location = /.well-known/assetlinks.json {
        alias /home/<USER>
        default_type application/json;
    }

    location = /apple-app-site-association {
        alias /home/<USER>
        default_type application/json;
    }

    error_page 404 /index.html;
}
