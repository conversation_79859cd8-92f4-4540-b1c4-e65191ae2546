import {useState} from "react";
import {useTranslation} from "react-i18next";
import Subtitle from "@components/Subtitle";
import {buttonClasses} from "@mui/base/Button";
import HeaderTitle from "@components/HeaderTitle";
import {styled} from "@mui/material";
import {Tabs} from "@mui/base/Tabs";
import {Tab as BaseTab, tabClasses} from "@mui/base/Tab";
import {TabPanel as BaseTabPanel} from "@mui/base/TabPanel";
import {TabsList as BaseTabsList} from "@mui/base/TabsList";
import RankSong from "./components/RankSong";

export default function Ranking() {
  const {t} = useTranslation();
  const countryItems = [
    {
      key: 1,
      label: t("common.country_name.vietnam"),
      params: {language: "vi"},
    },
    {
      key: 2,
      label: t("common.country_name.laos"),
      params: {language: "lo"},
    },
  ];
  const [activeTab, setActiveTab] = useState(1);

  return (
    <div className="flex flex-col gap-2 sm:gap-3 md:gap-4 lg:gap-5 pt-5 px-2 md:px-4 xl:px-8 text-white mb-[5vh]">
      <HeaderTitle title={t("common.home_sidebar.rankings")} />
      <Subtitle subtitle={t("ranking.new_music_chart")} seeMore={false} />
      <Tabs
        value={activeTab}
        onChange={(_e, newValue) => setActiveTab(Number(newValue))}
      >
        <div className="flex items-center gap-2">
          <TabsList className="max-[1270px]">
            {countryItems.map((item) => (
              <Tab key={item.key} value={item.key}>
                {item.label}
              </Tab>
            ))}
          </TabsList>
        </div>
        {countryItems.map((item) => (
          <TabPanel key={item.key} value={item.key}>
            <RankSong params={item.params} />
          </TabPanel>
        ))}
      </Tabs>
    </div>
  );
}
const Tab = styled(BaseTab)`
  color: white;
  cursor: pointer;
  font-size: 16px;
  background-color: #ffffff1a;
  padding: 8px 24px;
  margin-right: 12px;
  border: none;
  border-radius: 20px;
  display: flex;
  justify-content: center;
  margin-bottom: 8px;

  &:last-child {
    margin-right: 0;
  }

  &:hover {
    background-color: bg-[#FFFFFF14];
  }

  &.${tabClasses.selected} {
    background-color: #ff4319;
    color: #ffffff;
  }

  &.${buttonClasses.disabled} {
    opacity: 0.5;
    cursor: not-allowed;
  }

  @media (max-width: 640px) {
    padding: 6px 16px;
  }
`;

const TabPanel = styled(BaseTabPanel)`
  width: 100%;
  font-size: 0.875rem;
  color: #ffffff;
  padding: 16px 0;

  @media (max-width: 1024px) {
    padding: 8px 0;
  }
`;

const TabsList = styled(BaseTabsList)(
  () => `
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  `,
);
