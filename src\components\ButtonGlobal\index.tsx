import {CircularProgress} from "@mui/material";
import clsx from "clsx";
import React from "react";

interface GlobalButtonProps {
  text: string;
  onClick?: () => void;
  isLoading?: boolean;
  disabled?: boolean;
  color?: string;
  textClassName?: string;
  startIcon?: React.ReactNode;
  endIcon?: React.ReactNode;
  className?: string;
  styles?: StyleSheet;
}

const GlobalButton: React.FC<GlobalButtonProps> = ({
  text,
  onClick,
  isLoading = false,
  disabled = false,
  color = "#FF4319",
  textClassName = "text-white",
  startIcon,
  endIcon,
  className,
  styles,
}) => {
  return (
    <div
      onClick={onClick}
      className={`relative cursor-pointer select-none ${disabled || isLoading ? "opacity-50" : ""} px-4 py-2 ${className} rounded-lg font-medium`}
      style={{
        backgroundColor: isLoading ? "#FF4319" : color,
        display: "inline-flex",
        alignItems: "center",
        justifyContent: "center",
        height: "40px",
        ...styles,
      }}
    >
      {isLoading ? (
        <CircularProgress size={12} style={{color: "white"}} />
      ) : (
        <>
          {startIcon && <div className="mr-2">{startIcon}</div>}
          <span className={clsx("text-sm", textClassName)}>{text}</span>
          {endIcon && <div className="ml-2">{endIcon}</div>}
        </>
      )}
    </div>
  );
};

export default GlobalButton;
