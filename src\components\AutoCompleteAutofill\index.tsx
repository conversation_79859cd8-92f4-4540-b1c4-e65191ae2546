import {IAutoFill, IParamsAutofill} from "@api/ApiAutofill";
import {Autocomplete, Chip, TextField} from "@mui/material";
import {useInfiniteQuery} from "@tanstack/react-query";
import debounce from "lodash.debounce";
import React, {forwardRef, useCallback, useMemo, useState} from "react";
import {useTranslation} from "react-i18next";

interface IValue {
  id: string;
  label: string;
}

interface IExtraParams {
  artistIds?: string[];
}

interface IAutoCompleteProps {
  placeHolder?: string;
  suggestionAPI: (data: IParamsAutofill) => Promise<IAutoFill[]>;
  extraParams?: IExtraParams;
  onChange?: (selected: IValue[] | IValue) => void;
  name: string;
  pageSize?: number | undefined;
  multiple?: boolean;
  freeSolo?: boolean;
  value?: IValue[] | IValue | null;
  className?: string;
}

export default function AutoCompleteAutofill({
  placeHolder,
  suggestionAPI,
  onChange,
  name,
  pageSize = 10,
  multiple,
  freeSolo,
  value,
  className,
  extraParams = {},
}: IAutoCompleteProps) {
  const {t} = useTranslation();

  const [searchText, setSearchText] = useState("");

  const {data, fetchNextPage, hasNextPage} = useInfiniteQuery({
    queryKey: ["autoCompleteSuggestions", searchText, name, extraParams],
    queryFn: async ({pageParam = 0}) => {
      const params: IParamsAutofill = {
        keyword: searchText,
        page: pageParam,
        pageSize,
        ...extraParams,
      };

      const result = await suggestionAPI(params);
      return {
        data: result,
        nextPage: result.length === pageSize ? pageParam + 1 : undefined,
      };
    },
    initialPageParam: 0,
    getNextPageParam: (lastPage) => lastPage.nextPage,
    gcTime: 0,
  });

  // Convert id - name to id - label
  const suggestions = useMemo(() => {
    if (!data) return [];

    const allSuggestions = data.pages.flatMap((page) =>
      page.data.map((item) => ({
        label: item?.name ?? "",
        id: item?.id ?? "",
      })),
    );

    // Remove duplicate by id
    const uniqueMap = new Map();
    allSuggestions.forEach((item) => {
      uniqueMap.set(item.id, item);
    });

    return Array.from(uniqueMap.values());
  }, [data]);

  const debouncedSearch = useCallback(
    debounce((value: string) => {
      setSearchText(value);
    }, 300),
    [],
  );

  const handleScroll = (event: React.UIEvent<HTMLUListElement>) => {
    const listboxNode = event.currentTarget;
    const isAtBottom =
      Math.abs(
        listboxNode.scrollHeight -
          listboxNode.scrollTop -
          listboxNode.clientHeight,
      ) < 1;

    if (isAtBottom && hasNextPage) {
      fetchNextPage();
    }
  };

  return (
    <Autocomplete
      className={`cms-autocomplete ${className} max-h-[250px] overflow-auto`}
      multiple={multiple}
      freeSolo={freeSolo}
      options={suggestions}
      isOptionEqualToValue={(option, value) => option.id === value.id}
      getOptionKey={(option) => {
        return option.id;
      }}
      value={value}
      filterOptions={(options) => options}
      onInputChange={(_event, value, reason) => {
        if (reason === "input" || reason === "clear") {
          debouncedSearch(value);
        }
      }}
      slotProps={{
        listbox: {
          component: CustomListbox,
          onScroll: handleScroll,
        },
      }}
      renderTags={(value: IValue[], getTagProps) =>
        value.map((option, index) => {
          const tagProps = getTagProps({index});
          const {key, ...rest} = tagProps;

          return (
            <div key={key} style={{display: "block", marginBottom: 4}}>
              <Chip
                label={`${index + 1}. ${option.label}`}
                {...rest}
                style={{width: "100%"}}
              />
            </div>
          );
        })
      }
      onChange={(_event, value: IValue[] | IValue) => {
        if (onChange) {
          if (multiple) {
            onChange(value as IValue[]);
          } else {
            onChange(value as IValue);
          }
        }
      }}
      renderInput={(params) => (
        <TextField
          {...params}
          placeholder={placeHolder || t("common.search")}
        />
      )}
    />
  );
}

const CustomListbox = forwardRef(function CustomListbox(props: any, ref: any) {
  // TODO: Remove "any" later
  return (
    <div ref={ref} {...props} style={{maxHeight: 200, overflowY: "auto"}} />
  );
});
