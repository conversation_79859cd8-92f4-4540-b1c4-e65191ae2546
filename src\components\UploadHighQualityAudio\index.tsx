import React, {useEffect, useState} from "react";
import {IconButton} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import AudiotrackIcon from "@mui/icons-material/Audiotrack";
import clsx from "clsx";
import {useTranslation} from "react-i18next";

interface IUploadAudio {
  hasLabel?: boolean;
  wrapperClassname?: string;
  helperText?: JSX.Element;
  onFileUpload?: (file: File | null) => void;
  requireMark?: boolean;
  value?: File | string | null;
}

export default function UploadHighQualityAudio({
  hasLabel,
  wrapperClassname,
  helperText,
  onFileUpload,
  requireMark,
  value,
}: IUploadAudio) {
  const {t} = useTranslation();

  const [selectedFile, setSelectedFile] = useState<File | string | null>(null);

  useEffect(() => {
    if (value) {
      setSelectedFile(value);
    }
  }, [value]);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      if (onFileUpload) {
        onFileUpload(file);
      }

      setSelectedFile(file);
    }
  };

  const handleRemoveFile = () => {
    setSelectedFile(null);
    if (onFileUpload) {
      onFileUpload(null);
    }
  };

  return (
    <div className={clsx("flex flex-col gap-2.5", wrapperClassname)}>
      {hasLabel && (
        <label className="block text-sm font-bold text-gray-700">
          {t("common.audio_file")}{" "}
          {requireMark && <span className="text-red-600">*</span>}
        </label>
      )}

      {!selectedFile ? (
        <label className="flex items-center justify-center w-full rounded-lg border border-dashed border-orange-500 bg-orange-800 h-16 cursor-pointer">
          <div className="flex flex-col items-center justify-center">
            <div className="flex flex-row gap-2">
              <img src="/image/upload.svg" alt="Upload" />
              <span className="text-sm text-orange-500">
                {t("common.upload_song_file")}
              </span>
            </div>
            <input
              type="file"
              accept="audio/*"
              className="hidden"
              onChange={handleFileChange}
            />
          </div>
        </label>
      ) : (
        <div className="relative flex items-center justify-between gap-4 rounded-lg border border-gray-300 bg-white p-3">
          <AudiotrackIcon className="text-orange-500" fontSize="large" />
          {typeof selectedFile === "string" ? (
            <>
              <div className="text-sm font-medium text-gray-800 max-w-full break-all line-clamp-1">
                {selectedFile.split("/").pop()}
              </div>
              <audio controls className="w-full mt-2">
                <source src={selectedFile} type="audio/mpeg" />
              </audio>
            </>
          ) : (
            <>
              <div className="text-sm font-medium text-gray-800 max-w-full break-all line-clamp-1">
                {selectedFile?.name}
              </div>
              <div className="text-xs text-gray-500">
                {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
              </div>
            </>
          )}
          <IconButton
            onClick={handleRemoveFile}
            className="text-gray-500 hover:text-red-600"
            size="small"
          >
            <CloseIcon />
          </IconButton>
        </div>
      )}
      {helperText}
    </div>
  );
}
