import {SVGProps} from "react";

function IconVerify(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      width="18"
      height="18"
      viewBox="0 0 18 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <rect width="18" height="18" fill="url(#pattern0_1_22995)" />
      <defs>
        <pattern
          id="pattern0_1_22995"
          patternContentUnits="objectBoundingBox"
          width="1"
          height="1"
        >
          <use xlinkHref="#image0_1_22995" transform="scale(0.01)" />
        </pattern>
        <image
          id="image0_1_22995"
          width="100"
          height="100"
          preserveAspectRatio="none"
          xlinkHref="data:image/png;base64,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"
        />
      </defs>
    </svg>
  );
}

export default IconVerify;
