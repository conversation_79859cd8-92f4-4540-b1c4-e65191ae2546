import {SVGProps} from "react";

export default function ICEmptyCircle({
  width = 206,
  height = 206,
  ...props
}: SVGProps<SVGSVGElement>): JSX.Element {
  return (
    <svg
      width={width}
      height={height}
      viewBox={`0 0 ${width} ${height}`}
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <circle
        cx="103.538"
        cy="9.80422"
        r="102.944"
        stroke="white"
        strokeOpacity="0.44"
        strokeWidth="0.754167"
      />
    </svg>
  );
}
