import clsx from "clsx";
import {<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, SVGProps} from "react";

interface CustomIconButtonProps {
  Icon: (props: SVGProps<SVGSVGElement>) => JSX.Element;
  svgProps?: SVGProps<SVGSVGElement>;
  onClick?: MouseEventHandler<HTMLButtonElement>;
  disabled?: boolean;
  className?: string;
}

export default function CustomIconButton({
  Icon,
  svgProps,
  onClick,
  disabled,
  className,
}: CustomIconButtonProps) {
  return (
    <button
      className={clsx(className, Boolean(disabled) && "opacity-55")}
      onClick={onClick}
      disabled={disabled}
    >
      {<Icon {...svgProps} />}
    </button>
  );
}
