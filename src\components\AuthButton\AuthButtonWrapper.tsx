import {EGlobalModal, showModal} from "@redux/slices/GlobalModalSlice";
import {IRootState} from "@redux/store";
import {<PERSON><PERSON><PERSON>Handler} from "react";
import {useDispatch, useSelector} from "react-redux";

interface IAuthButtonWrapperProps {
  action?: MouseEventHandler;
  children?: React.ReactNode;
  className?: string;
}

export default function AuthButtonWrapper({
  action,
  children,
  className,
}: IAuthButtonWrapperProps): JSX.Element {
  const {accessToken} = useSelector((state: IRootState) => state?.user);
  const dispatch = useDispatch();

  const handleShowModal = () => {
    dispatch(showModal(EGlobalModal.AUTH_MODAL));
  };

  return (
    <div onClick={accessToken ? action : handleShowModal} className={className}>
      {children}
    </div>
  );
}
