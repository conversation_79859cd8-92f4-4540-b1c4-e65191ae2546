import {EThemeAndGenreType, IThemeAndGenre} from "src/types";
import {fetcher, fetcherWithMetadata, IDataWithMeta} from "./Fetcher";

const path = {
  cmsThemeAndGenre: "cms/genres",
};

export interface IGetThemeAndGenreList {
  type: EThemeAndGenreType;
  page?: number;
  pageSize?: number;
  keyword?: string;
  fromDate?: string;
  toDate?: string;
  order?: string;
  direction?: string;
  createdAt?: string;
}

export interface IGetGenresSongs {
  keyword?: string;
  fromDate?: string;
  toDate?: string;
  page?: number;
  pageSize: number;
  order?: string;
  themeId?: string;
  genreId?: string;
  albumId?: string;
  updatedAt?: string;
  direction?: string;
}

interface IValue {
  id: string;
  label: string;
}

export interface ICreateThemeAndGenre {
  name: string;
  image?: File | null;
  type?: EThemeAndGenreType;
  description?: string;
  parentId?: IValue;
  nameLo?: string;
  nameEn?: string;
  nameVi?: string;
}

function getThemeAndGenreDetail(id: string) {
  return fetcher<IThemeAndGenre>({
    url: `${path.cmsThemeAndGenre}/${id}`,
    method: "get",
  });
}

function getThemeAndGenreList(
  params: IGetThemeAndGenreList,
): Promise<IDataWithMeta<IThemeAndGenre[]>> {
  return fetcherWithMetadata(
    {
      url: path.cmsThemeAndGenre,
      method: "get",
      params,
    },
    {
      displayError: false,
      withMetadata: true,
    },
  );
}

function createThemeAndGenre(data: ICreateThemeAndGenre) {
  return fetcher(
    {
      url: path.cmsThemeAndGenre,
      method: "post",
      data: data,
    },
    {isFormData: true},
  );
}

function updateThemeAndGenre({
  id,
  ...restData
}: {id: string} & ICreateThemeAndGenre) {
  return fetcher(
    {
      url: `${path.cmsThemeAndGenre}/${id}`,
      method: "patch",
      data: restData,
    },
    {
      isFormData: true,
    },
  );
}

function deleteThemeAndGenre(id: string) {
  return fetcher({
    url: `${path.cmsThemeAndGenre}/${id}`,
    method: "delete",
  });
}

function getGenresSongs(
  id: string,
  params: IGetGenresSongs,
): Promise<IDataWithMeta<[]>> {
  return fetcherWithMetadata(
    {
      url: `${path.cmsThemeAndGenre}/${id}/songs`,
      method: "get",
      params,
    },
    {
      displayError: false,
    },
  );
}

export default {
  getThemeAndGenreList,
  createThemeAndGenre,
  getThemeAndGenreDetail,
  deleteThemeAndGenre,
  updateThemeAndGenre,
  getGenresSongs,
};
