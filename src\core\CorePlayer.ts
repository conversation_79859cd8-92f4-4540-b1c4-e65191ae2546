import {ISong} from "src/types";
import {IBasePlayer, EPlayerType} from "./types";
import <PERSON><PERSON><PERSON>enerHandler from "./PlayerListenerHandler";
import store from "@redux/store";
import {handleNextSong, handlePrevSong} from "@redux/slices/PlayerSlice";
import {t} from "i18next";

export default class CorePlayer implements IBasePlayer {
  type = EPlayerType.CORE;
  ref: HTMLAudioElement;
  ready = false;

  constructor(audioRef: HTMLAudioElement) {
    this.ref = audioRef;
    this.ref.ondurationchange = () => {
      PlayerListenerHandler.handleOndurationChange(this);
    };

    this.ref.onloadedmetadata = () => {
      if (!this.ready) {
        PlayerListenerHandler.handleOnReady(this);
        this.ready = true;
      }
      PlayerListenerHandler.handleOnLoadedMetadata(this);
      const {currentSong, queueList} = store.getState().player;
      if (currentSong) {
        if ("mediaSession" in navigator) {
          navigator.mediaSession.setActionHandler("play", () => this.play());
          navigator.mediaSession.setActionHandler("pause", () => this.pause());
          navigator.mediaSession.setActionHandler("nexttrack", () => {
            if (!currentSong || !queueList) return;
            store.dispatch(handleNextSong({isManual: true}));
          });
          navigator.mediaSession.setActionHandler("previoustrack", () => {
            if (!currentSong || !queueList) return;
            store.dispatch(handlePrevSong());
          });

          this.updateMediaSession(currentSong);
        }
      }
    };

    this.ref.onplaying = () => {
      PlayerListenerHandler.handleOnStart(this);
    };
    this.ref.onended = () => {
      PlayerListenerHandler.handleOnEnded(this);
    };
    this.ref.onerror = (error) => {
      PlayerListenerHandler.handleOnError(new Error(error.toString()));
    };
    this.ref.ontimeupdate = () => {
      PlayerListenerHandler.handleOnTimeChange(this);
    };
    this.ref.onpause = () => {
      PlayerListenerHandler.handleOnPause(this);
    };
    this.ref.onplay = () => {
      PlayerListenerHandler.handleOnPlay(this);
    };
  }

  get muted() {
    return this.ref.muted;
  }

  get paused() {
    return this.ref.paused;
  }

  get duration() {
    return this.ref.duration;
  }

  get currentTime() {
    return this.ref.currentTime;
  }

  get volume() {
    return this.ref.volume;
  }

  loadSong(songData: ISong) {
    if (!songData?.audios?.[0]?.url) {
      PlayerListenerHandler.handleOnError(
        new Error(t("youtube_error.unsupported_format")),
      );
    } else {
      this.ref.load();
      const musicQuality = store.getState().settings.musicQuality;
      const url =
        songData.audios?.find((audio) => audio?.type === musicQuality)?.url ||
        songData.audios?.[0]?.url;
      this.ref.src = url;
      this.ref.load();
    }
  }

  isSongPlaying(data?: ISong): boolean {
    if (!data?.audios) return false;
    return (
      data?.audios?.findIndex((audio) => audio.url === this.ref.src) !== -1
    );
  }

  play() {
    this.ref.play();
  }

  pause() {
    this.ref.pause();
  }

  seek(time: number) {
    this.ref.currentTime = time;
  }

  setVolume(volume: number) {
    this.ref.volume = volume;
  }

  mute() {
    this.ref.muted = true;
  }

  unMute() {
    this.ref.muted = false;
  }

  clear() {
    this.ref.src = "";
    this.ref.removeAttribute("src");
  }

  private updateMediaSession(song: ISong) {
    navigator.mediaSession.metadata = new MediaMetadata({
      title: song.name,
      artist:
        song.artists
          ?.map((artist) => artist?.stageName ?? artist?.name)
          ?.join(", ") || t("common.not_info_artist"),
      artwork: [
        {
          src:
            song.images?.SMALL ||
            song.images?.DEFAULT ||
            "/image/default-music.png",
          sizes: "96x96",
          type: "image/png",
        },
        {
          src:
            song.images?.SMALL ||
            song.images?.DEFAULT ||
            "/image/default-music.png",
          sizes: "128x128",
          type: "image/png",
        },
        {
          src:
            song.images?.DEFAULT ||
            song.images?.SMALL ||
            "/image/default-music.png",
          sizes: "192x192",
          type: "image/png",
        },
        {
          src:
            song.images?.DEFAULT ||
            song.images?.SMALL ||
            "/image/default-music.png",
          sizes: "256x256",
          type: "image/png",
        },
        {
          src:
            song.images?.DEFAULT ||
            song.images?.SMALL ||
            "/image/default-music.png",
          sizes: "384x384",
          type: "image/png",
        },
        {
          src:
            song.images?.DEFAULT ||
            song.images?.SMALL ||
            "/image/default-music.png",
          sizes: "512x512",
          type: "image/png",
        },
      ],
      album: song.playlists?.[0]?.name,
    });
  }
}
