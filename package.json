{"name": "laomusic", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:miniapp": "vite build --config vite.miniapp.config.ts", "lint": "eslint .", "preview": "vite preview", "prettier": "prettier --check \"./src/**/*.{js,jsx,ts,tsx}\"", "check-types": "tsc --pretty --noEmit", "prepare": "husky install", "prettier:fix": "prettier --write ."}, "dependencies": {"@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@fontsource/roboto": "^5.1.1", "@mui/icons-material": "^6.1.7", "@mui/lab": "^6.0.0-beta.26", "@mui/material": "^6.1.7", "@mui/x-data-grid": "^7.22.3", "@mui/x-date-pickers": "^7.25.0", "@reduxjs/toolkit": "^2.3.0", "@tailwindcss/container-queries": "^0.1.1", "@tanstack/react-query": "^5.60.5", "autoprefixer": "^10.4.20", "axios": "^1.7.7", "clsx": "^2.1.1", "color": "^5.0.0", "cropperjs": "^1.6.2", "dayjs": "^1.11.13", "firebase": "^11.5.0", "formik": "^2.4.6", "i18next": "^23.16.8", "i18next-browser-languagedetector": "^8.0.4", "lodash.debounce": "^4.0.8", "moment": "^2.30.1", "postcss": "^8.4.49", "react": "^18.3.1", "react-cropper": "^2.3.3", "react-dom": "^18.3.1", "react-i18next": "^15.4.1", "react-redux": "^9.2.0", "react-router-dom": "^7.1.1", "react-toastify": "^11.0.3", "recharts": "^2.15.1", "redux": "^5.0.1", "redux-persist": "^6.0.0", "sass": "^1.81.0", "swiper": "^11.2.4", "tailwindcss": "^3.4.15", "yup": "^1.4.0"}, "devDependencies": {"@eslint/js": "^9.13.0", "@types/lodash.debounce": "^4.0.9", "@types/node": "^22.10.1", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@types/recharts": "^1.8.29", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.13.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "globals": "^15.11.0", "husky": "^9.1.7", "lint-staged": "^15.2.10", "prettier": "^3.3.3", "typescript": "~5.6.2", "typescript-eslint": "^8.11.0", "vite": "^6.0.11", "vite-tsconfig-paths": "^5.1.2"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --cache --fix", "prettier --check"]}}