import IconNoData from "@components/Icon/IconNoData";
import {useTranslation} from "react-i18next";
import {IPlaylist, PlaylistType} from "src/types";
import {useEffect, useRef} from "react";
import {useInfiniteQuery} from "@tanstack/react-query";
import {IDataWithMeta} from "@api/Fetcher";
import QUERY_KEY from "@api/QueryKey";
import ApiSearch from "@api/ApiSearch";
import {Grid} from "@mui/material";
import CommonAlbumCard from "@components/CommonAlbumCard";
import AlbumCardSkeleton from "@components/AlbumCardSkeleton";
import {useLocation} from "react-router-dom";
import Subtitle from "@components/Subtitle";

interface PlaylistsViewProps {
  type: PlaylistType;
}

export function PlaylistsView({type}: PlaylistsViewProps) {
  const {t} = useTranslation();
  const location = useLocation();
  const query = new URLSearchParams(location.search);
  const searchValue = query.get("q");
  const observer = useRef<IntersectionObserver | null>(null);
  const lastElementRef = useRef<HTMLDivElement | null>(null);
  const {
    data: dataPlaylist,
    fetchNextPage,
    isLoading,
    isError,
    hasNextPage,
  } = useInfiniteQuery<IDataWithMeta<IPlaylist[]>, Error>({
    queryKey: [
      type === PlaylistType.ALBUM
        ? QUERY_KEY.SEARCH.GET_SEARCH_ALBUMS
        : QUERY_KEY.SEARCH.GET_SEARCH_PLAYLISTS,
      searchValue,
    ],
    queryFn: ({pageParam = 0}) =>
      ApiSearch.searchPlaylists({
        keyword: searchValue || "",
        pageSize: 15,
        page: pageParam as number,
        type,
      }),
    getNextPageParam: (lastPage) =>
      lastPage?.meta?.totalPage - 1 > lastPage?.meta?.currentPage
        ? (lastPage?.meta?.currentPage || 0) + 1
        : undefined,
    initialPageParam: 0,
  });

  useEffect(() => {
    if (isLoading || isError) return;

    if (observer.current) observer.current.disconnect();

    observer.current = new IntersectionObserver((entries) => {
      if (entries[0].isIntersecting && hasNextPage) {
        fetchNextPage();
      }
    });

    if (lastElementRef?.current) {
      observer.current.observe(lastElementRef.current);
    }

    return () => {
      if (observer.current) {
        observer.current.disconnect();
      }
    };
  }, [isLoading, isError, dataPlaylist, hasNextPage]);

  const isEmpty =
    !dataPlaylist ||
    !dataPlaylist.pages ||
    dataPlaylist.pages.every((page) => page.data.length === 0);

  return (
    <div className="w-full flex flex-col gap-1 sm:gap-2 md:gap-3 lg:gap-4">
      <Subtitle
        subtitle={
          type === PlaylistType.ALBUM ? t("common.album") : t("common.playlist")
        }
        seeMore={false}
      />
      {isLoading && !dataPlaylist && (
        <Grid
          container
          spacing={{xs: 2, md: 3}}
          columns={{xs: 2, sm: 6, md: 12, lg: 15}}
        >
          {[...Array(5)].map((_, index) => (
            <Grid item xs={1} sm={2} md={3} lg={3} key={index}>
              <AlbumCardSkeleton
                isMultipleInfo={type === PlaylistType.ALBUM ? true : false}
              />
            </Grid>
          ))}
        </Grid>
      )}
      {!isLoading && (isError || isEmpty) && (
        <div className=" flex justify-center items-center flex-col lg:gap-2.5 gap-1">
          <IconNoData />
          <span className="text-[#E3E3E3] w-full text-base font-normal flex justify-center">
            {type === PlaylistType.ALBUM
              ? t("common.album_list_not_found")
              : t("common.playlist_list_not_found")}
          </span>
        </div>
      )}
      <div>
        <Grid
          container
          spacing={{xs: 2, md: 3}}
          columns={{xs: 2, sm: 6, md: 12, lg: 15}}
        >
          {!isLoading &&
            !(isError || isEmpty) &&
            dataPlaylist?.pages?.map((page) =>
              page?.data?.map((playlist, index) => (
                <Grid item xs={1} sm={2} md={3} lg={3} key={index}>
                  <CommonAlbumCard
                    data={playlist}
                    haveLayer={type === PlaylistType.ALBUM ? true : false}
                  />
                </Grid>
              )),
            )}
        </Grid>
        {hasNextPage && (
          <Grid
            container
            spacing={{xs: 2, md: 3}}
            columns={{xs: 2, sm: 6, md: 12, lg: 15}}
          >
            {[...Array(5)].map((_, index) => (
              <Grid item xs={1} sm={2} md={3} lg={3} key={index}>
                <AlbumCardSkeleton
                  isMultipleInfo={type === PlaylistType.ALBUM ? true : false}
                />
              </Grid>
            ))}
          </Grid>
        )}
      </div>
      {!isLoading && <div ref={lastElementRef} className="h-1"></div>}
    </div>
  );
}
