import IconArrowDown from "@components/Icon/IconArrowDown";

export default function RankChange({change}: {change?: number}) {
  if (change === 0) {
    return <div className="h-[1px] bg-[#A7A5A3] w-3" />;
  }

  return (
    <div className="flex flex-col items-center justify-center gap-y-2">
      {change && change > 0 ? (
        <IconArrowDown className="text-green-500 rotate-180" />
      ) : (
        <IconArrowDown className="text-red-500" />
      )}
      <span className="text-xs font-bold leading-3">
        {" "}
        {Math.abs(change || 0)}
      </span>
    </div>
  );
}
