import {convertDate, convertNumber} from "src/utils/timeUtils";
import {useTranslation} from "react-i18next";
import {GenderEnum, IArtist} from "src/types";
import {CircularProgress} from "@mui/material";
import {
  useArtistTypeOptions,
  getStatusArtist,
  useGenderOptions,
  useCountryOptions,
} from "src/utils/global";
import BasicInfoCmsCustom from "@components/BasicInfoCmsCustom";

interface InformationArtistProps {
  dataArtist: IArtist;
  fetcherDataArtist: boolean;
}

export default function InformationArtist({
  dataArtist,
  fetcherDataArtist,
}: InformationArtistProps) {
  const {t} = useTranslation();

  const artistTypeOptions = useArtistTypeOptions();
  const artistType =
    artistTypeOptions.find((option) => option.value === dataArtist?.type)
      ?.label || "-";

  const genderOptions = useGenderOptions();
  const genderLabel =
    genderOptions.find(
      (option) =>
        option.value === (dataArtist?.user?.gender as unknown as GenderEnum),
    )?.label || "-";

  const {colorStatus, textStatus} = getStatusArtist(
    Number(dataArtist?.user?.status),
  );

  const countryOptions = useCountryOptions();
  const countryLabel =
    countryOptions.find(
      (option) => option.value === dataArtist?.user?.countryName,
    )?.label || "-";

  const basicInfoCol1 = [
    {
      title: t("cms.artist.artist_name"),
      value: (
        <span className="text-sm font-semibold text-gray-default">
          {dataArtist?.name || "-"}
        </span>
      ),
    },
    {
      title: t("cms.artist.stage_name"),
      value: (
        <span className="text-sm font-semibold text-gray-default">
          {dataArtist?.stageName || "-"}
        </span>
      ),
    },
    {
      title: t("cms.artist.artist_type"),
      value: (
        <span className="text-sm whitespace-nowrap text-left font-semibold text-gray-default">
          {artistType}
        </span>
      ),
    },
    {
      title: t("common.birthday"),
      value: (
        <span className="text-sm whitespace-nowrap text-left font-semibold text-gray-default">
          {convertDate(dataArtist?.user?.dateOfBirth)}
        </span>
      ),
    },
    {
      title: t("common.gender"),
      value: (
        <span className="text-sm whitespace-nowrap text-left font-semibold text-gray-default">
          {genderLabel}
        </span>
      ),
    },
  ];

  const basicInfoCol2 = [
    {
      title: t("common.quantity_song"),
      value: (
        <div className="text-sm font-semibold flex gap-1">
          <span>{convertNumber(dataArtist?.totalSongs)}</span>
          <span>{t("common.song")}</span>
        </div>
      ),
    },
    {
      title: t("cms.artist.quantity_album"),
      value: (
        <div className="text-sm font-semibold flex gap-1">
          <span>{convertNumber(dataArtist.totalPlaylists)}</span>
          <span>{t("common.album")}</span>
        </div>
      ),
    },
    {
      title: t("common.country"),
      value: (
        <span className="text-sm whitespace-nowrap text-left font-semibold text-gray-default">
          {countryLabel}
        </span>
      ),
    },
    {
      title: t("common.operating_status"),
      value: (
        <div
          className={`flex gap-2 items-center text-sm font-semibold`}
          style={{color: colorStatus}}
        >
          <span className="text-lg">•</span>
          <span>{textStatus}</span>
        </div>
      ),
    },
  ];

  return (
    <>
      {fetcherDataArtist && (
        <div className="flex items-center justify-center w-full h-[293px]">
          <CircularProgress sx={{color: "blue"}} />
        </div>
      )}
      {!fetcherDataArtist && dataArtist && (
        <div className="max-h-[50vh] overflow-y-auto">
          <BasicInfoCmsCustom
            image={dataArtist?.images?.DEFAULT || "/image/default-music.png"}
            basicInfoCol1={basicInfoCol1}
            basicInfoCol2={basicInfoCol2}
            description={dataArtist?.biography || "-"}
          />
        </div>
      )}
    </>
  );
}
