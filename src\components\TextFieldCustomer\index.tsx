import {ErrorMessage, Field} from "formik";

const TextFieldCustomer = ({name, label, required = false, ...props}: any) => (
  <div>
    <label className="block text-sm font-bold text-gray-700 pb-2">
      {label}
      {required && <span className="text-red-600"> *</span>}
    </label>
    <Field
      name={name}
      fullWidth
      size="small"
      className="custom-input-info w-full"
      {...props}
    />
    <div className="text-red-500 text-sm mt-2">
      <ErrorMessage name={name} />
    </div>
  </div>
);

export default TextFieldCustomer;
