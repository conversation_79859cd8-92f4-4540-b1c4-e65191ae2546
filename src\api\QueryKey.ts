const QUERY_KEY = {
  SONG: {
    GET_LIST_SONG_CMS: "GET_LIST_SONG_CMS",
    GET_SONG_BY_ID: "GET_SONG_BY_ID",
    GET_SONG_BY_URL_SLUG: "GET_SONG_BY_URL_SLUG",
    GET_SONGS_RECOMMENDED: "GET_SONGS_RECOMMENDED",
    GET_TRENDING_YOUTUBE: "GET_TRENDING_YOUTUBE",
    GET_INTERACTIONS_SONG: "GET_INTERACTIONS_SONG",
    GET_LYRIC_SONG: "GET_LYRIC_SONG",
  },
  PLAYLIST: {
    GET_PLAYLIST_DETAIL: "GET_PLAYLIST_DETAIL",
    GET_PLAYLIST_SONGS: "GET_PLAYLIST_SONGS",
    GET_RELEVANT_PLAYLISTS: "GET_RELEVANT_PLAYLISTS",
    GET_ARTISTS_LIST: "GET_ARTISTS_LIST",
    GET_LIST_PLAYLIST_CMS: "GET_LIST_PLAYLIST_CMS",
    GET_PLAYLIST_DETAIL_CMS: "GET_PLAYLIST_DETAIL_CMS",
    GET_LIST_PLAYLIST_SONGS_CMS: "GET_LIST_PLAYLIST_SONGS_CMS",
    UPDATE_MY_PLAYLIST: "UPDATE_MY_PLAYLIST",
    DELETE_MY_PLAYLIST: "DELETE_MY_PLAYLIST",
  },
  ALBUM: {
    GET_LIST_ALBUM_CMS: "GET_LIST_ALBUM_CMS",
    GET_TOP_100: "GET_TOP_100",
    GET_ALBUM_DETAIL: "GET_ALBUM_DETAIL",
    GET_LIST_ALBUM_SONGS_CMS: "GET_LIST_ALBUM_SONGS_CMS",
  },
  THEME_AND_GENRE: {
    GET_LIST_THEME_AND_GENRE_CMS: "GET_LIST_THEME_AND_GENRE_CMS",
    GET_DETAIL_BY_ID: "GET_DETAIL_BY_ID",
    GET_LIST_GENRES_SONGS: "GET_LIST_GENRES_SONGS",
  },
  TOP_100: {
    GET_LIST_ALBUM_TOP_100: "GET_LIST_ALBUM_TOP_100",
    GET_ALBUM_TOP_100_DETAIL: "GET_ALBUM_TOP_100_DETAIL",
    GET_LIST_TOP_100_SONGS_CMS: "GET_LIST_TOP_100_SONGS_CMS",
  },

  CMS_USER: {
    GET_CMS_USER: "GET_CMS_USER",
  },

  TOPIC: {
    GET_SONG: "GET_SONG",
    GET_PLAYLIST: "GET_PLAYLIST",
    GET_ARTIST: "GET_ARTIST",
  },
  PLAY_MUSIC: {
    GET_SUGGEST_SONGS: "GET_SUGGEST_SONGS",
  },
  // Auto fill
  AUTO_FILL: {
    GET_AUTO_ARTIST: "GET_AUTO_ARTIST",
    GET_AUTO_LYRICIST: "GET_AUTO_LYRICIST",
    GET_AUTO_PRODUCER: "GET_AUTO_PRODUCER",
    GET_AUTO_COMPOSER: "GET_AUTO_COMPOSER",
    GET_AUTO_THEME: "GET_AUTO_THEME",
    GET_AUTO_GENRE: "GET_AUTO_GENRE",
    GET_AUTO_ALBUM: "GET_AUTO_ALBUM",
    GET_AUTO_TAGS: "GET_AUTO_TAGS",
    GET_AUTO_LYRICS: "GET_AUTO_LYRICS",
  },

  USER: {
    GET_USER: "GET_USER",
    GET_LIST_USER: "GET_LIST_USER",
    UPDATE_USER_INFO: "UPDATE_USER_INFO",
    GET_INFO: "GET_INFO",
  },
  LIBRARY: {
    GET_MY_PLAYLISTS: "GET_MY_PLAYLISTS",
    GET_MY_FAVORITE_ARTISTS: "GET_MY_FAVORITE_ARTISTS",
    GET_MY_FAVORITE_PLAYLISTS: "GET_MY_FAVORITE_PLAYLISTS",
    GET_MY_FAVORITE_SONGS: "GET_MY_FAVORITE_SONGS",
    GET_MY_FAVORITE_ALBUMS: "GET_MY_FAVORITE_ALBUMS",
    IS_SONG_IN_PLAYLISTS: "IS_SONG_IN_PLAYLISTS",
    GET_MY_PLAYLISTS_VIEW: "GET_MY_PLAYLISTS_VIEW",
    GET_MY_FAVORITE_YOUTUBE: "GET_MY_FAVORITE_YOUTUBE",
  },
  RAKING_BOARD: {
    GET_LIST_SONG: "LIST_SONG",
  },
  GENRE: {
    GET_TOP_GENRE: "GET_TOP_GENRE",
    GET_GENRE: "GET_GENRE",
  },
  ARTIST: {
    GET_TOP_ARTIST_FAVOURITE: "GET_TOP_ARTIST_FAVOURITE",
    GET_ARTIST_DETAIL: "GET_ARTIST_DETAIL",
    GET_ARTIST_SONGS: "GET_ARTIST_SONGS",
    GET_ARTIST_PLAYLISTS: "GET_ARTIST_PLAYLISTS",
    GET_ARTIST_FAVORITE_PLAYLISTS: "GET_ARTIST_FAVORITE_PLAYLISTS",
    GET_COLLAB_ARTIST: "GET_COLLAB_ARTIST",
    SHARE_ARTIST: "SHARE_ARTIST",
    GET_ALL_ARTIST_CMS: "GET_ALL_ARTIST_CMS",
    GET_ALL_ARTIST_SONGS_CMS: "GET_ALL_ARTIST_SONGS_CMS",
    GET_ALL_ARTIST_ALBUMS_CMS: "GET_ALL_ARTIST_ALBUMS_CMS",
    DELETE_ARTIST_CMS: "DELETE_ARTIST_CMS",
    GET_ARTIST_DETAIL_CMS: "GET_ARTIST_DETAIL_CMS",
  },
  SEARCH: {
    GET_TOP_SEARCH_KEYWORDS: "GET_TOP_SEARCH_KEYWORDS",
    GET_SEARCH_ALL_VIEW_SONGS: "GET_SEARCH_ALL_VIEW_SONGS",
    GET_SEARCH_SONGS: "GET_SEARCH_SONGS",
    GET_SEARCH_ALL_VIEW_ARTISTS: "GET_SEARCH_ALL_VIEW_ARTISTS",
    GET_SEARCH_ARTISTS: "GET_SEARCH_ARTISTS",
    GET_SEARCH_ALL_VIEW_PLAYLIST: "GET_SEARCH_ALL_VIEW_PLAYLIST",
    GET_SEARCH_PLAYLISTS: "GET_SEARCH_PLAYLISTS",
    GET_SEARCH_ALL_VIEW_ALBUM: "GET_SEARCH_ALL_VIEW_ALBUM",
    GET_SEARCH_ALBUMS: "GET_SEARCH_ALBUMS",
    GET_SEARCH_ALL_VIEW_YOUTUBE: "GET_SEARCH_ALL_VIEW_YOUTUBE",
    GET_SEARCH_YOUTUBE: "GET_SEARCH_YOUTUBE",
    GET_SEARCH_ALL_VIEW_YOUTUBE_PLAYLIST:
      "GET_SEARCH_ALL_VIEW_YOUTUBE_PLAYLIST",
    GET_SEARCH_YOUTUBE_PLAYLIST: "GET_SEARCH_YOUTUBE_PLAYLIST",
    GET_SEARCH_SUGGESTIONS: "GET_SEARCH_SUGGESTIONS",
  },
  RECENT: {
    GET_RECENT_SONGS: "GET_RECENT_SONGS",
    GET_RECENT_PLAYLISTS: "GET_RECENT_PLAYLISTS",
    GET_RECENT_ALBUMS: "GET_RECENT_ALBUMS",
    GET_RECENT_YOUTUBE: "GET_RECENT_YOUTUBE",
  },
  STATISTICS: {
    GET_TOTAL: "GET_TOTAL",
    GET_SONG_LISTENS: "GET_SONG_LISTENS",
    GET_PLAYLIST_LISTENS: "GET_PLAYLIST_LISTENS",
    GET_SYSTEM_LISTENS: "GET_SYSTEM_LISTENS",
    GET_SYSTEM_VISITS: "GET_SYSTEM_VISITS",
  },
};

export default QUERY_KEY;
