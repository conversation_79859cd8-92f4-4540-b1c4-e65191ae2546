FROM node:20-alpine AS deps

WORKDIR /app

COPY package.json yarn.lock ./

RUN yarn install --frozen-lockfile

FROM node:20-alpine AS builder

WORKDIR /app

COPY --from=deps /app/node_modules ./node_modules

COPY . .

COPY .env.development .env.production

RUN npx vite build

FROM nginx:alpine

RUN rm -rf /usr/share/nginx/html/*

COPY --from=builder /app/dist /usr/share/nginx/html

COPY --from=builder /app/nginx/nginx.conf /etc/nginx/conf.d/default.conf

COPY ./app/android/assetlinks.json /home/<USER>
COPY ./app/ios/ios.json /home/<USER>

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
