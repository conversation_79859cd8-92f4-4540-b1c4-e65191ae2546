import IconCancel from "@components/Icon/IconCancel";
import {Modal, Box, Button, Tab} from "@mui/material";
import TabContext from "@mui/lab/TabContext";
import TabList from "@mui/lab/TabList";
import TabPanel from "@mui/lab/TabPanel";
import {useState} from "react";
import {getPackageInfo, getStatusInfo} from "src/utils/global";
import {convertDateTime} from "src/utils/timeUtils";
import IconStar from "@components/Icon/IconStar";
import CmsTable from "@pages/Cms/components/CmsTable";
import {GridColDef} from "@mui/x-data-grid";
import {useTranslation} from "react-i18next";

interface AccountDetailModalProps {
  open: boolean;
  onClose: () => void;
  userData: any;
}

export default function AccountDetailModal({
  open,
  onClose,
  userData,
}: AccountDetailModalProps) {
  const {t} = useTranslation();
  const [page, setPage] = useState(0);
  const [pageSize] = useState(10);
  const [tabIndex, setTabIndex] = useState("1");
  const {colorStatus, textStatus} = getStatusInfo(userData?.status);
  const {color, text} = getPackageInfo(userData?.premiumType);

  if (!userData) return null;

  const tableRows = userData?.listDevice?.map((item: any, index: number) => ({
    id: index, // Using to get id to handle onclick
    ids: index + 1, // Using to show in the table
    device: item?.device,
    lastLoggedIn: item?.lastLoggedIn,
  }));

  const tableRows2 = userData?.statusHistory?.map(
    (item: any, index: number) => ({
      id: index, // Using to get id to handle onclick
      ids: index + 1, // Using to show in the table
      device: item?.device,
      executionTime: item?.executionTime,
      status: item?.status,
    }),
  );

  const columns: GridColDef[] = [
    {
      field: "ids",
      headerName: t("common.id"),
      width: 46,
      sortable: false,
      disableColumnMenu: true,
      headerAlign: "center",
      align: "center",
      renderCell: (params) => (
        <span className="font-normal">{params?.value}</span>
      ),
    },
    {
      field: "device",
      headerName: t("cms.premium.device"),
      flex: 1,
      minWidth: 150,
      sortable: false,
      renderCell: (params) => (
        <span className="font-normal">{params?.value ?? "-"}</span>
      ),
    },
    {
      field: "lastLoggedIn",
      headerName: t("cms.premium.last_logged_in"),
      flex: 1,
      minWidth: 150,
      sortable: false,
      renderCell: (params) => (
        <span>{convertDateTime(params?.value) ?? "-"}</span>
      ),
    },
  ];

  const columns2: GridColDef[] = [
    {
      field: "ids",
      headerName: "ID",
      width: 46,
      sortable: false,
      disableColumnMenu: true,
      headerAlign: "center",
      align: "center",
      renderCell: (params) => (
        <span className="font-normal">{params?.value ?? "-"}</span>
      ),
    },
    {
      field: "device",
      headerName: t("cms.premium.device"),
      flex: 1,
      minWidth: 150,
      sortable: false,
      renderCell: (params) => (
        <span className="font-normal">{params?.value ?? "-"}</span>
      ),
    },
    {
      field: "status",
      headerName: t("common.account_status"),
      flex: 1,
      minWidth: 150,
      sortable: true,
      renderCell: (params) => {
        const {colorStatus, textStatus} = getStatusInfo(params?.value);

        return (
          <div className="font-semibold">
            <span
              style={{color: colorStatus}}
              className="flex gap-1 items-center"
            >
              <span
                className="rounded-full w-1.5 aspect-square inline-block"
                style={{
                  backgroundColor: colorStatus,
                }}
              />
              &ensp;{textStatus}
            </span>
          </div>
        );
      },
    },
    {
      field: "executionTime",
      headerName: t("cms.premium.execution_time"),
      flex: 1,
      minWidth: 150,
      sortable: false,
      renderCell: (params) => (
        <span>{convertDateTime(params?.value) ?? "-"}</span>
      ),
    },
  ];

  return (
    <Modal open={open} onClose={onClose} aria-labelledby="user-modal-title">
      <Box
        sx={{
          position: "absolute",
          top: "50%",
          left: "50%",
          transform: "translate(-50%, -50%)",
          width: "90%",
          maxWidth: "1300px",
          bgcolor: "background.paper",
          boxShadow: 24,
          borderRadius: 2,
          px: 3,
          py: 2,
        }}
      >
        <div className="flex items-center border-b">
          <span className="w-full flex font-bold text-base pb-3">
            {t("cms.premium.premium_account_information")}
          </span>
          <button onClick={onClose}>
            <IconCancel />
          </button>
        </div>

        <TabContext value={tabIndex}>
          <Box sx={{borderBottom: 1, borderColor: "divider"}}>
            <TabList
              onChange={(_, newValue) => setTabIndex(newValue)}
              sx={{
                "px": 0,
                "pt": 3,
                "& .MuiTabs-indicator": {
                  backgroundColor: "#FF4319",
                },
                "& .MuiTab-root": {
                  textTransform: "none",
                },
                "& .Mui-selected": {
                  color: "#FF4319 !important",
                  fontWeight: "semibold",
                },
              }}
            >
              <Tab
                label={t("cms.premium.general_information")}
                value="1"
                sx={{px: 0}}
              />
              <Tab
                label={t("cms.premium.list_logged_in_devices")}
                value="2"
                sx={{marginLeft: 2, px: 0}}
              />
              <Tab
                label={t("cms.premium.history_status")}
                value="3"
                sx={{marginLeft: 2, px: 0}}
              />
            </TabList>
          </Box>

          <TabPanel value="1" sx={{px: 0}}>
            <div className="flex flex-col gap-5 p-4 bg-[#F6F6F6] text-sm border border-[#E5E5E5] rounded-xl">
              <div className="flex flex-row max-w-[266px] justify-between">
                <div className="text-[#656565]">
                  {t("cms.premium.cover_image")}
                </div>
                <img
                  alt="anh"
                  src="/image/default-music.png"
                  className="w-20 h-20 object-cover rounded-sm"
                />
              </div>
              <div className="flex flex-col md:flex-row text-[#656565] gap-2 md:gap-0">
                <div className="w-full md:w-[45%]">
                  <div className="grid grid-cols-2 gap-2 max-w-sm rounded-lg">
                    <p className="font-normal">{t("common.account")}</p>
                    <p className="font-semibold">{userData?.username}</p>

                    <p className="font-normal">{t("common.email")}</p>
                    <p className="font-semibold">{userData?.email}</p>

                    <p className="font-normal">{t("common.account_status")}</p>
                    <p
                      className="flex items-center font-semibold"
                      style={{color: colorStatus}}
                    >
                      <span
                        className="w-2 h-2 rounded-full mr-2"
                        style={{background: colorStatus}}
                      ></span>
                      {textStatus}
                    </p>
                  </div>
                </div>
                <div className="w-full md:w-[55%]">
                  <div className="grid grid-cols-2 gap-2 max-w-sm rounded-lg items-center">
                    <p className="font-normal">
                      {t("cms.premium.package_type")}
                    </p>
                    <p className="flex items-center gap-1 font-normal leading-none">
                      <IconStar color={color} />
                      {text}
                    </p>

                    <p className="font-normal">
                      {t("cms.premium.extension_period")}
                    </p>
                    <p className="!text-[#242728] font-semibold">
                      {convertDateTime(userData?.extensionPeriod)}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </TabPanel>

          <TabPanel value="2" sx={{px: 0}}>
            <CmsTable
              rows={tableRows}
              columns={columns}
              currentPage={page || 0}
              onPageChange={(page) => setPage(page)}
              rowsPerPage={pageSize || 10}
            />
          </TabPanel>

          <TabPanel value="3" sx={{px: 0}}>
            <CmsTable
              rows={tableRows2}
              columns={columns2}
              currentPage={page || 0}
              onPageChange={(page) => setPage(page)}
              rowsPerPage={pageSize || 10}
            />
          </TabPanel>
        </TabContext>

        <div className="w-full flex justify-end border-t">
          <Button
            sx={{
              mt: 2,
              bgcolor: "#F2F2F3",
              color: "#242728",
              textTransform: "capitalize",
              fontWeight: 600,
              border: "1px solid #DCDEE0",
            }}
            onClick={onClose}
            className="flex gap-2 items-center justify-center"
          >
            <IconCancel />
            {t("common.close")}
          </Button>
        </div>
      </Box>
    </Modal>
  );
}
