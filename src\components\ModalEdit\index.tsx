import IconCancel from "@components/Icon/IconCancel";
import IconEdit from "@components/Icon/IconEdit";
import CloseIcon from "@mui/icons-material/Close";
import {Dialog, DialogContent, DialogTitle, IconButton} from "@mui/material";
import React from "react";

interface EditModalProps {
  open: boolean;
  title?: string;
  children?: React.ReactNode;
  onEdit: () => void;
  onCancel: () => void;
}

export default function ModalEdit({
  open,
  title = "Chỉnh sửa bài hát",
  children,
  onEdit,
  onCancel,
}: EditModalProps) {
  return (
    <Dialog
      open={open}
      onClose={onCancel}
      maxWidth="md"
      fullWidth
      classes={{paper: "rounded-lg"}}
    >
      <div className="flex items-center justify-between border-b border-gray-300 pr-4">
        <DialogTitle className="p-0 text-base font-bold text-gray-800">
          {title}
        </DialogTitle>
        <IconButton
          aria-label="close"
          onClick={onCancel}
          className="text-gray-500 hover:text-gray-800"
          size="small"
        >
          <CloseIcon />
        </IconButton>
      </div>

      <DialogContent className="px-6 py-4">{children}</DialogContent>

      <div className="flex justify-between border-t border-gray-300 px-4 py-2">
        <div
          onClick={onCancel}
          className="flex gap-2 items-center rounded-lg px-4 py-2 border text-base bg-gray-200 text-gray-default hover:bg-gray-300"
        >
          <IconCancel />
          Đóng
        </div>
        <div
          onClick={onEdit}
          className="flex gap-2 items-center rounded-lg bg-orange-500 px-4 py-2 text-base text-white hover:bg-red-600"
        >
          <IconEdit />
          Chỉnh sửa
        </div>
      </div>
    </Dialog>
  );
}
