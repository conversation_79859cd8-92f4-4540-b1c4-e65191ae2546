import "./index.scss";
import {useTranslation} from "react-i18next";
import {useSelector} from "react-redux";
import {IRootState} from "@redux/store";
import {useGender} from "src/utils/global";
import HeaderTitle from "@components/HeaderTitle";

export default function Profile() {
  const {t} = useTranslation();
  const genderOptions = useGender();
  const {userInfo} = useSelector((state: IRootState) => state.user);

  const userValues = {
    fullName: `${userInfo?.firstName ?? ""} ${userInfo?.lastName ?? ""}`,
    email: userInfo?.email?.find((item) => item.primary)?.email || "",
    countryName: userInfo?.countryName || "",
    dateOfBirth: userInfo?.dateOfBirth || "",
    gender: userInfo?.gender || "",
  };

  return (
    <div className="flex flex-col justify-center items-center gap-2 text-[#FFFFFF] mb-[8vh]">
      <HeaderTitle title={t("common.profile")} />
      <div className="flex flex-col items-center gap-4 max-w-[200px]">
        <img
          src={userInfo?.avatar || "/image/default-avatar.png"}
          alt="avatar"
          className="w-20 h-20 rounded-full object-cover"
        />
        <div className="font-semibold text-xl">{userInfo?.username}</div>
      </div>
      <div className="w-md text-xs w-[90%] min-[450px]:w-[400px]">
        <div className="space-y-4">
          <div className="flex flex-col gap-1.5 relative">
            <label htmlFor="displayName">{t("common.display_name")}</label>
            <div className="custom-input rounded-lg">{userValues.fullName}</div>
          </div>

          <div className="flex flex-col gap-1.5 relative">
            <label htmlFor="email">{t("common.email")}</label>
            <div className="custom-input rounded-lg">{userValues.email}</div>
          </div>

          <div className="flex flex-col gap-1.5 relative">
            <label htmlFor="country">{t("common.country")}</label>
            <div
              className="custom-input rounded-lg"
              style={{
                color: "#ffffff",
              }}
            >
              {userValues.countryName}
            </div>
          </div>

          <div className="flex flex-col gap-1.5 relative">
            <label htmlFor="dateOfBirth">{t("common.birthday")}</label>
            <div className="custom-input rounded-lg">
              {userValues.dateOfBirth}
            </div>
          </div>

          <div className="flex flex-col gap-1.5 relative">
            <label htmlFor="gender">{t("common.gender")}</label>
            <div
              className="custom-input rounded-lg"
              style={{
                color: "#ffffff",
              }}
            >
              {genderOptions.find((opt) => opt.value === userValues.gender)
                ?.label || ""}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
