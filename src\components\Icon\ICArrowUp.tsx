import {SVGProps} from "react";

interface IconArrowUpProps extends SVGProps<SVGSVGElement> {
  color?: string;
}
const ICArrowUp = ({color, ...props}: IconArrowUpProps) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    className="w-4 h-4"
    viewBox="0 0 24 24"
    fill="none"
    {...props}
  >
    <path
      fill={color || "#30BD88"}
      d="M19.707 10.707a.997.997 0 0 1-1.414 0L13 5.414V21a1 1 0 1 1-2 0V5.414l-5.293 5.293a.999.999 0 1 1-1.414-1.414l7-7a1 1 0 0 1 1.415 0l7 7a.999.999 0 0 1 0 1.414Z"
    />
  </svg>
);
export default ICArrowUp;
