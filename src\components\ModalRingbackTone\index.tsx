import {Button, Dialog} from "@mui/material";
import {useTranslation} from "react-i18next";

interface ModalRingbackToneProps {
  open: boolean;
  onCancel: () => void;
}

export default function ModalRingbackTone({
  open,
  onCancel,
}: ModalRingbackToneProps) {
  const {t} = useTranslation();

  const handleRegister = () => {
    onCancel();
  };

  return (
    <Dialog
      open={open}
      onClose={onCancel}
      maxWidth="md"
      fullWidth
      classes={{paper: "rounded-lg"}}
      PaperProps={{
        style: {
          background: "#1C1717",
          color: "white",
          boxSizing: "border-box",
          gap: "4px",
          padding: "32px 32px",
          borderRadius: "16px",
          width: "444px",
          boxShadow: "0px_0px_32px_0px_#FFFFFF30",
        },
      }}
    >
      <div className="space-y-6">
        <h1 className="text-xl font-bold text-center">
          {t("services.ringback_tone.register_title")}
        </h1>
        <p className="text-center text-[#E3E3E3]">
          {t("services.ringback_tone.confirm")}
        </p>
        <Button
          className="!bg-orange-500 !py-2.5 !px-8 !rounded-[10px] !text-[#FBFDFF] !font-bold w-full !capitalize p-5 !text-[16px]"
          onClick={handleRegister}
        >
          {t("common.register")}
        </Button>
      </div>
    </Dialog>
  );
}
