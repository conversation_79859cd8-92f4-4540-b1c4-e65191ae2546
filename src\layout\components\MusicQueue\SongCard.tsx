import ApiSong from "@api/ApiSong";
import LikeButton from "@components/AuthButton/LikeButton";
import IconAdd24px from "@components/Icon/IconAdd24px";
import IconMoreHorizontal from "@components/Icon/IconMoreHorizontal";
import IconPlay from "@components/Icon/IconPlay";
import ModalAddToPlaylist from "@components/ModalAddToPlaylist";
import PopupMenu from "@components/PopupMenu";
import {IconButton} from "@mui/material";
import {
  removeSongFromQueue,
  updateCurrentSong,
} from "@redux/slices/PlayerSlice";
import {IRootState} from "@redux/store";
import {useMutation} from "@tanstack/react-query";
import clsx from "clsx";
import {useState} from "react";
import {useTranslation} from "react-i18next";
import {useDispatch, useSelector} from "react-redux";
import {useNavigate} from "react-router-dom";
import {toast} from "react-toastify";
import {ESongType, ISong} from "src/types";
import "./index.scss";
import {DeleteOutlineRounded} from "@mui/icons-material";
import ModalShare from "@components/ModalShare";
import IconShare from "@components/Icon/IconShare";
import {generateShareLink} from "src/utils/global";
import PlayerUtil from "src/core/PlayerUtil";
import {handleLikeSong} from "src/utils/like";
import {useLayout} from "src/utils/hooks";

interface ISongCardProps {
  index: number;
  data: ISong;
  className?: string;
  isPlay?: boolean;
}

export default function SongCard({
  data,
  className,
  index,
  isPlay,
}: ISongCardProps): JSX.Element {
  const {t} = useTranslation();
  const navigate = useNavigate();
  const {isMobile} = useLayout();
  const paused = useSelector((state: IRootState) => state.player?.paused);
  const playlistUrl = data?.playlists?.[0]?.urlSlug || "";
  const hasPlaylist = (data?.playlists?.length ?? 0) > 0;
  const dispatch = useDispatch();
  const [addToPlaylistOpen, setAddToPlaylistOpen] = useState(false);
  const [openModalShare, setOpenModalShare] = useState<boolean>(false);
  const [isFocused, setIsFocused] = useState(false);
  const [menuAnchor, setMenuAnchor] = useState<null | HTMLElement>(null);
  const shareMutate = useMutation({
    mutationFn: ApiSong.shareSong,
    onSuccess: () => {
      const link = generateShareLink({type: "song", data});
      navigator.clipboard.writeText(link).then(() => {
        toast.success(t("common.copy_link_success"));
      });
    },
    onError: () => {
      toast.error(t("common.copy_link_failed"));
    },
  });

  const handleOpenMenu = (event: React.MouseEvent<HTMLButtonElement>) => {
    event.stopPropagation();
    setIsFocused(true);
    setMenuAnchor(event?.currentTarget);
  };

  const handleCloseMenu = () => {
    setIsFocused(false);
    setMenuAnchor(null);
  };

  const goToDetailPlaylist = () => {
    if (!hasPlaylist) return;
    navigate(`/playlist/${playlistUrl}`);
  };

  const openAddToPlaylist = () => {
    setAddToPlaylistOpen(true);
  };

  const handleDeleteSongFromQueue = () => {
    if (data) {
      dispatch(removeSongFromQueue(data?.id ?? ""));
      if (PlayerUtil.instance.isSongPlaying(data)) {
        PlayerUtil.instance.clear();
      }
      toast.success(`${t("common.delete_successfully")}`);
    }
  };

  const handleOpenModalShare = () => {
    setOpenModalShare(true);
  };

  const handleCloseModalShare = () => {
    setOpenModalShare(false);
  };

  const handleCopyLink = () => {
    shareMutate.mutateAsync(data?.id ?? "");
  };

  return (
    <>
      <div
        className={clsx(
          "song-queue-item group flex items-center w-full px-2.5 justify-between py-2 md:py-5 cursor-pointer select-none",
          className,
          isFocused && "focused",
        )}
        onClick={(e) => {
          if (
            (isMobile && e.target !== menuAnchor) ||
            e.target === e.currentTarget
          ) {
            if (isPlay) {
              if (paused) {
                PlayerUtil.instance.play();
              } else {
                PlayerUtil.instance.pause();
              }
            } else {
              if (!menuAnchor) {
                dispatch(updateCurrentSong(data));
              }
            }
            e.stopPropagation();
          }
        }}
        key={index}
      >
        <div className="flex items-center h-fit gap-4 flex-1 mr-2.5 w-full">
          <div className="relative min-w-10 min-h-10 h-10">
            <img
              src={
                data?.images?.SMALL ||
                data?.images?.DEFAULT ||
                "./image/default-music.png"
              }
              className="w-full h-full rounded object-cover"
            />

            {isPlay && (
              <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-full aspect-square flex z-20 justify-center items-center bg-[rgba(0,0,0,0.5)]">
                {paused ? (
                  <IconPlay onClick={() => PlayerUtil.instance.play()} />
                ) : (
                  <img
                    src="/image/animation_play.gif"
                    style={{filter: "grayscale(100%) brightness(0) invert(1)"}}
                    onClick={() => PlayerUtil.instance.pause()}
                  />
                )}
              </div>
            )}
            {!isPlay && !isMobile && (
              <div
                className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-full aspect-square hidden z-20 group-hover:flex justify-center items-center"
                onClick={() => dispatch(updateCurrentSong(data))}
              >
                <IconPlay />
              </div>
            )}
            <div className="avatar-mask absolute top-0 left-0 w-10 h-10 rounded bg-[rgba(0,0,0,0.5)]"></div>
          </div>
          <div className="flex flex-col gap-y-1 justify-center">
            <div
              className={clsx(
                "text-sm font-semibold line-clamp-1 overflow-hidden text-ellipsis whitespace-normal",
                isPlay && "text-[#FF4319]",
                data.type !== ESongType.YOUTUBE && "md:hover:text-sky-500",
              )}
              onClick={(e) => {
                if (isMobile || data.type === ESongType.YOUTUBE) return;
                e.stopPropagation();
                goToDetailPlaylist();
              }}
            >
              {data?.name}
            </div>
            <div
              className={clsx(
                "text-xs text-[rgba(255,255,255,0.5)] w-48 overflow-hidden text-ellipsis whitespace-nowrap",
              )}
            >
              {(data?.artists?.length ?? 0) > 0
                ? data?.artists?.map((artist, index) => (
                    <span key={artist.id}>
                      <span
                        onClick={(e) => {
                          if (isMobile || data.type === ESongType.YOUTUBE)
                            return;
                          e.stopPropagation();
                          navigate(`/artist/${artist.urlSlug || "unknown"}`);
                        }}
                        className={clsx(
                          "cursor-pointer",
                          data.type !== ESongType.YOUTUBE &&
                            "md:hover:text-sky-500 md:hover:underline",
                        )}
                      >
                        {artist?.stageName ?? artist?.name}
                      </span>
                      {index < (data?.artists?.length ?? 0) - 1 && ", "}
                    </span>
                  ))
                : t("common.not_info_artist")}
            </div>
          </div>
        </div>
        <div
          className="flex items-center flex-1 justify-end"
          onClick={(e) => {
            e.stopPropagation();
          }}
        >
          <LikeButton
            className="song-action md:p-1 hover:rounded-full"
            action={() => handleLikeSong(data)}
            isLiked={data?.isLiked}
            songId={data?.id}
          />
          <IconButton className="song-action !p-1" onClick={handleOpenMenu}>
            <IconMoreHorizontal />
          </IconButton>
        </div>
        <PopupMenu
          data={data}
          menuArray={[
            {
              icon: <IconAdd24px />,
              label: t("common.menu.add_to_playlist"),
              action: openAddToPlaylist,
              isAuth: true,
            },
            {
              icon: <DeleteOutlineRounded />,
              label: t("common.delete"),
              action: handleDeleteSongFromQueue,
            },
          ].concat(
            data.type === ESongType.SONG
              ? {
                  icon: <IconShare width={22} height={22} />,
                  label: t("common.share"),
                  action: handleOpenModalShare,
                }
              : [],
          )}
          anchorEl={menuAnchor}
          open={Boolean(menuAnchor)}
          onClose={handleCloseMenu}
        />
        <ModalAddToPlaylist
          open={addToPlaylistOpen}
          onClose={() => setAddToPlaylistOpen(false)}
          songData={data}
        />
        <ModalShare
          open={openModalShare}
          onCancel={handleCloseModalShare}
          handleCopyLink={handleCopyLink}
          image={data?.images?.SMALL || data?.images?.DEFAULT}
          name={data?.name}
          artists={data?.artists}
          shareUrl={generateShareLink({type: "song", data})}
        />
      </div>
      <div className="w-full h-[1px] bg-[#FFFFFF12]" />
    </>
  );
}
