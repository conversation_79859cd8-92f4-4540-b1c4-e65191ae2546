import {Box, Pagination, TablePagination} from "@mui/material";
import {DataGrid, DataGridProps, GridColDef} from "@mui/x-data-grid";
import clsx from "clsx";
import {useEffect, useMemo} from "react";
import {useTranslation} from "react-i18next";

interface CmsTableProps extends DataGridProps {
  className?: string;
  currentPage: number;
  totalItems?: number;
  rowsPerPage: number;
  ordinalColumn?: boolean;
  onPageChange: (page: number) => void;
  onRowsPerPageChange?: (rowsPerPage: number) => void;
  filterValues?: Record<string, any>;
}

export default function CmsTable({
  columns,
  className,
  totalItems,
  currentPage,
  ordinalColumn,
  rowsPerPage,
  onPageChange,
  onRowsPerPageChange,
  filterValues,
  ...otherProps
}: CmsTableProps) {
  const {t} = useTranslation();
  const dataColumns: readonly GridColDef[] = useMemo(() => {
    if (ordinalColumn) {
      return [
        {
          field: "stt",
          headerName: t("cms.ordinal"),
          width: 60,
          sortable: false,
          disableColumnMenu: true,
          cellClassName: "line-clamp-3",
          align: "center",
          headerAlign: "center",
          renderCell: (tableParams) => {
            return (
              <div>
                {(currentPage || 0) * 10 +
                  tableParams.api.getRowIndexRelativeToVisibleRows(
                    tableParams.row.id,
                  ) +
                  1}
              </div>
            );
          },
        },
        ...columns,
      ];
    }
    return columns;
  }, [columns]);

  useEffect(() => {
    onPageChange(0);
  }, [filterValues]);

  return (
    <div className={clsx("flex flex-col w-full", className)}>
      <DataGrid
        columns={dataColumns}
        sx={{
          "&.MuiDataGrid-root--densityCompact .MuiDataGrid-cell": {py: "8px"},
          "&.MuiDataGrid-root--densityStandard .MuiDataGrid-cell": {py: "15px"},
          "&.MuiDataGrid-root--densityComfortable .MuiDataGrid-cell": {
            py: "22px",
          },
          "overflowX": "auto",
          "border": 0,
          "& .MuiDataGrid-scrollbar.MuiDataGrid-scrollbar--horizontal": {
            height: "8px",
            backgroundColor: "#f0f0f0",
            borderRadius: "4px",
          },
          "& .MuiDataGrid-scrollbar.MuiDataGrid-scrollbar--horizontal::-webkit-scrollbar":
            {
              height: "8px",
            },
          "& .MuiDataGrid-scrollbar.MuiDataGrid-scrollbar--horizontal::-webkit-scrollbar-thumb":
            {
              backgroundColor: "#b3b3b3",
              borderRadius: "4px",
              minWidth: "150px",
              width: "150px",
            },
          "& .MuiDataGrid-scrollbar.MuiDataGrid-scrollbar--horizontal::-webkit-scrollbar-thumb:hover":
            {
              backgroundColor: "#999",
            },
          "& .MuiDataGrid-scrollbar.MuiDataGrid-scrollbar--horizontal::-webkit-scrollbar-track":
            {
              backgroundColor: "#e6e6e6",
            },
          "& .MuiDataGrid-columnHeaderTitle": {
            whiteSpace: "normal",
            lineHeight: "normal",
            wordWrap: "break-word",
            textOverflow: "clip",
            marginLeft: "2px",
          },
          "& .MuiDataGrid-columnHeader": {
            backgroundColor: "#F2F2F3",
            fontWeight: "bold",
            fontSize: "14px",
            color: "#242728",
          },

          "& .MuiDataGrid-cell": {
            outline: "none",
            whiteSpace: "normal",
            lineHeight: "1.3rem",
            wordWrap: "break-word",
            textOverflow: "clip",
            alignContent: "center",
          },
          "& .MuiDataGrid-cell--textLeft": {
            paddingLeft: "12px",
          },
          "& .MuiDataGrid-footerContainer": {
            borderTop: "none",
          },
          "& .MuiDataGrid-root": {
            border: "1px solid #DCDEE0",
            borderRadius: "8px",
          },
          "& .MuiDataGrid-iconButtonContainer": {
            visibility: "visible",
            marginLeft: "2px",
            width: "auto !important",
          },
          "& .MuiDataGrid-sortIcon": {
            opacity: "inherit !important",
          },
          "& .MuiTablePagination-select": {
            backgroundColor: "#F2F2F3",
            border: "1px solid #DCDEE0",
            borderRadius: "6px",
          },
          "&. MuiIconButton-sizeMedium": {
            backgroundColor: "#F2F2F3",
            border: "1px solid #DCDEE0",
            borderRadius: "6px",
          },
          "& .sticky-header": {
            position: "sticky",
            right: 0,
            marginBottom: 0,
            zIndex: 100,
            contain: "strict",
            backgroundColor: "#F2F2F3",
            fontWeight: "bold",
            fontSize: "14px",
            color: "#242728",
          },
          "& .sticky-cell": {
            position: "sticky",
            right: 0,
            backgroundColor: "#fff",
            zIndex: 99,
            outline: "none !important",
          },
          "& .MuiDataGrid-row:hover .sticky-cell": {
            backgroundColor: "#F5F5F5",
          },
          "& .MuiDataGrid-columnSeparator--resizable": {
            zIndex: "100",
          },
          "& .MuiDataGrid-row": {
            cursor: "pointer",
          },
        }}
        classes={{
          columnHeader: "bg-[#F2F2F3]",
          columnHeaderTitle: "!font-bold !text-sm !text-[#242728]",
          cell: "focus:!outline-none",
          main: "border rounded-md border-[#DCDEE0]",
          footerContainer: "!border-t-0",
        }}
        getRowHeight={() => "auto"}
        scrollbarSize={1}
        disableColumnSelector
        disableVirtualization={true}
        disableRowSelectionOnClick
        disableColumnMenu
        sortingMode="server"
        filterMode="server"
        localeText={{
          noRowsLabel: t("common.no_data"),
        }}
        rowHeight={72}
        hideFooterPagination={true}
        {...otherProps}
      />
      <div className="flex items-center justify-between">
        <TablePagination
          count={totalItems || -1}
          page={currentPage}
          onPageChange={(_, page) => onPageChange(page)}
          rowsPerPage={rowsPerPage || 10}
          onRowsPerPageChange={(e) => {
            onRowsPerPageChange?.(Number(e.target.value));
          }}
          labelRowsPerPage={t("common.record_per_page")}
          labelDisplayedRows={({from, to, count}) =>
            `${from} - ${to} ${t("common.out_of")} ${count} ${t("common.record")}`
          }
          component={Box}
          ActionsComponent={() => null}
        />
        <Pagination
          count={Math.ceil((totalItems || 0) / (rowsPerPage || 1))}
          page={currentPage + 1}
          onChange={(_, page) => onPageChange(page - 1)}
        />
      </div>
    </div>
  );
}
