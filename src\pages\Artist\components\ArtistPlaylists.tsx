import ArtistBanner from "./ArtistBanner";
import CommonAlbumCard from "@components/CommonAlbumCard";
import {useParams} from "react-router-dom";
import {useTranslation} from "react-i18next";
import AlbumCardSkeleton from "@components/AlbumCardSkeleton";
import QUERY_KEY from "@api/QueryKey";
import {useInfiniteQuery, useQuery} from "@tanstack/react-query";
import {IPlaylist} from "src/types";
import ApiArtistDetail from "@api/ApiArtistDetail";
import IconNoData from "@components/Icon/IconNoData";
import Subtitle from "@components/Subtitle";
import {useEffect, useRef} from "react";
import {IDataWithMeta} from "@api/Fetcher";
import {Grid} from "@mui/material";
import HeaderTitle from "@components/HeaderTitle";

export default function ArtistPlaylists() {
  const {t} = useTranslation();
  const {urlSlug} = useParams<{urlSlug: string}>();

  const {data: artistDetails, refetch: refetchDetail} = useQuery({
    queryKey: [QUERY_KEY.ARTIST.GET_ARTIST_DETAIL, urlSlug],
    queryFn: () => ApiArtistDetail.getArtistDetail(urlSlug || ""),
    enabled: !!urlSlug,
  });

  const observer = useRef<IntersectionObserver | null>(null);
  const lastElementRef = useRef<HTMLDivElement | null>(null);
  const {
    data: artistPlaylists,
    fetchNextPage,
    isLoading,
    isError,
    hasNextPage,
  } = useInfiniteQuery<IDataWithMeta<IPlaylist[]>, Error>({
    queryKey: [QUERY_KEY.ARTIST.GET_ARTIST_PLAYLISTS, urlSlug],
    queryFn: ({pageParam = 0}) =>
      ApiArtistDetail.getArtistPlaylists(urlSlug || "", {
        pageSize: 15,
        page: pageParam as number,
      }),
    getNextPageParam: (lastPage) =>
      lastPage?.meta?.totalPage - 1 > lastPage?.meta?.currentPage
        ? (lastPage?.meta?.currentPage || 0) + 1
        : undefined,
    initialPageParam: 0,
  });

  useEffect(() => {
    if (isLoading || isError) return;

    if (observer.current) observer.current.disconnect();

    observer.current = new IntersectionObserver((entries) => {
      if (entries[0].isIntersecting && hasNextPage) {
        fetchNextPage();
      }
    });

    if (lastElementRef?.current) {
      observer.current.observe(lastElementRef.current);
    }

    return () => {
      if (observer.current) {
        observer.current.disconnect();
      }
    };
  }, [isLoading, isError, artistPlaylists, hasNextPage]);

  const isEmpty =
    !artistPlaylists ||
    !artistPlaylists.pages ||
    artistPlaylists.pages.every((page) => page.data.length === 0);

  return (
    <div>
      <HeaderTitle title={t("common.playlist")} name={artistDetails?.name} />
      <ArtistBanner data={artistDetails} refetch={refetchDetail} />

      <div className="px-4 md:px-6 sm:ml-6 mt-6 flex flex-col gap-5">
        <Subtitle
          subtitle={`${artistDetails?.name} & ${t("common.playlist")}`}
          seeMore={false}
        />
        {isLoading && !artistPlaylists && (
          <Grid
            container
            spacing={{xs: 2, md: 3}}
            columns={{xs: 2, sm: 6, md: 12, lg: 15}}
          >
            {[...Array(5)].map((_, index) => (
              <Grid item xs={1} sm={2} md={3} lg={3} key={index}>
                <AlbumCardSkeleton isMultipleInfo={false} />
              </Grid>
            ))}
          </Grid>
        )}
        {!isLoading && (isError || isEmpty) && (
          <div className=" flex justify-center items-center flex-col gap-2.5">
            <IconNoData />
            <span className="text-[#E3E3E3] w-full text-base font-normal flex justify-center">
              {t("common.playlist_list_not_found")}
            </span>
          </div>
        )}
        <div>
          <Grid
            container
            spacing={{xs: 2, md: 3}}
            columns={{xs: 2, sm: 6, md: 12, lg: 15}}
          >
            {artistPlaylists?.pages?.map((page) =>
              page?.data?.map((playlist, index) => (
                <Grid item xs={1} sm={2} md={3} lg={3} key={index}>
                  <CommonAlbumCard data={playlist} haveLayer={false} />
                </Grid>
              )),
            )}
          </Grid>
          {hasNextPage && (
            <Grid
              container
              spacing={{xs: 2, md: 3}}
              columns={{xs: 2, sm: 6, md: 12, lg: 15}}
            >
              {[...Array(5)].map((_, index) => (
                <Grid item xs={1} sm={2} md={3} lg={3} key={index}>
                  <AlbumCardSkeleton isMultipleInfo={false} />
                </Grid>
              ))}
            </Grid>
          )}
        </div>
        {!isLoading && <div ref={lastElementRef} className="h-1"></div>}
      </div>
    </div>
  );
}
