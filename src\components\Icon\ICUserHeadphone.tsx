import {SVGProps} from "react";
const ICUserHeadphone = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={20}
    height={21}
    fill="none"
    {...props}
  >
    <path
      fill="#3C82F6"
      d="M20 16.5a9.05 9.05 0 0 1-.272 2.138c-.004.016-.003.03-.007.045-.008.034-.02.066-.028.099-.013.05-.029.098-.042.148-.044.158-.085.317-.135.47l-.217.704a1.273 1.273 0 0 1-1.584.84l-.446-.136a.803.803 0 0 1-.478-.394.8.8 0 0 1-.058-.615l.685-2.24a.805.805 0 0 1 1.01-.536l.434.133c.146.045.277.117.392.205.032-.29.054-.58.054-.861 0-2.1-1.709-3.808-3.808-3.808a3.812 3.812 0 0 0-3.808 3.808c0 .281.021.57.054.86.115-.087.246-.16.392-.204l.435-.133a.8.8 0 0 1 .616.058c.19.101.33.27.393.477l.685 2.241a.804.804 0 0 1-.536 1.009l-.445.137a1.273 1.273 0 0 1-1.584-.84l-.219-.708c-.05-.15-.092-.313-.136-.473-.012-.048-.028-.093-.04-.142-.009-.034-.02-.064-.028-.098-.004-.015-.003-.03-.007-.045A9.047 9.047 0 0 1 11 16.5c0-2.481 2.019-4.5 4.5-4.5s4.5 2.018 4.5 4.5Z"
    />
    <path
      fill="#3C82F6"
      d="M3.508 4c0-2.206 1.794-4 4-4s4 1.794 4 4-1.794 4-4 4-4-1.794-4-4ZM9.5 16.143c0-.784.147-1.699.69-2.643.41-.713 1.31-1.5 2.548-2.045.227-.095.27-.393.067-.533C11.978 10.353 10.894 10 9.5 10h-4C1.44 10 0 12.97 0 15.52 0 17.8 1.21 19 3.5 19h6.39c.17 0 .3-.13.3-.3a.377.377 0 0 0-.1-.22c-.281-.35-.59-1.337-.59-2.337Z"
    />
  </svg>
);
export default ICUserHeadphone;
