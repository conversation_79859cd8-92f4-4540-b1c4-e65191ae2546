import {<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>lider} from "@mui/material";
import clsx from "clsx";
import {useRef, useState} from "react";
import Cropper, {ReactCropperElement} from "react-cropper";
import "cropperjs/dist/cropper.css";
import IconClose from "@components/Icon/IconClose";
import IconAdd from "@components/Icon/IconAdd";
import IconSubtract from "@components/Icon/IconSubtract";
import IconRotate from "@components/Icon/IconRotate";
import "./index.scss";
import {t} from "i18next";

interface IImageMeta {
  size: number;
  width: number;
  height: number;
}

interface ImageCropperProps {
  initialImageUrl?: string;
  renderActionButton?: (
    croppedImageUrl: string,
    handleImageChange: (event: React.ChangeEvent<HTMLInputElement>) => void,
  ) => JSX.Element;
  onChange: (croppedImage?: File) => void;
  aspectRatio?: number;
  className?: string;
  freeCropMode?: boolean;
}

export default function ImageCropper({
  initialImageUrl,
  aspectRatio = 1 / 1,
  className,
  renderActionButton,
  onChange,
  freeCropMode = false,
}: ImageCropperProps) {
  const cropperRef = useRef<ReactCropperElement>(null);
  const inputUploadRef = useRef<HTMLInputElement>(null);

  const [loading, setLoading] = useState(false);
  const [open, setOpen] = useState(false);
  const [imageUrl, setImageUrl] = useState("");
  const [croppedImageUrl, setCroppedImageUrl] = useState(initialImageUrl || "");
  const [zoomValue, setZoomValue] = useState(1);
  const [rotateValue, setRotateValue] = useState(0);
  const [imageMeta, setImageMeta] = useState<IImageMeta>({
    size: 0,
    width: 0,
    height: 0,
  });

  const onClose = () => setOpen(false);

  const onCrop = () => {
    setLoading(true);
    const canvas = cropperRef.current?.cropper.getCroppedCanvas();
    if (canvas) {
      const url = canvas.toDataURL();
      setCroppedImageUrl(url);
      canvas.toBlob((blob) => {
        if (blob) {
          const file = new File([blob], "image.png", {type: blob?.type});
          onChange(file);
        }
        setLoading(false);
        onClose();
      });
    }
  };

  const handleZoom = (value: number) => {
    setZoomValue(value);
    if (cropperRef.current?.cropper) {
      cropperRef.current.cropper.zoomTo(value);
      centerImage();
    }
  };

  const handleQuickZoom = (increase: boolean) => {
    const step = 0.1;
    const newValue = increase
      ? Math.min(3, zoomValue + step)
      : Math.max(0.1, zoomValue - step);
    handleZoom(newValue);
  };

  const handleRotate = (value: number) => {
    const newValue = Math.min(180, Math.max(-180, value));

    setRotateValue(newValue);
    if (cropperRef.current?.cropper) {
      cropperRef.current.cropper.rotateTo(newValue);
      centerImage();
    }
  };

  const handleQuickRotate = (degrees: number) => {
    const targetRotation = rotateValue + degrees;
    handleRotate(targetRotation);
  };

  const centerImage = () => {
    const cropper = cropperRef.current?.cropper;
    if (!cropper) return;

    const containerData = cropper.getContainerData();
    const canvasData = cropper.getCanvasData();

    const left = (containerData.width - canvasData.width) / 2;
    const top = (containerData.height - canvasData.height) / 2;

    cropper.setCanvasData({
      left,
      top,
      width: canvasData.width,
      height: canvasData.height,
    });
  };

  const handleImageChange = async (
    event: React.ChangeEvent<HTMLInputElement>,
  ) => {
    if (event.target.files && event.target.files?.[0]) {
      const file = event.target.files?.[0];
      if (file) {
        setOpen(true);
        setImageMeta((prev) => ({
          ...prev,
          size: parseFloat((file.size / (1024 * 1024)).toFixed(3)),
        }));
        const reader = new FileReader();
        reader.onload = () => {
          setImageUrl(reader.result as string);
        };
        reader.readAsDataURL(file);
      }
      if (inputUploadRef.current) {
        inputUploadRef.current.value = "";
      }
    }
  };

  const handleCrop = () => {
    const cropper = cropperRef.current?.cropper;
    if (cropper) {
      const cropBoxData = cropper?.getCropBoxData();
      const width = cropBoxData?.width?.toFixed(0);
      const height = cropBoxData?.height?.toFixed(0);
      setImageMeta((prev) => ({
        ...prev,
        width: parseInt(width || "0"),
        height: parseInt(height || "0"),
      }));
    }
  };

  return (
    <div className={clsx(className)}>
      <>
        {renderActionButton ? (
          renderActionButton(croppedImageUrl, handleImageChange)
        ) : (
          <div className="flex flex-col aspect-square">
            <div
              className={clsx(
                "w-full h-full rounded-md flex items-center justify-center flex-col cursor-pointer overflow-hidden relative bg-orange-800",
                !croppedImageUrl
                  ? "border border-dashed border-orange-500"
                  : "",
              )}
              onClick={() => inputUploadRef?.current?.click()}
            >
              <input
                name="image"
                ref={inputUploadRef}
                type="file"
                accept="image/*"
                id="imageInput"
                style={{display: "none"}}
                onChange={(event) => {
                  handleImageChange(event);
                }}
              />
              {croppedImageUrl ? (
                <img
                  src={croppedImageUrl}
                  alt="Preview"
                  className="w-full h-full object-cover"
                />
              ) : (
                <span className="text-sm text-orange-500">
                  + {t("cms.add_image")}
                </span>
              )}
            </div>
          </div>
        )}
      </>
      <Modal
        open={open}
        onClose={() => {
          if (!loading) onClose();
        }}
      >
        <div className="absolute top-1/2 left-1/2 bg-white transform -translate-x-1/2 -translate-y-1/2 rounded-lg">
          <div className="py-4 px-6 flex justify-between items-center">
            <span className="text-base font-bold">{t("cms.add_image")}</span>
            <IconClose className="cursor-pointer" onClick={onClose} />
          </div>
          <hr className="bg-[#F0F0F0]" />
          <div className="relative p-6">
            <span className="text-xs opacity-50">
              {t("common.size")}: {imageMeta?.size}MB, {t("common.width")}:{" "}
              {imageMeta?.width}, {t("common.height")}: {imageMeta?.height}
            </span>
            <Cropper
              ref={cropperRef}
              src={imageUrl}
              style={{height: 400, width: "100%", minWidth: 300}}
              guides
              background
              responsive
              zoomOnWheel={false}
              viewMode={1}
              minCropBoxHeight={10}
              minCropBoxWidth={10}
              zoomTo={zoomValue}
              autoCropArea={1}
              aspectRatio={freeCropMode ? undefined : aspectRatio}
              crop={handleCrop}
            />
            <div className="mt-4 flex flex-col justify-center items-center gap-2.5">
              <div className="flex items-center gap-2.5 w-full max-w-[70%]">
                <IconSubtract
                  className="cursor-pointer"
                  onClick={() => handleQuickZoom(false)}
                />
                <Slider
                  sx={{color: "#FF4319"}}
                  value={zoomValue}
                  onChange={(_, value) => handleZoom(value as number)}
                  min={0.1}
                  max={3}
                  step={0.1}
                  className="flex-1"
                  valueLabelDisplay="auto"
                  valueLabelFormat={(value) => `${(value * 100).toFixed(0)}%`}
                />
                <IconAdd
                  fill="#242728"
                  className="cursor-pointer"
                  onClick={() => handleQuickZoom(true)}
                />
              </div>
              <div className="flex items-center gap-2.5 w-full max-w-[70%]">
                <IconRotate
                  className="cursor-pointer"
                  onClick={() => handleQuickRotate(-45)}
                />
                <Slider
                  sx={{color: "#FF4319"}}
                  value={rotateValue}
                  onChange={(_, value) => handleRotate(value as number)}
                  min={-180}
                  max={180}
                  step={1}
                  className="flex-1"
                  valueLabelDisplay="auto"
                  valueLabelFormat={(value) => `${value}°`}
                />
                <IconRotate
                  className="-scale-x-[1] cursor-pointer"
                  onClick={() => handleQuickRotate(45)}
                />
              </div>
            </div>
          </div>
          <hr className="bg-[#F0F0F0]" />
          <div className="flex justify-end gap-6 py-2.5 px-4 text-[#000000D9]">
            <button
              onClick={onClose}
              disabled={loading}
              className="py-[9px] px-3 rounded-lg"
            >
              {t("common.cancel")}
            </button>
            <Button
              onClick={onCrop}
              loading={loading}
              className="!w-fit !bg-orange-500 gap-2 !py-[9px] !px-3 !rounded-lg !text-white whitespace-nowrap"
            >
              {t("common.confirm")}
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
}
