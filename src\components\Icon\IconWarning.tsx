import {SVGProps} from "react";

function IconWarning({
  width = "16",
  height = "16",
  ...props
}: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox={`0 0 ${width} ${height}`}
      className={props.className}
      fill="none"
      {...props}
    >
      <path
        d="M7.99997 8.99998V5.99998M3.17897 13.5H12.821C13.579 13.5 14.061 12.69 13.7005 12.0235L8.87947 3.12348C8.50097 2.42498 7.49897 2.42498 7.12047 3.12348L2.29997 12.0235C1.93847 12.69 2.42097 13.5 3.17897 13.5Z"
        stroke={props.stroke || "#ffffff"}
        strokeLinecap="round"
      />
      <path
        d="M8.5 10.75C8.5 10.8826 8.44732 11.0098 8.35355 11.1036C8.25979 11.1973 8.13261 11.25 8 11.25C7.86739 11.25 7.74021 11.1973 7.64645 11.1036C7.55268 11.0098 7.5 10.8826 7.5 10.75C7.5 10.6174 7.55268 10.4902 7.64645 10.3964C7.74021 10.3027 7.86739 10.25 8 10.25C8.13261 10.25 8.25979 10.3027 8.35355 10.3964C8.44732 10.4902 8.5 10.6174 8.5 10.75Z"
        fill="#393939"
        stroke={props.stroke || "#ffffff"}
        strokeWidth="0.5"
      />
    </svg>
  );
}

export default IconWarning;
