import {initializeApp} from "firebase/app";
import {getAnalytics, logEvent as _logEvent} from "firebase/analytics";
import config from "@config";
import {
  IFirebaseLogEventName,
  IFirebaseLogEventParams,
} from "src/types/firebase";
import store from "@redux/store";

export const initializeFirebase = () => {
  const app = initializeApp(
    window.location.hostname.includes("laomusic")
      ? config.FIREBASE
      : config.FIREBASE_DEV,
  );
  getAnalytics(app);
};

export function logEvent<T extends IFirebaseLogEventName>(
  eventName: T,
  eventParams?: IFirebaseLogEventParams<T>,
) {
  const user = store.getState().user.userInfo;
  const analytics = getAnalytics();
  _logEvent(
    analytics,
    eventName as string,
    user
      ? {...eventParams, user_name: user.username, user_email: user.email}
      : eventParams,
  );
}
