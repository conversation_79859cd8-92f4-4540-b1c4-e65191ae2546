import ApiLibrary from "@api/ApiLibrary";
import QUERY_KEY from "@api/QueryKey";
import CommonYoutubeCard from "@components/CommonYoutubeCard";
import IconNoData from "@components/Icon/IconNoData";
import YoutubeCardSkeleton from "@components/YoutubeCardSkeleton";
import {Grid} from "@mui/material";
import {playSongFromList} from "@redux/slices/PlayerSlice";
import {useQuery} from "@tanstack/react-query";
import {useTranslation} from "react-i18next";
import {useDispatch} from "react-redux";
import {ESongType} from "src/types";

export default function FavoriteYoutube(): JSX.Element {
  const {t} = useTranslation();
  const dispatch = useDispatch();

  const {data, isLoading} = useQuery({
    queryKey: [
      QUERY_KEY.LIBRARY.GET_MY_FAVORITE_YOUTUBE,
      {
        page: 0,
        pageSize: 15,
        type: ESongType.YOUTUBE,
      },
    ],
    queryFn: () =>
      ApiLibrary.getMyFavoriteSong({
        page: 0,
        pageSize: 15,
        type: ESongType.YOUTUBE,
      }),
  });
  return (
    <div className="w-full flex flex-col">
      {isLoading && !data && (
        <Grid
          container
          spacing={{xs: 2, md: 3}}
          columns={{xs: 2, sm: 6, md: 12, lg: 15}}
        >
          {[...Array(5)].map((_, index) => (
            <Grid item xs={1} sm={2} md={3} lg={3} key={index}>
              <YoutubeCardSkeleton />
            </Grid>
          ))}
        </Grid>
      )}
      {!isLoading && data?.data?.length === 0 && (
        <div className=" flex justify-center items-center flex-col lg:gap-2.5 gap-1">
          <IconNoData />
          <span className="text-[#E3E3E3] w-full text-base font-normal flex justify-center">
            {t("common.mv_list_not_found")}
          </span>
        </div>
      )}
      <div>
        <Grid
          container
          spacing={{xs: 2, md: 3}}
          columns={{xs: 2, sm: 6, md: 12, lg: 15}}
        >
          {data?.data?.map((item, index) => (
            <Grid item xs={1} sm={2} md={3} lg={3} key={index}>
              <CommonYoutubeCard
                data={item}
                handlePlayMusic={() =>
                  dispatch(playSongFromList({song: item, songList: data.data}))
                }
              />
            </Grid>
          ))}
        </Grid>
      </div>
    </div>
  );
}
