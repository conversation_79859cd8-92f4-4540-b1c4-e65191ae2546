import ICNoInternet from "@components/Icon/ICNoInternet";
import ICRefresh from "@components/Icon/ICRefresh";
import {Button} from "@mui/material";
import {useTranslation} from "react-i18next";

export default function NoInternet(): JSX.Element {
  const {t} = useTranslation();

  return (
    <div>
      <div className="topic-icon text-white flex justify-center items-center flex-col flex-1 gap-6 h-[calc(100vh-164px)]">
        <ICNoInternet fill="white" />
        <div className="title font-semibold text-xl">
          {t("common.you_offline")}
        </div>
        <div className="remind text-sm opacity-55">
          {t("common.please_connect_internet")}
        </div>
        <Button
          className="text-white justify-center items-center gap-3"
          size="small"
          color="inherit"
          sx={{
            "background": "rgba(236,236,236,0.2)",
            "borderRadius": "12px",
            "padding": "4px 8px",
            "cursor": "pointer",
            "textTransform": "capitalize",
            "fontWeight": "0",
            "&:hover": {
              background: "rgba(236,236,236,0.3)",
            },
          }}
          onClick={() => {
            location.replace(location.href);
          }}
        >
          <ICRefresh fill="white" /> {t("common.refresh")}
        </Button>
      </div>
    </div>
  );
}
