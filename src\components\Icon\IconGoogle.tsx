import {SVGProps} from "react";

function IconGoogle(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={29}
      height={29}
      viewBox="0 0 29 29"
      fill="none"
      {...props}
    >
      <path
        fill="#4285F4"
        d="M26.751 14.53c0-1.007-.083-1.742-.264-2.505H14.751v4.546h6.889c-.139 1.13-.889 2.832-2.556 3.975l-.023.152 3.71 2.817.258.025c2.36-2.137 3.722-5.28 3.722-9.01Z"
      />
      <path
        fill="#34A853"
        d="M14.75 26.508c3.376 0 6.209-1.089 8.278-2.967l-3.944-2.995c-1.056.722-2.472 1.225-4.333 1.225a7.51 7.51 0 0 1-7.111-5.09l-.147.012-3.859 2.926-.05.138c2.056 4.001 6.278 6.751 11.167 6.751Z"
      />
      <path
        fill="#FBBC05"
        d="M7.64 16.68a7.405 7.405 0 0 1-.417-2.422c0-.844.153-1.66.403-2.423l-.007-.162-3.907-2.974-.128.06a12.062 12.062 0 0 0-1.333 5.499c0 1.973.486 3.838 1.333 5.499L7.64 16.68Z"
      />
      <path
        fill="#EB4335"
        d="M14.75 6.744c2.348 0 3.931.994 4.834 1.824l3.528-3.375c-2.167-1.974-4.986-3.185-8.361-3.185-4.89 0-9.111 2.75-11.167 6.75l4.042 3.077c1.014-2.954 3.82-5.09 7.125-5.09Z"
      />
    </svg>
  );
}

export default IconGoogle;
