import {SVGProps} from "react";

function IconStatistical({
  width = "20",
  height = "20",
  ...props
}: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox={`0 0 ${width} ${height}`}
      className={props.className}
      fill="none"
      {...props}
    >
      <g clipPath="url(#clip0_4330_9853)">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M7.70708 1.51091C7.76508 1.69568 7.7474 1.89592 7.65791 2.06765C7.56843 2.23938 7.41446 2.36855 7.22983 2.42679C5.55666 2.95375 4.0951 4.00081 3.05762 5.41576C2.02014 6.83072 1.46082 8.53981 1.46097 10.2946C1.46084 11.3772 1.67387 12.4491 2.08791 13.4493C2.50194 14.4495 3.10886 15.3583 3.87401 16.1238C4.63917 16.8894 5.54756 17.4966 6.54733 17.9109C7.5471 18.3252 8.61866 18.5385 9.70081 18.5385C11.4548 18.5387 13.1632 17.9793 14.5776 16.9416C15.992 15.9039 17.0387 14.4421 17.5657 12.7684C17.5919 12.674 17.6369 12.5857 17.6979 12.509C17.7589 12.4323 17.8348 12.3686 17.9209 12.3218C18.007 12.275 18.1017 12.246 18.1993 12.2366C18.2968 12.2272 18.3953 12.2375 18.4888 12.2669C18.5823 12.2964 18.6689 12.3443 18.7435 12.4079C18.8181 12.4716 18.8791 12.5495 18.9229 12.6373C18.9667 12.725 18.9925 12.8206 18.9986 12.9185C19.0047 13.0163 18.991 13.1144 18.9585 13.2069C17.7235 17.1432 14.0467 20 9.70178 20C4.34394 20 0 15.6544 0 10.2956C0 5.94806 2.8557 2.2709 6.79154 1.03349C6.97636 0.975501 7.17664 0.993326 7.34833 1.08304C7.52002 1.17275 7.64906 1.32604 7.70708 1.51091Z"
          fill={props.fill || "#ffffff"}
        />
        <path
          d="M19.913 7.94772C19.3869 6.07334 18.3876 4.3658 17.0111 2.98919C15.6346 1.61257 13.9272 0.613156 12.053 0.0869819C10.409 -0.372061 9 1.05407 9 2.76023V9.45486C9 10.3079 9.692 11 10.545 11H17.239C18.946 11 20.372 9.59087 19.913 7.94772Z"
          fill={props.fill || "#ffffff"}
        />
      </g>
      <defs>
        <clipPath id="clip0_4330_9853">
          <rect width="20" height="20" fill={props.fill || "#ffffff"} />
        </clipPath>
      </defs>
    </svg>
  );
}

export default IconStatistical;
