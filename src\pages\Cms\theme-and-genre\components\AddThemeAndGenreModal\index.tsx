import ApiAutofill from "@api/ApiAutofill";
import ApiThemeAndGenre, {ICreateThemeAndGenre} from "@api/ApiThemeAndGenre";
import AutoCompleteAutofill from "@components/AutoCompleteAutofill";
import GlobalButton from "@components/ButtonGlobal";
import ImageCropper from "@components/ImageCropper";
import TextFieldCustomer from "@components/TextFieldCustomer";
import {Dialog, DialogActions, DialogContent, DialogTitle} from "@mui/material";
import {useMutation} from "@tanstack/react-query";
import {ErrorMessage, Formik} from "formik";
import {useMemo, useState} from "react";
import {useTranslation} from "react-i18next";
import {toast} from "react-toastify";
import {EThemeAndGenreType, IThemeAndGenre} from "src/types";
import * as Yup from "yup";

interface AddThemeAndGenreModalProps {
  selectedItem?: IThemeAndGenre;
  open: boolean;
  type: EThemeAndGenreType;
  onClose: () => void;
  refetch: () => void;
}

export default function AddThemeAndGenreModal({
  selectedItem,
  open,
  type,
  onClose,
  refetch,
}: AddThemeAndGenreModalProps) {
  const {t} = useTranslation();

  const initialValues: ICreateThemeAndGenre = useMemo(() => {
    if (selectedItem)
      return {
        image: undefined,
        name: selectedItem?.name,
        nameLo: selectedItem?.nameLo,
        nameEn: selectedItem?.nameEn,
        nameVi: selectedItem?.nameVi,
        type,
        parentId: {
          label: selectedItem?.parentGenreDto?.name ?? "",
          id: selectedItem?.parentGenreDto?.id ?? "",
        },
        description: selectedItem?.description,
      };
    return {
      image: undefined,
      name: "",
      nameLo: "",
      nameEn: "",
      nameVi: "",
      type,
      parentId: {
        label: "",
        id: "",
      },
      description: "",
    };
  }, [selectedItem, type]);

  const validationSchema = Yup.object({
    nameVi: Yup.string().required(
      t("validation.field_is_require", {field: t("cms.theme_and_genre.name")}),
    ),
    nameLo: Yup.string().required(
      t("validation.field_is_require", {field: t("cms.theme_and_genre.name")}),
    ),
    nameEn: Yup.string().required(
      t("validation.field_is_require", {field: t("cms.theme_and_genre.name")}),
    ),
    image: Yup.mixed()
      .test(
        "required",
        t("validation.field_is_require", {
          field: t("cms.theme_and_genre.cover"),
        }),
        (file) => {
          if (selectedItem && !file) return true;
          return !!file;
        },
      )
      .test(
        "size",
        t("validation.file_size_limit_exceed", {size: "10MB"}),
        (file) => {
          if (selectedItem && !file) return true;
          return file && (file as File).size < 10 * 1024 * 1024;
        },
      ),
  });
  const isEdit = selectedItem !== undefined;
  const [loading, setLoading] = useState(false);
  const {mutateAsync: createThemeAndGenreMutationAsync} = useMutation({
    mutationFn: ApiThemeAndGenre.createThemeAndGenre,
    onSuccess: () => {
      toast.success(t("common.add_successfully"));
      refetch();
      onClose();
    },
  });

  const {mutateAsync: updateThemeAndGenreMuatationAsync} = useMutation({
    mutationFn: ApiThemeAndGenre.updateThemeAndGenre,
    onSuccess: () => {
      toast.success(t("common.update_successfully"));
      refetch();
      onClose();
    },
  });

  const handleSubmit = async (values: ICreateThemeAndGenre) => {
    const formData = {
      ...values,
      name: values.nameLo ?? "",
      parentId: values.parentId ? values.parentId.id : undefined,
    };

    try {
      setLoading(true);
      if (isEdit) {
        await updateThemeAndGenreMuatationAsync({
          id: selectedItem.id,
          ...(formData as ICreateThemeAndGenre),
        });
      } else {
        await createThemeAndGenreMutationAsync(
          formData as ICreateThemeAndGenre,
        );
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog
      open={open}
      onClose={() => {
        if (!loading) {
          onClose?.();
        }
      }}
      fullWidth
    >
      <Formik
        initialValues={initialValues}
        onSubmit={handleSubmit}
        validationSchema={validationSchema}
        enableReinitialize
      >
        {({handleChange, values, handleSubmit, setFieldValue}) => (
          <>
            <DialogTitle>
              {isEdit ? t("common.update") : t("common.add")}{" "}
              {type === EThemeAndGenreType.THEME
                ? t("cms.theme_and_genre.theme").toLocaleLowerCase()
                : t("cms.theme_and_genre.genre").toLocaleLowerCase()}
            </DialogTitle>

            <DialogContent className="flex flex-col gap-y-4">
              <div>
                <label className="block  text-sm font-bold text-gray-700 pb-2 pt-2">
                  {t("cms.theme_and_genre.cover")}
                </label>
                <ImageCropper
                  onChange={(file) => {
                    setFieldValue("image", file);
                  }}
                  className="w-[120px] aspect-square object-cover"
                  initialImageUrl={
                    selectedItem?.images?.DEFAULT || selectedItem?.images?.SMALL
                  }
                  freeCropMode={true}
                />
                <div className="text-red-500 text-sm">
                  <ErrorMessage name="image" />
                </div>
              </div>
              {/* Vietnam name */}
              <TextFieldCustomer
                name="nameVi"
                required
                value={values.nameVi}
                onChange={handleChange("nameVi")}
                label={
                  type === EThemeAndGenreType.THEME
                    ? t("cms.theme_and_genre.theme_name", {
                        language: t("common.language.vietnamese"),
                      })
                    : t("cms.theme_and_genre.genre_name", {
                        language: t("common.language.vietnamese"),
                      })
                }
                placeholder={
                  type === EThemeAndGenreType.THEME
                    ? t("cms.theme_and_genre.theme_name", {
                        language: t("common.language.vietnamese"),
                      })
                    : t("cms.theme_and_genre.genre_name", {
                        language: t("common.language.vietnamese"),
                      })
                }
              />
              {/* Lao name */}
              <TextFieldCustomer
                name="nameLo"
                required
                value={values.nameLo}
                onChange={handleChange("nameLo")}
                label={
                  type === EThemeAndGenreType.THEME
                    ? t("cms.theme_and_genre.theme_name", {
                        language: t("common.language.lao"),
                      })
                    : t("cms.theme_and_genre.genre_name", {
                        language: t("common.language.lao"),
                      })
                }
                placeholder={
                  type === EThemeAndGenreType.THEME
                    ? t("cms.theme_and_genre.theme_name", {
                        language: t("common.language.lao"),
                      })
                    : t("cms.theme_and_genre.genre_name", {
                        language: t("common.language.lao"),
                      })
                }
              />
              {/* English name */}
              <TextFieldCustomer
                name="nameEn"
                required
                value={values.nameEn}
                onChange={handleChange("nameEn")}
                label={
                  type === EThemeAndGenreType.THEME
                    ? t("cms.theme_and_genre.theme_name", {
                        language: t("common.language.english"),
                      })
                    : t("cms.theme_and_genre.genre_name", {
                        language: t("common.language.english"),
                      })
                }
                placeholder={
                  type === EThemeAndGenreType.THEME
                    ? t("cms.theme_and_genre.theme_name", {
                        language: t("common.language.english"),
                      })
                    : t("cms.theme_and_genre.genre_name", {
                        language: t("common.language.english"),
                      })
                }
              />

              {type === EThemeAndGenreType.THEME && (
                <div className="flex flex-col gap-2">
                  <label className="text-sm font-bold text-gray-700">
                    {t("cms.theme_and_genre.topic_parent")}
                  </label>
                  <AutoCompleteAutofill
                    className="h-10 rounded-none!"
                    name="parentId"
                    value={values.parentId}
                    placeHolder={t("cms.theme_and_genre.topic_parent")}
                    suggestionAPI={ApiAutofill.autoGenre}
                    onChange={(val) => setFieldValue("parentId", val)}
                  />
                </div>
              )}

              <div className="flex flex-col gap-2">
                <label className="text-sm font-bold text-gray-700">
                  {type === EThemeAndGenreType.THEME
                    ? t("cms.theme_and_genre.des_theme")
                    : t("cms.theme_and_genre.des_genre")}
                </label>
                <textarea
                  name="description"
                  value={values.description}
                  onChange={handleChange}
                  placeholder={t("cms.theme_and_genre.placeholder_genres")}
                  className="border border-gray-300 p-2 text-sm w-full"
                />
              </div>
            </DialogContent>

            <DialogActions>
              <GlobalButton
                onClick={onClose}
                text={t("common.cancel")}
                color="white"
                className="w-20"
                textClassName="text-[#000000D9]"
                disabled={loading}
              />
              <GlobalButton
                text={t("common.confirm")}
                className="w-30"
                onClick={handleSubmit}
                isLoading={loading}
                disabled={loading}
              />
            </DialogActions>
          </>
        )}
      </Formik>
    </Dialog>
  );
}
