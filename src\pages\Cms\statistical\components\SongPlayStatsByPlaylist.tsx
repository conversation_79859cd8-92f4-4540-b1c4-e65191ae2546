import {useTranslation} from "react-i18next";
import {useState} from "react";
import Bar<PERSON>hart<PERSON>ustom from "./BarChartCustom";
import CommonDateRange from "@components/CommonDateRange";
import dayjs from "dayjs";
import Header<PERSON><PERSON> from "./HeaderChart";
import {IParamsDefault} from "src/types";
import {useQuery} from "@tanstack/react-query";
import QUERY_KEY from "@api/QueryKey";
import ApiCMSStatistics from "@api/ApiCMSStatistics";
import {
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  SelectChangeEvent,
} from "@mui/material";

type statisticsListens = "song" | "playlist";

function SongPlayStatsByPlaylist() {
  const {t} = useTranslation();
  const [type, setType] = useState<statisticsListens>("playlist");

  const [params, setParams] = useState<IParamsDefault>({
    fromDate: dayjs().subtract(30, "day").format("YYYY-MM-DD"),
    toDate: dayjs().format("YYYY-MM-DD"),
    page: 1,
    pageSize: 10,
    direction: "desc",
  });

  const {data: songListensData} = useQuery({
    queryKey: [QUERY_KEY.STATISTICS.GET_SONG_LISTENS, params, type],
    queryFn: () => ApiCMSStatistics.getSongListens(params),
    enabled: type === "song",
  });
  const {data: playlistListenData} = useQuery({
    queryKey: [QUERY_KEY.STATISTICS.GET_PLAYLIST_LISTENS, params, type],
    queryFn: () => ApiCMSStatistics.getPlaylistListens(params),
    enabled: type === "playlist",
  });

  const dashboardData =
    (type === "song" ? songListensData : playlistListenData)?.data?.map(
      (item) => ({
        name: `${item.name}: ${item.urlSlug}`,
        value: Number(item?.totalListens ?? 0),
      }),
    ) ?? [];

  const linearGradient = {
    id: "linear-gradient-pink",
    linear: [
      {offset: "0%", color: "#FF1090"},
      {offset: "100%", color: "#FFA6D6"},
    ],
  };
  const labels = {
    x: type === "song" ? t("cms.dashboard.song") : t("cms.dashboard.playlist"),
    y: t("cms.dashboard.listen"),
  };

  const listSelects = [
    {
      idLabel: "type_simple-label",
      id: "type_simple",
      label: t("cms.dashboard.type"),
      value: type,
      onChange: (e: SelectChangeEvent) => {
        setType(e.target.value as statisticsListens);
      },
      items: [
        {
          label: t("cms.dashboard.song"),
          value: "song",
        },
        {
          label: t("cms.dashboard.playlist"),
          value: "playlist",
        },
      ],
    },
    {
      idLabel: "top_simple-label",
      id: "top_simple",
      label: t("cms.dashboard.top"),
      value: (params?.pageSize ?? 10).toString(),
      onChange: (e: SelectChangeEvent) => {
        setParams((prev) => ({
          ...prev,
          pageSize: parseInt(e.target.value, 10),
        }));
      },
      items: [
        {
          label: "10",
          value: "10",
        },
        {
          label: "20",
          value: "20",
        },
        {
          label: "30",
          value: "30",
        },
      ],
    },
  ];

  return (
    <div className="bg-white rounded-2xl shadow-md p-5">
      <div>
        <div className="flex justify-between items-start flex-row">
          <HeaderChart title={t("cms.dashboard.music_listening_time")} />
          <div className="flex flex-row gap-4">
            {listSelects.map((item) => (
              <FormControl key={item.id}>
                <InputLabel id={item.idLabel}>{item.label}</InputLabel>
                <Select
                  labelId={item.idLabel}
                  sx={{
                    "background": "#f2f2f3",
                    "borderRadius": "8px",
                    "& .MuiOutlinedInput-notchedOutline": {
                      border: "1px solid #dcdee0",
                    },
                    "&:hover .MuiOutlinedInput-notchedOutline": {
                      border: "1px solid #dcdee0",
                    },
                  }}
                  id={item.id}
                  value={item.value}
                  label={item.label}
                  onChange={item.onChange}
                >
                  {item.items.map((item) => (
                    <MenuItem value={item.value} key={item.label}>
                      {item.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            ))}
            <CommonDateRange
              labelDateRange={[
                t("cms.dashboard.start"),
                t("cms.dashboard.end"),
              ]}
              valueDateRange={[params?.fromDate, params?.toDate]}
              onChangeStartDate={(v) => {
                setParams((prev) => {
                  return {
                    ...prev,
                    fromDate: dayjs(v).format("YYYY-MM-DD"),
                  };
                });
              }}
              onChangeEndDate={(v) => {
                setParams((prev) => {
                  return {
                    ...prev,
                    toDate: dayjs(v).format("YYYY-MM-DD"),
                  };
                });
              }}
            />
          </div>
        </div>
        <div className="p-5">
          <BarChartCustom
            key={`${type}-${params.pageSize}`}
            data={dashboardData ?? []}
            linearGradientColor={linearGradient}
            labels={labels}
          />
        </div>
      </div>
    </div>
  );
}

export default SongPlayStatsByPlaylist;
