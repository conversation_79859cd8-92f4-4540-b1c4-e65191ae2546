import ApiRanking from "@api/ApiRanking";
import {IDataWithMeta} from "@api/Fetcher";
import QUERY_KEY from "@api/QueryKey";
import SongItem from "@components/SongItem";
import SongCardSkeleton from "@pages/MusicLibrary/components/SongCardSkeleton";
import {useInfiniteQuery} from "@tanstack/react-query";
import {useEffect, useMemo, useRef} from "react";
import {useTranslation} from "react-i18next";
import {IParamsDefault, ISong} from "src/types";
import RankChange from "../RankChange";
import {playSongFromList} from "@redux/slices/PlayerSlice";
import {useDispatch} from "react-redux";

interface IRankSongProps {
  params: IParamsDefault;
}

export default function RankSong({params}: IRankSongProps) {
  const {t} = useTranslation();
  const dispatch = useDispatch();
  const observer = useRef<IntersectionObserver | null>(null);
  const lastElementRef = useRef<HTMLDivElement | null>(null);

  const {data, fetchNextPage, isLoading, isError, hasNextPage} =
    useInfiniteQuery<IDataWithMeta<ISong[]>, Error>({
      queryKey: [QUERY_KEY.RAKING_BOARD.GET_LIST_SONG, params],
      queryFn: ({pageParam = 0}) =>
        ApiRanking.getListOfRankingBoard({
          pageSize: 20,
          page: pageParam as number,
          ...params,
        }),
      getNextPageParam: (lastPage) =>
        lastPage?.meta?.totalPage - 1 > lastPage?.meta?.currentPage
          ? (lastPage?.meta?.currentPage || 0) + 1
          : undefined,
      initialPageParam: 0,
    });

  const songList = useMemo(() => {
    return data?.pages.flatMap((page) => page.data) || [];
  }, [data]);

  useEffect(() => {
    if (isLoading || isError) return;

    if (observer.current) observer.current.disconnect();

    observer.current = new IntersectionObserver((entries) => {
      if (entries[0].isIntersecting && hasNextPage) {
        fetchNextPage();
      }
    });

    if (lastElementRef?.current) {
      observer.current.observe(lastElementRef.current);
    }

    return () => {
      if (observer.current) {
        observer.current.disconnect();
      }
    };
  }, [isLoading, isError, data, hasNextPage]);

  return (
    <>
      {isLoading && !data && (
        <div className="flex flex-col">
          {[...Array(5)].map((_, index) => (
            <SongCardSkeleton key={`skeleton_${index}`} />
          ))}
        </div>
      )}
      {(isError || data?.pages.length === 0) && (
        <div>{t("common.list_empty")}</div>
      )}
      <div>
        {songList?.map((song, index) => (
          <SongItem
            song={song}
            showReleaseDate={false}
            key={`ranking_${song?.id}`}
            left={
              <div
                className="flex items-center gap-x-4 mr-4 md:gap-x-6 md:mr-6"
                key={index}
              >
                <div className="font-bold text-base md:text-xl text-center w-6 md:w-10">
                  {song?.position}
                </div>
                <RankChange
                  change={
                    song?.prevPosition &&
                    (song?.prevPosition > 100
                      ? 0
                      : song?.prevPosition - (song?.position || 0))
                  }
                />
              </div>
            }
            handlePlayMusic={() => {
              dispatch(
                playSongFromList({
                  song: song,
                  songList: songList,
                }),
              );
            }}
          />
        ))}
        {hasNextPage && (
          <div className="flex flex-col">
            <SongCardSkeleton index={1} />
          </div>
        )}
      </div>
      {!isLoading && <div ref={lastElementRef} className="h-1"></div>}{" "}
    </>
  );
}
