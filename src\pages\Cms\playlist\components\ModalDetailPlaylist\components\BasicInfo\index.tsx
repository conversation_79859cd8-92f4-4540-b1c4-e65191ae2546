import BasicInfoCmsCustom from "@components/BasicInfoCmsCustom";
import {Avatar, AvatarGroup} from "@mui/material";
import {useTranslation} from "react-i18next";
import {EThemeAndGenreType, IArtist, IPlaylist} from "src/types";
import {convertDate, convertDuration, convertNumber} from "src/utils/timeUtils";

export default function BasicInfo({
  albumData,
}: {
  albumData: IPlaylist | undefined;
}) {
  const {t} = useTranslation();

  const themeData = albumData?.genres?.filter(
    (item) => item?.type === EThemeAndGenreType.THEME,
  );

  const genresData = albumData?.genres?.filter(
    (item) => item?.type === EThemeAndGenreType.GENRE,
  );

  const basicInfoCol1 = [
    {
      title: t("cms.album.album_name"),
      value: (
        <span className="text-sm font-semibold text-gray-default">
          {albumData?.name}
        </span>
      ),
    },
    {
      title: t("cms.playlist.user_create_playlist"),
      value: (
        <div className="w-full h-full flex justify-start items-center space-x-1">
          <Avatar
            src={albumData?.user?.avatar || "/image/default-avatar.png"}
            sx={{
              width: 24,
              height: 24,
              fontSize: 12,
            }}
          />
          <span className="font-semibold text-sm text-[#242728]">
            {albumData?.user?.username}
          </span>
        </div>
      ),
    },
    {
      title: t("common.duration"),
      value: (
        <span className="text-sm whitespace-nowrap text-left font-semibold text-gray-default">
          {convertDuration(albumData?.totalDurations)}
        </span>
      ),
    },
    {
      title: t("common.quantity_song"),
      value: (
        <div className="text-sm font-semibold flex gap-1 text-[#242728]">
          <span>{convertNumber(albumData?.totalSongs)}</span>
          <span>{t("common.song")}</span>
        </div>
      ),
    },
    {
      title: t("cms.song.theme"),
      value: (
        <div className="text-sm font-semibold text-gray-default line-clamp-3">
          {themeData?.map((item) => (
            <span key={item?.id}>
              {item?.name}
              {themeData?.length > 1 &&
              themeData?.indexOf(item) < themeData?.length - 1
                ? ", "
                : ""}
            </span>
          ))}
        </div>
      ),
    },
    {
      title: t("cms.song.genres"),
      value: (
        <div className="flex flex-wrap line-clamp-3">
          {genresData?.map((item) => (
            <span
              key={item?.id}
              className="text-sm font-normal text-[#393939] border border-solid border-[#DCDCDC] rounded-lg px-2 py-0.5 bg-[#F2F2F3]"
            >
              {item?.name}
              {genresData?.length > 1 &&
              genresData?.indexOf(item) < genresData?.length - 1
                ? " "
                : ""}
            </span>
          ))}
        </div>
      ),
    },
    {
      title: t("cms.song.release_time"),
      value: (
        <span className="text-sm whitespace-nowrap text-left font-semibold text-gray-default">
          {convertDate(albumData?.releaseDate)}
        </span>
      ),
    },
  ];

  const basicInfoCol2 = [
    {
      title: t("cms.album.featured_artist"),
      value: (
        <div className="w-full h-full flex justify-start items-center space-x-1">
          <AvatarGroup
            max={3}
            sx={{
              "& .MuiAvatar-root": {
                width: 24,
                height: 24,
                fontSize: 12,
              },
            }}
          >
            {albumData?.artists?.map((artist: IArtist, index: number) => (
              <Avatar
                key={`avatar_${index}`}
                src={
                  artist?.images?.SMALL ||
                  artist?.images?.DEFAULT ||
                  "/image/default-avatar.png"
                }
              />
            ))}
          </AvatarGroup>
          <span className="font-semibold text-sm text-[#242728]">
            {albumData?.artists
              ?.slice(0, 2)
              .map((artist: IArtist) => artist?.stageName ?? artist?.name)
              .join(", ")}
            {(albumData?.artists?.length ?? 0) > 2 &&
              ` & ${t("common.lots_artist")}`}
          </span>
        </div>
      ),
    },
    {
      title: t("common.listen_count"),
      value: (
        <span className="text-sm whitespace-nowrap text-left font-semibold text-gray-default">
          {convertNumber(albumData?.totalListens)}
        </span>
      ),
    },
    {
      title: t("common.favorite_count"),
      value: (
        <span className="text-sm whitespace-nowrap text-left font-semibold text-gray-default">
          {convertNumber(albumData?.totalLikes)}
        </span>
      ),
    },
    {
      title: t("common.download_count"),
      value: (
        <span className="text-sm whitespace-nowrap text-left font-semibold text-gray-default">
          {convertNumber(albumData?.totalDownloads)}
        </span>
      ),
    },
    {
      title: t("cms.song.share_count"),
      value: (
        <span className="text-sm whitespace-nowrap text-left font-semibold text-gray-default">
          {convertNumber(albumData?.totalShares)}
        </span>
      ),
    },
  ];

  return (
    <div className="max-h-[50vh] overflow-y-auto">
      <BasicInfoCmsCustom
        image={
          albumData?.images?.SMALL ||
          albumData?.images?.DEFAULT ||
          "/image/default-music.png"
        }
        basicInfoCol1={basicInfoCol1}
        basicInfoCol2={basicInfoCol2}
        description={albumData?.description || "-"}
      />
    </div>
  );
}
