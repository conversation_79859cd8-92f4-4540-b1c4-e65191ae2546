@use "../../styles/global.scss" as globals;

.table-song-item-v2 {
  .table {
    display: table;
    width: 100%;
    border-collapse: collapse;
    color: white;
  }

  .table-header-group {
    display: table-header-group;
  }

  .table-row-group {
    display: table-row-group;
  }

  .table-row {
    display: table-row;
  }

  .table-cell {
    display: table-cell;
  }

  // Song item specific styles for table layout
  .song-item {
    .song-action {
      display: none;

      &:has(.liked) {
        display: block;
      }

      &:hover {
        background-color: rgba(255, 255, 255, 0.2);
      }
    }

    @media screen and (max-width: globals.$sm) {
      .song-action {
        display: flex;
      }
      .song-duration {
        display: none;
      }
    }

    @media screen and (min-width: globals.$sm) {
      &:hover {
        .song-duration {
          display: none;
        }
        .song-action {
          display: flex;
        }
      }
    }
  }

  // Responsive behavior
  @media screen and (max-width: globals.$md) {
    .hidden-md {
      display: none !important;
    }
  }

  @media screen and (max-width: globals.$lg) {
    .hidden-lg {
      display: none !important;
    }
  }
}
