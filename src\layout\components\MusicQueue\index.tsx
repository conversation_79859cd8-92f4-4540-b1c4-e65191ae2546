import {Icon<PERSON>utton} from "@mui/material";
import {removeSongFromQueue, toggleSuggest} from "@redux/slices/PlayerSlice";
import {IRootState} from "@redux/store";
import {useMutation} from "@tanstack/react-query";
import clsx from "clsx";
import {useEffect, useRef, useState} from "react";
import {useTranslation} from "react-i18next";
import {useDispatch, useSelector} from "react-redux";
import {ESongType, ISong} from "src/types";
import SongCard from "./SongCard";
import GlobalSwitch from "@components/SwitchCustom";
import IconMoreHorizontal from "@components/Icon/IconMoreHorizontal";
import PopupMenu from "@components/PopupMenu";
import IconAdd24px from "@components/Icon/IconAdd24px";
import IconShare from "@components/Icon/IconShare";
import ModalAddToPlaylist from "@components/ModalAddToPlaylist";
import ModalShare from "@components/ModalShare";
import {generateShareLink} from "src/utils/global";
import ApiSong from "@api/ApiSong";
import {toast} from "react-toastify";
import IconHeart from "@components/Icon/IconHeart";
import {DeleteOutlineRounded} from "@mui/icons-material";
import PlayerUtil from "src/core/PlayerUtil";
import {handleLikeSong} from "src/utils/like";
import {useLayout} from "src/utils/hooks";

interface MusicQueueProps {
  className?: string;
}

export default function MusicQueue({className}: MusicQueueProps): JSX.Element {
  const {t} = useTranslation();
  const openWishList = useSelector(
    (state: IRootState) => state.wishlist.isOpen,
  );
  const {currentSong, queueList, isSuggestionEnabled, suggestedSongs} =
    useSelector((state: IRootState) => state.player);
  const {isMobile} = useLayout();
  const currentIndex = queueList?.findIndex(
    (song) => song?.id === currentSong?.id,
  );
  const [menuAnchor, setMenuAnchor] = useState<null | HTMLElement>(null);
  const [addToPlaylistOpen, setAddToPlaylistOpen] = useState(false);
  const [openModalShare, setOpenModalShare] = useState<boolean>(false);
  const queueRef = useRef<HTMLDivElement | null>(null);
  const currentSongRef = useRef<HTMLDivElement | null>(null);
  const dispatch = useDispatch();

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(toggleSuggest(event.target.checked));
  };

  useEffect(() => {
    if (currentSongRef?.current && queueRef?.current) {
      const listTop = queueRef.current.getBoundingClientRect().top;
      const itemTop = currentSongRef.current.getBoundingClientRect().top;
      const offset = itemTop - listTop;

      queueRef.current.scrollTo({
        top: queueRef.current.scrollTop + offset,
        behavior: "smooth",
      });
    }
  }, [currentSong, queueList]);

  const shareMutate = useMutation({
    mutationFn: ApiSong.shareSong,
    onSuccess: () => {
      const link = generateShareLink({type: "song", data: currentSong});
      navigator.clipboard.writeText(link).then(() => {
        toast.success(t("common.copy_link_success"));
      });
    },
    onError: () => {
      toast.error(t("common.copy_link_failed"));
    },
  });

  const handleOpenMenu = (event: React.MouseEvent<HTMLButtonElement>) => {
    setMenuAnchor(event?.currentTarget);
  };

  const handleCloseMenu = () => {
    setMenuAnchor(null);
  };

  const openAddToPlaylist = () => {
    setAddToPlaylistOpen(true);
  };

  const handleOpenModalShare = () => {
    setOpenModalShare(true);
  };

  const handleCloseModalShare = () => {
    setOpenModalShare(false);
  };

  const handleCopyLink = () => {
    shareMutate.mutateAsync(currentSong?.id ?? "");
  };

  const handleDeleteSongFromQueue = () => {
    if (currentSong) {
      dispatch(removeSongFromQueue(currentSong?.id ?? ""));
      if (PlayerUtil.instance.isSongPlaying(currentSong)) {
        PlayerUtil.instance.clear();
      }
      toast.success(`${t("common.delete_successfully")}`);
    }
  };

  return (
    <div className={clsx("text-white duration-500", className)}>
      <div className="px-3 hidden md:block">
        <h3 className="text-base font-semibold py-3 pl-3 h-12 box-border">
          {t("common.playlist_common")}
        </h3>
      </div>
      <div className="md:h-[calc(100%-48px)] overflow-y-scroll" ref={queueRef}>
        {queueList?.length > 1 && currentIndex > 0 && (
          <div className="flex flex-col w-full md:px-3">
            {queueList
              ?.slice(0, currentIndex)
              ?.map((song: ISong, index: number) => {
                return (
                  <div key={index}>
                    <SongCard
                      data={song}
                      index={index}
                      className="opacity-50"
                    />
                  </div>
                );
              })}
          </div>
        )}
        <div
          className={clsx(
            "top-0 h-fit z-30 w-full bg-[#FFFFFF17] backdrop-blur-3xl",
            openWishList && "sticky",
          )}
          ref={currentSongRef}
        >
          <div className="md:mx-3">
            <div id="desktop-youtube-player" />
            {currentSong &&
              (currentSong.type === ESongType.YOUTUBE && !isMobile ? (
                <div className="flex gap-2 items-center justify-center w-full py-3">
                  <img
                    src={
                      currentSong?.artists?.[0]?.images?.SMALL ||
                      currentSong?.artists?.[0]?.images?.DEFAULT ||
                      "/image/default-avatar.png"
                    }
                    className="aspect-square rounded-full object-cover w-[13%]"
                  />
                  <div className="flex flex-col gap-0.5 justify-start w-[87%]">
                    <span className="text-base font-medium text-white line-clamp-1">
                      {currentSong?.name}
                    </span>
                    <span className="text-sm font-normal text-[#FFFFFF80] line-clamp-1">
                      {currentSong?.artists?.[0]?.name}
                    </span>
                  </div>
                  <IconButton
                    className="song-action !p-0"
                    onClick={(e) => {
                      handleOpenMenu(e);
                      e.stopPropagation();
                    }}
                  >
                    <IconMoreHorizontal className="rotate-90" />
                  </IconButton>
                  <PopupMenu
                    data={currentSong}
                    menuArray={[
                      {
                        icon: (
                          <IconHeart
                            className={clsx(
                              currentSong.isLiked &&
                                "fill-[#FF0000] text-[#FF0000]",
                            )}
                          />
                        ),
                        label: t("common.like"),
                        action: () => handleLikeSong(currentSong),
                        isAuth: true,
                      },
                      {
                        icon: <IconAdd24px />,
                        label: t("common.menu.add_to_playlist"),
                        action: openAddToPlaylist,
                        isAuth: true,
                      },
                      {
                        icon: <IconShare />,
                        label: t("common.share"),
                        action: handleOpenModalShare,
                      },
                      {
                        icon: <DeleteOutlineRounded />,
                        label: t("common.delete"),
                        action: handleDeleteSongFromQueue,
                      },
                    ]}
                    anchorEl={menuAnchor}
                    open={Boolean(menuAnchor)}
                    onClose={handleCloseMenu}
                  ></PopupMenu>
                </div>
              ) : (
                <SongCard index={0} data={currentSong} isPlay={true} />
              ))}
          </div>
        </div>
        <div
          className={clsx(
            "md:px-3",
            (queueList?.length === 0 ||
              currentIndex === queueList?.length - 1) &&
              "hidden",
          )}
        >
          <h3 className="text-base font-semibold pt-4 md:pl-3 h-12 box-border">
            {t("common.play_next")}
          </h3>
        </div>
        <div className="flex flex-col w-full md:px-3">
          {queueList?.slice(currentIndex + 1)?.map((song: ISong, index) => {
            return (
              <div key={index}>
                <SongCard data={song} index={index} />
              </div>
            );
          })}
        </div>
        <div className="md:px-3 flex flex-row items-center justify-between">
          <div className="text-sm font-semibold pt-4 pl-3 h-12 box-border">
            {t("common.list_of_suggest_song")}
          </div>
          <GlobalSwitch checked={isSuggestionEnabled} onChange={handleChange} />
        </div>
        <div
          className={clsx(
            "flex flex-col w-full md:px-3",
            !isSuggestionEnabled && "hidden",
          )}
        >
          {suggestedSongs?.map((song: ISong, index) => {
            return (
              <div key={index}>
                <SongCard data={song} index={index} />
              </div>
            );
          })}
        </div>
      </div>
      <ModalAddToPlaylist
        open={addToPlaylistOpen}
        onClose={() => setAddToPlaylistOpen(false)}
        songData={currentSong}
      />
      <ModalShare
        open={openModalShare}
        onCancel={handleCloseModalShare}
        handleCopyLink={handleCopyLink}
        image={currentSong?.images?.SMALL || currentSong?.images?.DEFAULT}
        name={currentSong?.name}
        artists={currentSong?.artists}
        shareUrl={generateShareLink({type: "song", data: currentSong})}
      />
    </div>
  );
}
