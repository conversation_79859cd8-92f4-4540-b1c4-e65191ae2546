import ApiAuth from "@api/ApiAuth";
import {Button} from "@mui/material";
import {hideModal} from "@redux/slices/GlobalModalSlice";
import {loginUser} from "@redux/slices/UserSlice";
import {IRootState} from "@redux/store";
import {useMutation} from "@tanstack/react-query";
import {memo, useCallback, useEffect} from "react";
import {useTranslation} from "react-i18next";
import {useDispatch, useSelector} from "react-redux";
import {useNavigate} from "react-router-dom";
import {toast} from "react-toastify";

function Welcome() {
  const {t} = useTranslation();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const shouldViewUpgradePage = useSelector(
    (state: IRootState) => state.accType.shouldViewUpgradePage,
  );
  const navigateToUpgrade = () => {
    if (shouldViewUpgradePage === true) {
      navigate("/upgrade");
    }
  };

  const {mutateAsync, isPending} = useMutation({
    mutationFn: ApiAuth.login,
    onSuccess(data) {
      dispatch(loginUser(data));
      dispatch(hideModal());
      toast.success(t("common.login_success"));
      navigateToUpgrade();
    },
  });

  const onMessage = useCallback(
    (event: {data: {message?: string; data?: string}}) => {
      if (event.data.message === "login_success" && event.data.data) {
        mutateAsync({authorizationCode: event.data.data});
      }
    },
    [],
  );

  useEffect(() => {
    window.LaoIdSSO?.init(
      import.meta.env.VITE_LAOID_CLIENT_ID,
      import.meta.env.VITE_LAOID_REDIRECT_URI,
      false,
    );

    window.addEventListener("message", onMessage, false);
    return () => {
      window.removeEventListener("message", onMessage, false);
    };
  }, []);

  return (
    <div className="relative flex flex-col items-center justify-center bg-[linear-gradient(#252525,#151515)] w-[90vw] sm:w-[600px] rounded-xl">
      <div className="absolute top-0 left-0 w-full h-[182px] bg-cover bg-center bg-no-repeat rounded-t-xl bg-[linear-gradient(to_bottom,#20202000,#202020B0,#202020),url(/image/auth-background.png)]" />
      <div className="relative z-10 flex flex-col items-center justify-center mt-10 p-6 text-white">
        <div className="flex justify-center mb-6">
          <img src="/image/logo.png" alt="login logo" className="w-52" />
        </div>
        <h2 className="text-center font-bold text-white mb-4 md:text-[26px]">
          {t("common.experience_download_for_free")}
        </h2>
        <p className="text-center hidden sm:block text-[#A5A5A5] mb-6 max-w-3xl">
          {t("common.discover_amazing_melodies_and")}
        </p>

        <div className="space-y-4 w-full max-w-lg mt-2">
          <Button
            disabled={isPending}
            loading={isPending}
            id="laoid-signin"
            className="!py-3 !bg-orange-500 !text-base !text-white !font-semibold !rounded-xl !normal-case"
            fullWidth
            loadingPosition="end"
          >
            {t("auth.login")}
          </Button>
        </div>
      </div>
    </div>
  );
}

export default memo(Welcome);
