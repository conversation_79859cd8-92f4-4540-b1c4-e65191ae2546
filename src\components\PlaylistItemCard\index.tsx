import {Checkbox, IconButton} from "@mui/material";
import {useTranslation} from "react-i18next";
import {IPlaylist} from "src/types";

interface PlaylistItemCardProps {
  className?: string;
  data?: IPlaylist;
  checked?: boolean;
  onClick?: () => void;
}

export default function PlaylistItemCard({
  className,
  checked = false,
  data,
  onClick,
}: PlaylistItemCardProps): JSX.Element {
  const {t} = useTranslation();

  return (
    <div
      className={`w-full py-1 flex flex-row items-center justify-between px-3 ${className}`}
      onClick={onClick}
    >
      <div className="relative flex items-center gap-3">
        <img
          src={
            data?.images?.SMALL ||
            data?.images?.DEFAULT ||
            "/image/default-music.png"
          }
          alt={data?.name}
          className="h-[52px] w-[52px] object-cover rounded"
        />
        <div className="flex flex-col align-center">
          <div className="text-white font-normal text-base">{data?.name}</div>
          <div className="text-[#808080] font-normal text-sm">
            {data?.totalSongs != null && !isNaN(data.totalSongs)
              ? `${data.totalSongs} ${t("playlist.song")}`
              : "0 " + t("playlist.song")}
          </div>
        </div>
      </div>

      <div className="aspect-square justify-center items-center rounded-full">
        <IconButton>
          <Checkbox
            checked={checked}
            sx={{
              "position": "relative",
              "borderRadius": "8px",
              "color": "#D9D9D96E",
              "&.Mui-checked": {
                "color": "#FF4319",
                "&::before": {
                  content: '""',
                  width: "18px",
                  height: "18px",
                  position: "absolute",
                  borderRadius: "8px",
                  left: "50%",
                  top: "50%",
                  transform: "translate(-50%, -50%)",
                  background: "white",
                  zIndex: 0,
                  pointerEvents: "none",
                },
              },
              "& .MuiSvgIcon-root": {
                position: "relative",
                zIndex: 1,
              },
            }}
          />
        </IconButton>
      </div>
    </div>
  );
}
