import IconClose from "@components/Icon/IconClose";
import {Modal} from "@mui/material";

interface CUModalProps {
  open?: boolean;
  onClose?: () => void;
  children?: JSX.Element;
  title?: string;
  isLoading?: boolean;
  onOk?: () => void;
}
export default function CUModal({
  open = false,
  onClose,
  children,
  title,
}: CUModalProps) {
  return (
    <Modal sx={{outline: 0}} open={open} onClose={onClose}>
      <div className="absolute top-1/2 left-1/2 bg-white transform -translate-x-1/2 -translate-y-1/2 rounded-lg min-w-[367px] max-w-[572px] w-full">
        {/* Header */}
        <div className="py-4 px-6 flex justify-between items-center">
          <span className="text-base font-bold">{title}</span>
          <IconClose className="cursor-pointer" onClick={onClose} />
        </div>
        <hr className="bg-[#F0F0F0]" />
        {/* Content */}
        {children}
      </div>
    </Modal>
  );
}
