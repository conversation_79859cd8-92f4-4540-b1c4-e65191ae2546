import React, { useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import clsx from "clsx";
import { SwapVertRounded } from "@mui/icons-material";
import SongItem from "../SongItem";
import { ISong } from "../../types/song";
import { IRootState } from "../../store";
import "./index.scss";

interface TableSongItemV2Props {
  songs: ISong[];
  className?: string;
  showDuration?: boolean;
  showAlbumInfo?: boolean;
  showReleaseDate?: boolean;
  showNumber?: boolean;
  onPlaySong?: (song: ISong) => void;
  extraMenuActions?: {
    icon?: React.ReactElement;
    label: React.ReactElement | string;
    action?: (song: ISong) => void;
    isAuth?: boolean;
  }[];
  onSortSongs?: () => void;
}

export default function TableSongItemV2({
  songs,
  className,
  showDuration = true,
  showAlbumInfo = true,
  showReleaseDate = true,
  showNumber = true,
  onPlaySong,
  extraMenuActions,
  onSortSongs,
}: TableSongItemV2Props) {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { currentSong, paused, queueList } = useSelector(
    (state: IRootState) => state?.player,
  );

  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("asc");

  const sortedSongs = useMemo(() => {
    if (!songs) return [];
    return [...songs].sort((a, b) => {
      if (sortOrder === "asc") {
        return (a.name || "").localeCompare(b.name || "");
      } else {
        return (b.name || "").localeCompare(a.name || "");
      }
    });
  }, [songs, sortOrder]);

  const handleSortListSong = () => {
    setSortOrder(prev => prev === "asc" ? "desc" : "asc");
    onSortSongs?.();
  };

  const handlePlayMusic = (song: ISong) => {
    if (onPlaySong) {
      onPlaySong(song);
    }
  };

  return (
    <div className={clsx("table-song-item-v2 overflow-x-auto", className)}>
      <div className="table w-full border-collapse">
        {/* Table Header */}
        <div className="table-header-group">
          <div className="table-row border-b border-[rgba(255,255,255,0.1)]">
            {showNumber && (
              <div className="table-cell align-middle text-center px-2 py-3 font-bold text-white hidden md:table-cell w-16">
                <p className="whitespace-nowrap overflow-hidden text-ellipsis md:text-xl">
                  #
                </p>
              </div>
            )}
            
            <div className="table-cell align-middle px-2 py-3 font-bold text-white w-full">
              <div className="flex items-center gap-2 overflow-hidden whitespace-nowrap text-ellipsis">
                <p className="md:text-xl">{t("common.song")}</p>
                <SwapVertRounded
                  sx={{ "&:hover": { cursor: "pointer" } }}
                  onClick={handleSortListSong}
                />
              </div>
            </div>

            {showAlbumInfo && (
              <div className="hidden lg:table-cell align-middle px-2 py-3 font-bold text-white w-32">
                <p className="whitespace-nowrap overflow-hidden text-ellipsis md:text-xl">
                  {t("common.album")}
                </p>
              </div>
            )}

            {showReleaseDate && (
              <div className="hidden md:table-cell align-middle px-2 py-3 font-bold text-white w-32">
                <p className="whitespace-nowrap overflow-hidden text-ellipsis md:text-xl">
                  {t("common.release_date")}
                </p>
              </div>
            )}

            <div className="table-cell align-middle px-2 py-3 font-bold text-white text-right w-24">
              <p className="whitespace-nowrap overflow-hidden text-ellipsis md:text-xl">
                {showDuration && t("common.duration")}
              </p>
            </div>
          </div>
        </div>

        {/* Table Body */}
        <div className="table-row-group">
          {sortedSongs.map((song, index) => (
            <SongItem
              key={song.id}
              song={song}
              tableLayout={true}
              showNumber={showNumber}
              showDuration={showDuration}
              showAlbumInfo={showAlbumInfo}
              showReleaseDate={showReleaseDate}
              index={index}
              handlePlayMusic={() => handlePlayMusic(song)}
              extraMenuActions={extraMenuActions?.map(action => ({
                ...action,
                action: action.action ? () => action.action!(song) : undefined,
              }))}
            />
          ))}
        </div>
      </div>
    </div>
  );
}
