import {SVGProps} from "react";

export default function ICRefresh({
  width = 17,
  height = 19,
  ...props
}: SVGProps<SVGSVGElement>): JSX.Element {
  return (
    <svg
      width={width}
      height={height}
      viewBox={`0 0 ${width} ${height}`}
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M8.5 18.5C7.38333 18.5 6.34167 18.2959 5.375 17.8878C4.40833 17.4796 3.5625 16.9204 2.8375 16.2102C2.1125 15.5 1.54167 14.6714 1.125 13.7245C0.708333 12.7776 0.5 11.7571 0.5 10.6633H1.275C1.275 12.6224 1.97917 14.2918 3.3875 15.6714C4.79583 17.051 6.5 17.7408 8.5 17.7408C10.5 17.7408 12.2042 17.051 13.6125 15.6714C15.0208 14.2918 15.725 12.6224 15.725 10.6633C15.725 8.70408 15.0333 7.03469 13.65 5.6551C12.2667 4.27551 10.575 3.58571 8.575 3.58571H7.975L9.75 5.3L9.2 5.81429L6.5 3.1449L9.225 0.5L9.775 1.01429L7.925 2.82653H8.5C9.61667 2.82653 10.6583 3.03061 11.625 3.43878C12.5917 3.84694 13.4375 4.40612 14.1625 5.11633C14.8875 5.82653 15.4583 6.6551 15.875 7.60204C16.2917 8.54898 16.5 9.56939 16.5 10.6633C16.5 11.7571 16.2917 12.7776 15.875 13.7245C15.4583 14.6714 14.8875 15.5 14.1625 16.2102C13.4375 16.9204 12.5917 17.4796 11.625 17.8878C10.6583 18.2959 9.61667 18.5 8.5 18.5Z"
        fill={props.fill}
      />
    </svg>
  );
}
