import {createSlice, PayloadAction} from "@reduxjs/toolkit";

export enum EGlobalModal {
  AUTH_MODAL,
}

interface IGlobalModalState {
  currentModal?: EGlobalModal;
}

const initialState: IGlobalModalState = {};

const GlobalModalSlice = createSlice({
  name: "modal",
  initialState,
  reducers: {
    showModal(state, action: PayloadAction<EGlobalModal>) {
      state.currentModal = action.payload;
    },
    hideModal(state) {
      state.currentModal = undefined;
    },
  },
});

// Action creators are generated for each case reducer function
export const {showModal, hideModal} = GlobalModalSlice.actions;

export default GlobalModalSlice.reducer;
