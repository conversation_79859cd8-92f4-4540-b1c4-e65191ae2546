import ApiArtist from "@api/ApiArtist";
import QUERY_KEY from "@api/QueryKey";
import {IconButton, MenuItem, Select} from "@mui/material";
import {GridColDef, GridSortModel} from "@mui/x-data-grid";
import {
  keepPreviousData,
  useMutation,
  useQuery,
  useQueryClient,
} from "@tanstack/react-query";
import {useMemo, useState} from "react";
import {toast} from "react-toastify";
import IconAdd from "@components/Icon/IconAdd";
import ModalComfirm from "@components/ModalConfirm";
import SearchInput from "../components/SearchInput";
import CmsTable from "../components/CmsTable";
import {useTranslation} from "react-i18next";
import {CountryEnum, EArtistType, IArtist} from "src/types";
import IconCmsEdit from "@components/Icon/IconCmsEdit";
import IconCmsDelete from "@components/Icon/IconCmsDelete";
import GlobalButton from "@components/ButtonGlobal";
import {
  useArtistTypeOptions,
  useCountryOptions,
  useGenderOptions,
} from "src/utils/global";
import ArtistDetailModal from "./components/ArtistDetailModal";
import ModalEditArtist from "./components/ArtistDetailModal/component/ModalEditArtist";
import AddArtistModal from "./components/AddArtistModal";
import clsx from "clsx";
import {convertDate} from "src/utils/timeUtils";
import useDebounce from "src/hooks/useDebounce";

export default function ArtistsManage() {
  const {t} = useTranslation();
  const [page, setPage] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const [sortModel, setSortModel] = useState<GridSortModel>([]);
  const [searchText, setSearchText] = useState("");
  // const [isActive, setIsActive] = useState("");
  const [selectTypeArtist, setSelectTypeArtist] = useState("");
  const debounceSearchText = useDebounce(searchText);
  const [country, setCountry] = useState("");

  const [selectedArtistId, setSelectedArtistId] = useState<string | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isConfirmDeleteModalOpen, setIsConfirmDeleteModalOpen] =
    useState(false);

  const filterValues = useMemo(
    () => [debounceSearchText, country, selectTypeArtist],
    [debounceSearchText, country, selectTypeArtist],
  );

  // TODO: ẨN/ HIỂN
  // const [isStatusConfirmModalOpen, setIsStatusConfirmModalOpen] =
  //   useState(false);
  // const [pendingStatusChange, setPendingStatusChange] = useState<{
  //   id: string;
  //   newStatus: string;
  // } | null>(null);
  const genderOptions = useGenderOptions();
  const artistTypeOptions = useArtistTypeOptions();
  const countryOptions = useCountryOptions();
  // const statusArtistOptions = useStatusArtistOptions();

  // TODO: Phase 2
  // const [isModalNotifyOpen, setIsModalNotifyOpen] = useState(false);

  const queryClient = useQueryClient();

  const deleteArtistMutation = useMutation({
    mutationFn: (id: string) => ApiArtist.deleteArtist(id),
    onSuccess: (id) => {
      queryClient.invalidateQueries({queryKey: ["artists"]});
      queryClient.setQueryData(
        [QUERY_KEY.ARTIST.DELETE_ARTIST_CMS],
        (oldData: any) => {
          if (!oldData) return oldData;
          return {
            ...oldData,
            data: oldData.data.filter((user: any) => user.id !== id),
          };
        },
      );
      toast.success(t("cms.artist.delete_artist_success"));
      refetch();
    },
    onError: () => {
      toast.error(t("cms.artist.delete_artist_fail"));
    },
  });

  // TODO: ẨN/ HIỂN
  // const updateArtistMutation = useMutation({
  //   mutationFn: ApiArtist.updateArtist,
  // });

  const {
    data: getListArtist,
    isPending,
    refetch,
  } = useQuery({
    queryKey: [
      QUERY_KEY.ARTIST.GET_ALL_ARTIST_CMS,
      page,
      pageSize,
      sortModel,
      country,
      debounceSearchText,
      // isActive,
      selectTypeArtist,
    ],
    placeholderData: keepPreviousData,
    queryFn: () =>
      ApiArtist.getAllArtist({
        page,
        pageSize,
        keyword: debounceSearchText,
        // status: isActive as unknown as EActivityStatus | undefined,
        artistType: selectTypeArtist as unknown as EArtistType | undefined,
        direction: sortModel[0]?.sort || "",
        country: country,
      }),
  });

  const tableRows = getListArtist?.data?.map((artist: IArtist) => ({
    id: artist?.id,
    images: artist?.images,
    name: artist?.name ?? "-",
    stageName: artist?.stageName ?? "-",
    birthday: artist?.user?.dateOfBirth,
    gender: artist?.user?.gender ?? "-",
    country: artist?.user?.countryName ?? "-",
    company: artist?.company ?? "-",
    status: artist?.user?.status ?? "-",
    artistType: artist?.type ?? "-",
    countSong: artist?.totalSongs ?? "0",
    quantityAlbum: artist?.totalPlaylists ?? "0",
  }));

  const handleRowClick = (artistId: string) => {
    setSelectedArtistId(artistId);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setSelectedArtistId(null);
    setIsModalOpen(false);
  };

  const handleOpenModalEdit = (artistId: string) => {
    setSelectedArtistId(artistId);
    setIsEditModalOpen(true);
  };

  const handleCloseModalEdit = () => {
    setSelectedArtistId(null);
    setIsEditModalOpen(false);
  };

  const {data: dataArtist, isFetching: fetcherDataArtist} = useQuery({
    queryKey: [QUERY_KEY.ARTIST.GET_ARTIST_DETAIL_CMS, selectedArtistId],
    queryFn: () =>
      selectedArtistId ? ApiArtist.getArtist(selectedArtistId) : null,
    enabled: !!selectedArtistId,
  });

  const handleDeleteArtist = (artistId: string) => {
    setSelectedArtistId(artistId);
    setIsConfirmDeleteModalOpen(true);
  };

  const confirmDeleteArtist = () => {
    if (selectedArtistId) {
      deleteArtistMutation.mutate(selectedArtistId);
      setIsConfirmDeleteModalOpen(false);
      setSelectedArtistId(null);
    }
  };

  // TODO: ẨN / HIỂN;
  // const handleStatusChange = (id: string, newStatus: boolean) => {
  //   setSelectedArtistId(id);
  //   setPendingStatusChange({
  //     id,
  //     newStatus: newStatus ? "ACTIVE" : "INACTIVE",
  //   });
  //   setIsStatusConfirmModalOpen(true);
  // };

  // const confirmStatusChange = () => {
  //   if (pendingStatusChange) {
  //     updateArtistMutation.mutateAsync(
  //       {id: pendingStatusChange.id, status: pendingStatusChange.newStatus},
  //       {
  //         onSuccess: () => {
  //           toast.success(t("cms.artist.change_switch_success"));
  //           refetch();
  //         },
  //         onError: () => {
  //           toast.error(t("cms.artist.change_switch_fail"));
  //         },
  //         onSettled: () => {
  //           setIsStatusConfirmModalOpen(false);
  //           setPendingStatusChange(null);
  //         },
  //       },
  //     );
  //   }
  // };
  // const cancelStatusChange = () => {
  //   setIsStatusConfirmModalOpen(false);
  //   setPendingStatusChange(null);
  //   setSelectedArtistId(null);
  // };

  // TODO: Phase 2
  // const handleOpenModalNotify = (artistId: string) => {
  //   setSelectedArtistId(artistId);
  //   setIsModalNotifyOpen(true);
  // };

  // const handleCloseModalNotify = () => {
  //   setSelectedArtistId(null);
  //   setIsModalNotifyOpen(false);
  // };

  const columns: GridColDef[] = [
    {
      field: "images",
      headerName: t("common.cover_img"),
      width: 134,
      sortable: false,
      headerAlign: "center",
      align: "center",
      renderCell: (params) => (
        <div className="w-full h-full flex justify-center items-center">
          <img
            src={
              params.value.SMALL ||
              params.value.DEFAULT ||
              "/image/default-music.png"
            }
            className="h-12 w-12 rounded-[4px] object-cover"
          />
        </div>
      ),
    },
    {
      field: "name",
      headerName: t("cms.artist.artist_name"),
      minWidth: 30,
      width: 190,
      sortable: false,
      disableColumnMenu: true,
      renderCell: (params) => <span>{params?.value}</span>,
    },
    {
      field: "stageName",
      headerName: t("cms.artist.stage_name"),
      minWidth: 30,
      width: 190,
      sortable: false,
      disableColumnMenu: true,
      renderCell: (params) => <span>{params?.value}</span>,
    },
    {
      field: "birthday",
      headerName: t("common.birthday"),
      minWidth: 30,
      width: 160,
      sortable: false,
      disableColumnMenu: true,
      renderCell: (params) => <span>{convertDate(params?.value)}</span>,
    },
    {
      field: "gender",
      headerName: t("common.gender"),
      minWidth: 30,
      width: 190,
      sortable: false,
      renderCell: (params) => {
        const genderLabel =
          genderOptions.find((option) => option.value === params?.value)
            ?.label || "-";

        return <span>{genderLabel}</span>;
      },
    },
    {
      field: "artistType",
      headerName: t("cms.artist.artist_type"),
      minWidth: 30,
      width: 190,
      sortable: false,
      renderCell: (params) => {
        const artistLabel =
          artistTypeOptions.find((option) => option.value === params?.value)
            ?.label || "-";

        return <span className="line-clamp-2">{artistLabel}</span>;
      },
    },
    {
      field: "country",
      headerName: t("common.country"),
      minWidth: 30,
      width: 190,
      sortable: false,
      disableColumnMenu: true,
      renderCell: (params) => {
        const countryLabel =
          countryOptions.find((option) => option.value === params?.value)
            ?.label || params?.value;

        return <span>{countryLabel}</span>;
      },
    },
    {
      field: "company",
      headerName: t("cms.artist.management_company"),
      minWidth: 30,
      width: 190,
      sortable: false,
      disableColumnMenu: true,
      renderCell: (params) => <span>{params?.value}</span>,
    },
    {
      field: "countSong",
      headerName: t("common.song_number"),
      minWidth: 30,
      width: 160,
      sortable: false,
      renderCell: (params) => <span>{params?.value}</span>,
    },
    {
      field: "quantityAlbum",
      headerName: t("cms.artist.quantity_album"),
      minWidth: 30,
      width: 160,
      sortable: false,
      renderCell: (params) => <span>{params?.value}</span>,
    },
    //   TODO: Trạng thái hoạt động nghệ sĩ
    // {
    //   field: "status",
    //   headerName: t("common.account_status"),
    //   minWidth: 30,
    //   width: 190,
    //   sortable: false,
    //   renderCell: (params) => {
    //     const {colorStatus, textStatus} = getStatusArtist(params?.value);

    //     return (
    //       <span
    //         style={{color: colorStatus}}
    //         className="flex gap-2 items-center"
    //       >
    //         <span className="text-lg">•</span>
    //         <span>{textStatus}</span>
    //       </span>
    //     );
    //   },
    // },
    {
      field: "actions",
      headerName: t("common.actions"),
      minWidth: 30,
      width: 120,
      sortable: false,
      disableColumnMenu: true,
      align: "center",
      headerAlign: "center",
      headerClassName: "sticky-header",
      cellClassName: "sticky-cell",
      renderCell: (params) => (
        <div className="flex justify-center items-center">
          {/* <CustomTooltip
            title={
              params?.row?.status === EActivityStatus.ACTIVE
                ? t("cms.artist.hide_singer")
                : t("cms.artist.display_singer")
            }
          >
            <IconButton
              onClick={(e) => {
                e.preventDefault();
                // handleStatusChange(
                //   params?.row?.id,
                //   params?.row?.status !== EActivityStatus.ACTIVE,
                // );
              }}
            >
              {params?.row?.status === EActivityStatus.ACTIVE ? (
                <IconLockKeyOpen />
              ) : (
                <IconLockKey />
              )}
            </IconButton>
          </CustomTooltip> */}
          <IconButton
            onClick={() => {
              handleOpenModalEdit(params?.row?.id);
            }}
          >
            <IconCmsEdit />
          </IconButton>
          <IconButton
            onClick={() => {
              handleDeleteArtist(params?.row?.id);
            }}
          >
            <IconCmsDelete />
          </IconButton>

          {/* TODO: Phase 2 */}
          {/* <IconButton
            onClick={() => {
              handleOpenModalNotify(params?.row?.id);
            }}
          >
            <IconCmsSend />
          </IconButton> */}
        </div>
      ),
    },
  ];

  return (
    <div className="p-5 bg-white space-y-4 rounded-[20px]">
      <div className="flex justify-between sm:items-center sm:flex-row flex-col sm:gap-0 gap-3 items-start">
        <div className="flex gap-3 flex-wrap w-full">
          <SearchInput
            searchText={searchText}
            className="py-[5px]"
            onChange={(v) => setSearchText(v)}
            placeholder={t("cms.artist.artist_real_name")}
          />

          <Select
            value={country}
            onChange={(e) => setCountry(e.target.value)}
            size="small"
            displayEmpty
            sx={{borderRadius: "8px", fontWeight: "800", fontSize: "14px"}}
            className="cms-select-gray h-10 w-[200px]"
            inputProps={{"aria-label": "Without label"}}
            renderValue={(selected) => {
              if (selected !== "") {
                return (
                  <span className="text-sm">
                    {countryOptions.find(
                      (option) =>
                        option.value === (selected as unknown as CountryEnum),
                    )?.label || selected}
                  </span>
                );
              } else {
                return (
                  <span
                    className={clsx("text-[#242728] font-semibold text-sm")}
                  >
                    {t("common.country")}
                  </span>
                );
              }
            }}
          >
            <MenuItem value="">{t("common.clear_all")}</MenuItem>
            {countryOptions.map((type) => (
              <MenuItem key={type.value} value={type.value}>
                {type.label}
              </MenuItem>
            ))}
          </Select>

          <Select
            value={selectTypeArtist}
            onChange={(e) => setSelectTypeArtist(e.target.value)}
            size="small"
            displayEmpty
            sx={{borderRadius: "8px", fontWeight: "800", fontSize: "14px"}}
            className="cms-select-gray h-10 w-[200px]"
            inputProps={{"aria-label": "Without label"}}
            renderValue={(selected) => {
              if (selected !== "") {
                return (
                  <span className="text-sm">
                    {artistTypeOptions.find(
                      (option) =>
                        option.value === (selected as unknown as EArtistType),
                    )?.label || selected}
                  </span>
                );
              } else {
                return (
                  <span
                    className={clsx("text-[#242728] font-semibold text-sm")}
                  >
                    {t("cms.artist.artist_type")}
                  </span>
                );
              }
            }}
          >
            <MenuItem value="">{t("common.clear_all")}</MenuItem>
            {artistTypeOptions.map((type) => (
              <MenuItem key={type.value} value={type.value}>
                {type.label}
              </MenuItem>
            ))}
          </Select>

          {/* TODO: Trạng thái hoạt động nghệ sĩ */}
          {/* <Select
            value={isActive}
            onChange={(e) => setIsActive(e.target.value)}
            size="small"
            displayEmpty
            sx={{borderRadius: "8px", fontWeight: "600", fontSize: "14px"}}
            className="cms-select-gray h-10 w-[200px]"
            inputProps={{"aria-label": "Without label"}}
            renderValue={(selected) => {
              if (selected !== "") {
                return (
                  <span className="text-sm">
                    {statusArtistOptions.find(
                      (option) =>
                        option.value ===
                        (selected as unknown as EActivityStatus),
                    )?.label || selected}
                  </span>
                );
              } else {
                return (
                  <span
                    className={clsx("text-[#242728] font-semibold text-sm")}
                  >
                    {t("common.operating_status")}
                  </span>
                );
              }
            }}
          >
            <MenuItem value="">{t("common.clear_all")}</MenuItem>
            {statusArtistOptions.map((status) => (
              <MenuItem key={status.value} value={status.value}>
                {status.label}
              </MenuItem>
            ))}
          </Select> */}
        </div>
        <GlobalButton
          text={t("cms.song.btn_add")}
          startIcon={<IconAdd />}
          className="w-auto whitespace-nowrap"
          onClick={() => setIsAddModalOpen(true)}
        />
      </div>

      <CmsTable
        rows={tableRows}
        ordinalColumn
        columns={columns}
        loading={isPending}
        totalItems={getListArtist?.meta?.totalItems || 0}
        currentPage={page}
        onPageChange={(newPage) => setPage(newPage)}
        rowsPerPage={pageSize}
        onRowsPerPageChange={(newPageSize) => setPageSize(newPageSize)}
        onSortModelChange={(model) => setSortModel(model)}
        onRowDoubleClick={(params) => handleRowClick(params.row.id)}
        filterValues={filterValues}
        hideFooter
      />

      <ArtistDetailModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        dataArtist={dataArtist ?? ({} as IArtist)}
        artistId={selectedArtistId}
        fetcherDataArtist={fetcherDataArtist}
      />

      <ModalEditArtist
        open={isEditModalOpen}
        onClose={handleCloseModalEdit}
        artistId={selectedArtistId}
        refetchList={refetch}
      />

      <AddArtistModal
        isOpen={isAddModalOpen}
        onClose={() => {
          setIsAddModalOpen(false);
        }}
        refetchList={refetch}
      />

      {/* TODO: ẨN/ HIỂN */}
      {/* <ModalComfirm
        open={isStatusConfirmModalOpen}
        onConfirm={confirmStatusChange}
        onCancel={cancelStatusChange}
        title={
          pendingStatusChange?.newStatus === "ACTIVE"
            ? t("cms.artist.display_singer")
            : t("cms.artist.hide_singer")
        }
        loading={fetcherDataArtist || updateArtistMutation.isPending}
      >
        <div className="flex items-center justify-center py-6 px-6 gap-1">
          <span className="font-normal text-xl">
            {pendingStatusChange?.newStatus === "ACTIVE"
              ? t("cms.artist.confirm_show_artist")
              : t("cms.artist.question_hide_singer")}
          </span>
          <img
            src={dataArtist?.image ?? "/image/default-avatar.png"}
            className="w-5 h-5 border-[1.5px] border-solid border-[#262626] rounded-full"
          />
          <span className="font-semibold text-base">
           {dataArtist?.stageName ??
              dataArtist?.name ??
              dataArtist?.user?.username ??
              "-"}
          </span>
          <span className="font-normal text-xl">?</span>
        </div>
      </ModalComfirm> */}

      <ModalComfirm
        open={isConfirmDeleteModalOpen}
        onConfirm={confirmDeleteArtist}
        onCancel={() => setIsConfirmDeleteModalOpen(false)}
        title={t("cms.artist.delete_singer")}
        loading={fetcherDataArtist || deleteArtistMutation.isPending}
      >
        <div className="flex items-center justify-center py-6 px-6 gap-1">
          <span className="font-normal text-xl">
            {t("cms.artist.question_delete_singer")}
          </span>
          <img
            src={
              dataArtist?.images?.SMALL ||
              dataArtist?.images?.DEFAULT ||
              "/image/default-avatar.png"
            }
            className="w-5 h-5 border-[1.5px] border-solid border-[#262626] rounded-full object-cover"
          />
          <span className="font-semibold text-base">
            {dataArtist?.stageName ??
              dataArtist?.name ??
              dataArtist?.user?.username ??
              "-"}
          </span>
          <span className="font-normal text-xl">?</span>
        </div>
      </ModalComfirm>

      {/* TODO: Phase 2 */}
      {/* <ModalNotify
        open={isModalNotifyOpen}
        onCancel={handleCloseModalNotify}
        onSubmit={handleCloseModalNotify}
        artistId={selectedArtistId}
      /> */}
    </div>
  );
}
