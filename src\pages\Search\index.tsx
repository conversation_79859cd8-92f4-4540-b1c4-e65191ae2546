import {useEffect, useState} from "react";
import {useLocation, useNavigate, useParams} from "react-router-dom";
import {useTranslation} from "react-i18next";
import {styled} from "@mui/system";
import {buttonClasses} from "@mui/base/Button";
import {Tabs} from "@mui/base/Tabs";
import {Tab as BaseTab, tabClasses} from "@mui/base/Tab";
import {TabPanel as BaseTabPanel} from "@mui/base/TabPanel";
import {TabsList as BaseTabsList} from "@mui/base/TabsList";
import {AllView} from "./AllView";
import {SongsView} from "./SongsView";
import {ArtistsView} from "./ArtistsView";
import {PlaylistsView} from "./PlaylistsView";
import {YoutubeView} from "./YoutubeView";
import {Divider} from "@mui/material";
import {PlaylistType} from "src/types";
import HeaderTitle from "@components/HeaderTitle";
import {YoutubePlaylistView} from "./YoutubePlaylistView";

export default function Search() {
  const {t} = useTranslation();
  const location = useLocation();
  const navigate = useNavigate();
  const {tab} = useParams();
  const query = new URLSearchParams(location.search);
  const searchValue = query.get("q") || "";

  const tabValueMap: Record<string, number> = {
    "all": 1,
    "songs": 2,
    "artists": 3,
    "albums": 4,
    "playlists": 5,
    "youtube": 6,
    "youtube-playlist": 7,
  };

  const tabNameMap: Record<number, string> = Object.fromEntries(
    Object.entries(tabValueMap).map(([key, value]) => [value, key]),
  ) as Record<number, string>;

  const [activeTab, setActiveTab] = useState(tabValueMap[tab || "all"] || 1);

  useEffect(() => {
    setActiveTab(tabValueMap[tab || "all"] || 1);
  }, [tab]);

  const handleTabChange = (newValue: string | number | null) => {
    if (typeof newValue === "number") {
      const tabName = tabNameMap[newValue];
      setActiveTab(newValue);
      navigate(`/search/${tabName}?q=${encodeURIComponent(searchValue)}`);
    }
  };

  if (!searchValue) return null;

  return (
    <div className="py-5 px-4 sm:px-6 md:px-8">
      <HeaderTitle title={t("common.search")} />
      {searchValue ? (
        <Tabs value={activeTab} onChange={(_, value) => handleTabChange(value)}>
          <div
            className={`flex items-center gap-2 transition-all duration-300 ease-in-out flex-row max-[1330px]:flex-col max-[1330px]:items-start`}
          >
            <div className={`flex gap-2 mb-2 items-center`}>
              <span className="text-lg text-white font-bold hidden lg:block">
                {t("search.search_result")}
              </span>
              <Divider
                orientation="vertical"
                className={`bg-[#FFFFFF36] w-0.5 hidden lg:block max-[1330px]:!hidden`}
                sx={{height: 16}}
              />
            </div>
            <TabsList className={`transition-all duration-300 ease-in-out`}>
              <Tab value={1}>{t("common.all")}</Tab>
              <Tab value={2}>{t("common.songs")}</Tab>
              <Tab value={3}>{t("common.artist")}</Tab>
              <Tab value={4}>{t("common.album")}</Tab>
              <Tab value={5}>{t("common.playlist")}</Tab>
              <Tab value={6}>Youtube MV</Tab>
              <Tab value={7}>{t("common.youtube_playlist")}</Tab>
            </TabsList>
          </div>
          <TabPanel value={1}>
            <AllView
              onSongViewClick={() => handleTabChange(2)}
              onArtistViewClick={() => handleTabChange(3)}
              onAlbumViewClick={() => handleTabChange(4)}
              onPlaylistViewClick={() => handleTabChange(5)}
              onYoutubeViewClick={() => handleTabChange(6)}
              onYoutubePlaylistViewClick={() => handleTabChange(7)}
              searchValue={searchValue}
            />
          </TabPanel>
          <TabPanel value={2}>
            <SongsView />
          </TabPanel>
          <TabPanel value={3}>
            <ArtistsView />
          </TabPanel>
          <TabPanel value={4}>
            <PlaylistsView type={PlaylistType.ALBUM} />
          </TabPanel>
          <TabPanel value={5}>
            <PlaylistsView type={PlaylistType.PLAYLIST} />
          </TabPanel>
          <TabPanel value={6}>
            <YoutubeView />
          </TabPanel>
          <TabPanel value={7}>
            <YoutubePlaylistView />
          </TabPanel>
        </Tabs>
      ) : (
        <></>
      )}
    </div>
  );
}

const Tab = styled(BaseTab)`
  color: white;
  cursor: pointer;
  font-size: 16px;
  background-color: #ffffff1a;
  padding: 8px 24px;
  margin-right: 12px;
  border: none;
  border-radius: 20px;
  display: flex;
  justify-content: center;
  margin-bottom: 8px;

  &:last-child {
    margin-right: 0;
  }

  &:hover {
    background-color: bg-[#FFFFFF14];
  }

  &.${tabClasses.selected} {
    background-color: #ff4319;
    color: #ffffff;
  }

  &.${buttonClasses.disabled} {
    opacity: 0.5;
    cursor: not-allowed;
  }

  @media (max-width: 640px) {
    padding: 6px 16px;
  }
`;

const TabPanel = styled(BaseTabPanel)`
  width: 100%;
  font-size: 0.875rem;
  color: #ffffff;
  padding: 16px 0;

  @media (max-width: 1239px) {
    padding: 8px 0;
  }
`;

const TabsList = styled(BaseTabsList)(
  () => `
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  `,
);
