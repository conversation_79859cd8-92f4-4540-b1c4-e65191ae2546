import {useState} from "react";
import {useTranslation} from "react-i18next";
import {styled} from "@mui/system";
import {buttonClasses} from "@mui/base/Button";
import {Tabs} from "@mui/base/Tabs";
import {Tab as BaseTab, tabClasses} from "@mui/base/Tab";
import {TabPanel as BaseTabPanel} from "@mui/base/TabPanel";
import {TabsList as BaseTabsList} from "@mui/base/TabsList";
import {Divider} from "@mui/material";
import {PlaylistType} from "src/types";
import SongRecent from "./components/SongRecent";
import PlaylistRecent from "./components/PlaylistRecent";
import YoutubeRecent from "./components/YoutubeRecent";
import {useSelector} from "react-redux";
import {IRootState} from "@redux/store";
import HeaderTitle from "@components/HeaderTitle";

export default function LibraryRecent() {
  const {t} = useTranslation();
  const isOpen = useSelector((state: IRootState) => state?.wishlist?.isOpen);

  const [activeTab, setActiveTab] = useState(1);

  return (
    <div className="py-5 px-4 sm:px-6 md:px-8">
      <HeaderTitle title={t("common.home_sidebar.recent")} />
      <Tabs
        value={activeTab}
        onChange={(_e, newValue) => setActiveTab(Number(newValue))}
      >
        <div
          className={`flex items-center gap-2 transition-all duration-300 ease-in-out ${
            isOpen
              ? "max-[1270px]:flex-col max-[1270px]:items-start"
              : "flex-row"
          }`}
        >
          <div
            className={`flex gap-2 mb-2 items-center ${
              isOpen ? "max-[1270px]:!mb-0" : ""
            }`}
          >
            <span className="text-lg text-white font-bold hidden lg:block">
              {t("common.library_recent")}
            </span>
            <Divider
              orientation="vertical"
              className={`bg-[#FFFFFF36] w-0.5 hidden lg:block ${
                isOpen ? "max-[1270px]:!hidden" : ""
              }`}
              sx={{height: 16}}
            />
          </div>
          <TabsList
            className={`transition-all duration-300 ease-in-out ${
              isOpen
                ? "max-[1270px]:translate-y-1"
                : "max-[1270px]:translate-y-0"
            }`}
          >
            <Tab value={1}>{t("common.songs")}</Tab>
            <Tab value={2}>{t("common.album")}</Tab>
            <Tab value={3}>{t("common.playlist")}</Tab>
            <Tab value={4}>Youtube MV</Tab>
          </TabsList>
        </div>
        <TabPanel value={1}>
          <SongRecent />
        </TabPanel>
        <TabPanel value={2}>
          <PlaylistRecent type={PlaylistType.ALBUM} />
        </TabPanel>
        <TabPanel value={3}>
          <PlaylistRecent type={PlaylistType.PLAYLIST} />
        </TabPanel>
        <TabPanel value={4}>
          <YoutubeRecent />
        </TabPanel>
      </Tabs>
    </div>
  );
}

const Tab = styled(BaseTab)`
  color: white;
  cursor: pointer;
  font-size: 16px;
  background-color: #ffffff1a;
  padding: 8px 24px;
  margin-right: 12px;
  border: none;
  border-radius: 20px;
  display: flex;
  justify-content: center;
  margin-bottom: 8px;

  &:last-child {
    margin-right: 0;
  }

  &:hover {
    background-color: bg-[#FFFFFF14];
  }

  &.${tabClasses.selected} {
    background-color: #ff4319;
    color: #ffffff;
  }

  &.${buttonClasses.disabled} {
    opacity: 0.5;
    cursor: not-allowed;
  }

  @media (max-width: 640px) {
    padding: 6px 16px;
  }
`;

const TabPanel = styled(BaseTabPanel)`
  width: 100%;
  font-size: 0.875rem;
  color: #ffffff;
  padding: 16px 0;

  @media (max-width: 1024px) {
    padding: 8px 0;
  }
`;

const TabsList = styled(BaseTabsList)(
  () => `
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  `,
);
