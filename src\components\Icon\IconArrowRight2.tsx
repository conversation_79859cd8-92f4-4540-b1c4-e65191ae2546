import {SVGProps} from "react";

function IconArrowRight2({
  width = 8,
  height = 14,
  ...props
}: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      fill="none"
      viewBox={`0 0 ${width} ${height}`}
      {...props}
    >
      <path
        d="M1 13L7 7L1 1"
        stroke={props.fill || "currentColor"}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}

export default IconArrowRight2;
