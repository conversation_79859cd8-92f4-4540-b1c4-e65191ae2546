import HeaderTitle from "@components/HeaderTitle";
import Icon404CMS from "@components/Icon/Icon404CMS";
import {useTranslation} from "react-i18next";
import {useNavigate} from "react-router-dom";
interface NotFoundProps {
  redirectTo?: string;
}

export default function NotFoundCMS({redirectTo}: NotFoundProps) {
  const navigate = useNavigate();
  const {t} = useTranslation();

  if (redirectTo) {
    window.location.pathname = redirectTo;
    return null;
  }

  return (
    <div
      id="error-page"
      className="flex gap-4 flex-col items-center justify-center"
    >
      <HeaderTitle />
      <div className="flex flex-col justify-center items-center gap-2 sm:w-[500px]">
        <Icon404CMS className="w-full h-full" />
        <span className="text-[#2d2d2d] sm:text-base text-sm">
          {t("404.not_found_page")}
        </span>
      </div>
      <button
        className="text-orange-500 sm:text-base text-sm"
        onClick={() => navigate("/cms")}
      >
        {t("404.back_to_home")}
      </button>
    </div>
  );
}
