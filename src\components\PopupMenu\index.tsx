import AuthButtonWrapper from "@components/AuthButton/AuthButtonWrapper";
import LikeButton from "@components/AuthButton/LikeButton";
import IconArrowLeft from "@components/Icon/IconArrowLeft";
import IconClose from "@components/Icon/IconClose";
import IconPlaylist2 from "@components/Icon/IconPlaylist2";
import IconPlaylistDuotone from "@components/Icon/IconPlaylistDuotone";
import ModalLyric from "@components/ModalLyric";
import {Divider, Menu, MenuItem, MenuProps, Popover} from "@mui/material";
import {toggleWishlist} from "@redux/slices/WishlistSlice";
import {IRootState} from "@redux/store";
import clsx from "clsx";
import React, {useMemo, useState} from "react";
import {useTranslation} from "react-i18next";
import {useDispatch, useSelector} from "react-redux";
import {ESongType, ISong} from "src/types";
import {useWindowWidth} from "src/utils/hooks";
import {handleLikeSong} from "src/utils/like";
import {shortenNumber} from "src/utils/numberUtils";

export interface IPopupMenuItem {
  icon?: React.ReactElement;
  label: React.ReactElement | string;
  action?: () => void;
  subMenu?: IPopupMenuItem[];
  isAuth?: boolean;
  isDisabled?: boolean;
}

interface IPopupMenu extends MenuProps {
  menuArray: IPopupMenuItem[];
  onClose?: () => void;
  data: ISong;
}

export default function PopupMenu({
  menuArray,
  anchorEl,
  onClose,
  data,
  ...props
}: IPopupMenu) {
  const [submenuAnchor, setSubmenuAnchor] = useState<null | HTMLElement>(null);
  const [submenuItems, setSubmenuItems] = useState<IPopupMenuItem[] | null>(
    null,
  );
  const [openModalLyric, setOpenModalLyric] = useState(false);
  const {t} = useTranslation();
  const dispatch = useDispatch();
  const isWishlistOpen = useSelector(
    (state: IRootState) => state.wishlist.isOpen,
  );
  const currentSong = useSelector(
    (state: IRootState) => state.player.currentSong,
  );

  const handleClose = (e: React.MouseEvent) => {
    e.stopPropagation();
    onClose?.();
    setSubmenuAnchor(null);
    setSubmenuItems(null);
  };

  const handleTogglePlaylist = (e: React.MouseEvent) => {
    handleClose(e);
    if (!isWishlistOpen) {
      dispatch(toggleWishlist());
    }
  };

  const handleCloseLyric = (e: React.MouseEvent) => {
    e.stopPropagation();
    setOpenModalLyric(false);
  };

  const handleOpenLyric = (e: React.MouseEvent) => {
    handleClose(e);
    setOpenModalLyric(true);
  };

  const width = useWindowWidth();
  const isMobile = useMemo(() => width <= 834, [width]);

  return (
    <>
      <Menu
        keepMounted
        onClose={handleClose}
        anchorEl={anchorEl}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "right",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "right",
        }}
        slotProps={{
          paper: {
            style: {
              minWidth: "200px",
              width: "375px",
              background: "#1C1717",
              color: "#ffffffcc",
              boxShadow: "0px 10px 15px 0px #1B28361A",
              borderRadius: "12px",
              padding: "20px 16px",
            },
          },
          list: {
            style: {
              padding: 0,
              display: "flex",
              flexDirection: "column",
              gap: "8px",
            },
          },
        }}
        {...props}
      >
        <div className="flex gap-x-[10px] justify-between mb-[6px]">
          <img
            className="h-[50px] object-cover rounded-[4px] aspect-square"
            src={
              data?.images?.SMALL ||
              data?.images?.DEFAULT ||
              "/image/default-music.png"
            }
            alt=""
          />
          <div className="flex-1">
            <p className="line-clamp-1">{data?.name}</p>
            <div className="flex gap-[8px] items-center">
              <LikeButton
                isLiked={
                  data?.id === currentSong?.id
                    ? currentSong.isLiked
                    : data?.isLiked || false
                }
                songId={data?.id}
                className={clsx(
                  "song-action",
                  !isMobile && "hover:rounded-full",
                )}
                classNameIcon="h-[18px] w-[18px] text-[#A9A9A9]"
                action={(e) => {
                  e.stopPropagation();
                  handleLikeSong(data);
                }}
              />
              <p
                className={`text-[#A9A9A9] total-likes song-${data.id}-liking`}
              >
                {shortenNumber(data?.totalLikes || 0)}
              </p>
            </div>
          </div>
          <div>
            <IconClose
              className="cursor-pointer"
              fill="#fff"
              onClick={(e) => handleClose(e)}
            />
          </div>
        </div>
        <Divider className="bg-[#FFFFFF36]" />

        <div className="flex justify-around items-center rounded-lg px-4 py-2 bg-[rgba(255,255,255,0.1)] h-[78px] my-[6px]">
          <button
            className={clsx(
              "w-1/2 flex flex-col text-white items-center justify-center gap-2 cursor-pointer ",
              {
                "!cursor-not-allowed !text-gray-500":
                  data.type === ESongType.YOUTUBE,
              },
            )}
            onClick={handleOpenLyric}
            disabled={data.type === ESongType.YOUTUBE}
          >
            <IconPlaylist2 width={24} height={24} />
            <p>{t("common.lyrics")}</p>
          </button>
          <button
            className={clsx(
              "w-1/2 flex flex-col text-white items-center justify-center gap-2 cursor-pointer ",
            )}
            onClick={handleTogglePlaylist}
          >
            <IconPlaylistDuotone width={24} height={24} />
            <p>{t("common.menu.playlist")}</p>
          </button>
        </div>

        {menuArray.map((item, index) => {
          const menuItem = (
            <MenuItem
              key={`menu_item_${index}`}
              sx={{
                "display": "flex",
                "gap": "8px",
                "alignItems": "center",
                "padding": "8px",
                "minWidth": "200px",
                "borderRadius": "4px",
                "justifyContent": "space-between",
                ":hover": {
                  backgroundColor: "#ffffff0d",
                },
              }}
              onClick={(e) => {
                if (!item.subMenu) {
                  onClose?.();
                  setSubmenuAnchor(null);
                  setSubmenuItems(null);
                } else {
                  setSubmenuAnchor(e.currentTarget);
                  setSubmenuItems(item.subMenu);
                }
              }}
            >
              <div className="flex gap-2 items-center">
                {item.icon}
                <span className="text-sm leading-none">{item.label}</span>
              </div>
              {item?.subMenu && <IconArrowLeft className="rotate-180" />}
            </MenuItem>
          );

          return item.isAuth ? (
            <AuthButtonWrapper
              key={`button_auth_${index}`}
              action={item.action}
            >
              {menuItem}
            </AuthButtonWrapper>
          ) : (
            <div key={`menu_item_wrapper_${index}`} onClick={item.action}>
              {menuItem}
            </div>
          );
        })}
      </Menu>

      {submenuItems && (
        <Popover
          open={Boolean(submenuAnchor)}
          anchorEl={submenuAnchor}
          onClose={handleClose}
          anchorOrigin={{vertical: "top", horizontal: "right"}}
          transformOrigin={{vertical: "top", horizontal: "left"}}
          slotProps={{
            paper: {
              style: {
                minWidth: "180px",
                background: "#1C1717",
                color: "#ffffffcc",
                boxShadow: "0px 10px 15px 0px #1B28361A",
                borderRadius: "8px",
                padding: "8px",
              },
            },
          }}
        >
          {submenuItems.map((subItem, subIndex) => (
            <MenuItem
              key={`sub_menu_item_${subIndex}`}
              sx={{
                "display": "flex",
                "gap": "8px",
                "alignItems": "center",
                "padding": "6px 8px",
                "borderRadius": "4px",
                ":hover": {
                  backgroundColor: "#ffffff0d",
                },
              }}
              onClick={(e) => {
                handleClose(e);
                subItem?.action?.();
              }}
            >
              {subItem.icon}
              <span className="text-sm leading-none">{subItem.label}</span>
            </MenuItem>
          ))}
        </Popover>
      )}
      <ModalLyric
        open={openModalLyric}
        onCancel={handleCloseLyric}
        lrcUrl={data.lrcLyrics}
      />
    </>
  );
}
