import ApiSearch, {ISearchParams} from "@api/ApiSearch";
import {useQuery} from "@tanstack/react-query";
import {useEffect, useRef, useState} from "react";
import {useLocation, useNavigate} from "react-router-dom";
import {useDispatch, useSelector} from "react-redux";
import {useTranslation} from "react-i18next";
import {
  Avatar,
  Box,
  Divider,
  Drawer,
  InputBase,
  Menu,
  MenuItem,
  Popover,
  Skeleton,
  Tooltip,
} from "@mui/material";
import {logoutUser} from "@redux/slices/UserSlice.ts";
import {IRootState} from "@redux/store";
import {EGlobalModal, showModal} from "@redux/slices/GlobalModalSlice";
import IconArrowLeft from "@components/Icon/IconArrowLeft";
import IconCancel from "@components/Icon/IconCancel";
import IconSearchHome from "@components/Icon/IconSearchHome";
import IconLogout from "@components/Icon/IconLogout";
import IconVerify2 from "@components/Icon/IconVerify2";
import IconUser3 from "@components/Icon/IconUser3";
import ICHamburgerMenu from "@components/Icon/ICHamburgerMenu";
import ModalConfirm from "@components/ModalConfirm";
import ModalSettingGeneral from "../ModalSettingGeneral";
import GlobalButton from "@components/ButtonGlobal";
import QUERY_KEY from "@api/QueryKey";
import {logEvent} from "src/utils/firebase";
import SettingsIcon from "@mui/icons-material/Settings";
import {useFullName} from "src/utils/global";
import useDebounce from "src/hooks/useDebounce";
import SearchItem from "./components/SearchItem";
import {AccountType, ESearchType, ISearchItem} from "src/types";
import {playSingleSong} from "@redux/slices/PlayerSlice";
import ModalAppInstall from "./components/ModalInstall/ModalAppInstall";
import IconDownApp from "@components/Icon/IconDownApp";
import ICRingbackTone from "@components/Icon/ICRingTone";
import ModalRingbackTone from "@components/ModalRingbackTone";
import clsx from "clsx";

interface HomeSearchbarProps {
  className?: string;
  toggleDrawer: (value: boolean) => void; // Drawer list in parent component, check <HomeSidebar />
}

export default function HomeSearchbar({
  className,
  toggleDrawer,
}: HomeSearchbarProps) {
  const {t} = useTranslation();
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useDispatch();
  const getFullName = useFullName();

  const inputRef = useRef<HTMLInputElement | null>(null);

  const {accessToken, userInfo} = useSelector(
    (state: IRootState) => state.user,
  );

  const query = new URLSearchParams(location.search);
  const initialSearchValue =
    location.pathname === "/search/:tab" ? query.get("q") || "" : "";

  const [searchValue, setSearchValue] = useState(initialSearchValue);
  const [isSearchActive, setIsSearchActive] = useState(false);
  const [searchHistory, setSearchHistory] = useState<ISearchItem[]>([]);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [isModalLogout, setIsModalLogOut] = useState(false);
  const [searchPanelOpen, setSearchPanelOpen] = useState<boolean>(false);
  const [isModalSettingOpen, setIsModalSettingOpen] = useState(false);
  const [isDrawerSearch, setIsDrawerSearch] = useState<boolean>(false);
  const [isModalRegisterRingbackTone, setIsModalRegisterRingbackTone] =
    useState<boolean>(false);
  const [searchAnchorEl, setSearchAnchorEl] = useState<HTMLInputElement | null>(
    null,
  );
  const [isModalAppInstallOpen, setIsModalAppInstallOpen] =
    useState<boolean>(false);
  const searchValueDebounce = useDebounce(searchValue);

  useEffect(() => {
    if (!location.pathname.startsWith("/search")) {
      setSearchValue("");
    } else {
      const query = new URLSearchParams(location.search);
      setSearchValue(query.get("q") || "");
    }
  }, [location]);

  const params: ISearchParams = {
    page: 0,
    pageSize: 6,
  };

  const {data: dataTopSearch, isFetching} = useQuery({
    queryKey: [QUERY_KEY.SEARCH.GET_TOP_SEARCH_KEYWORDS, params],
    queryFn: () => ApiSearch.searchRecommend(params),
    enabled: isSearchActive || isDrawerSearch,
    staleTime: 3600000, // Refetch after 1 hour
  });

  const {data: dataSearchSuggestions} = useQuery({
    queryKey: [QUERY_KEY.SEARCH.GET_SEARCH_SUGGESTIONS, searchValueDebounce],
    queryFn: () =>
      ApiSearch.searchSuggestions({
        keyword: encodeURIComponent(searchValueDebounce),
      }),
    enabled: searchValueDebounce.length > 0,
  });

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchValue(event.target.value);
  };

  const handleKeyPress = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === "Enter" && searchValue.trim() !== "") {
      addToSearchHistory({value: searchValue, type: ESearchType.KEYWORD});
      logEvent("search_song", {
        search_query: searchValue.trim(),
        search_type: "text",
        is_select_search_result: false,
        is_playing_song: false,
      });
      // Mobile
      setIsDrawerSearch(false);
      // Desktop
      setSearchPanelOpen(false);
      event?.currentTarget.blur();
      navigate(`/search/all?q=${encodeURIComponent(searchValue.trim())}`);
      setTimeout(() => setIsSearchActive(false), 1000);
    }
  };

  const handleSearchIconClick = () => {
    if (searchValue.trim() !== "") {
      addToSearchHistory({value: searchValue, type: ESearchType.KEYWORD});
      logEvent("search_song", {
        search_query: searchValue.trim(),
        search_type: "text",
        is_select_search_result: false,
        is_playing_song: false,
      });
      setSearchPanelOpen(false);
      inputRef?.current?.blur();
      navigate(`/search/all?q=${encodeURIComponent(searchValue.trim())}`);
      setTimeout(() => setIsSearchActive(false), 1000);
    }
  };

  const handleClickSuggestion = (item: ISearchItem) => {
    const value =
      item.type === ESearchType.KEYWORD
        ? item.value
        : item.type === ESearchType.SONG
          ? item.value.name || ""
          : item.value.stageName || item.value.name;
    if (item.type === ESearchType.KEYWORD) {
      const searchUrl = `/search/all?q=${encodeURIComponent(value)}`;
      setSearchValue(value);
      navigate(searchUrl);
    } else if (item.type === ESearchType.SONG) {
      dispatch(playSingleSong(item.value));
      ApiSearch.saveSearchSuggestion(value);
    } else {
      navigate(`/artist/${item.value.urlSlug}`);
      ApiSearch.saveSearchSuggestion(value);
    }
    logEvent("search_song", {
      search_query: value,
      search_type: "suggestion",
      is_select_search_result: true,
      is_playing_song: false,
    });

    addToSearchHistory(item);
    // Mobile
    setIsDrawerSearch(false);
    // Desktop
    setSearchPanelOpen(false);
  };

  const handleClearInput = (event: React.MouseEvent) => {
    event.stopPropagation();
    setSearchValue("");
    setTimeout(() => {
      inputRef?.current?.focus();
      handleOpenMenu();
    }, 0);
  };

  const handleGoBack = () => {
    navigate(-1);
  };

  const handleMenuClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    if (accessToken) {
      setAnchorEl(event.currentTarget);
    }
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleCloseModalLogout = () => {
    setIsModalLogOut(false);
  };

  const handleConfirmLogout = () => {
    dispatch(logoutUser());
    logEvent("logout", {
      device_type: "web",
      status: "success",
    });
    handleMenuClose();
    handleCloseModalLogout();
  };

  const addToSearchHistory = (item: ISearchItem) => {
    setSearchHistory((prev) => {
      const updatedHistory = [
        item,
        ...prev.filter(
          (stored) =>
            stored.type !== item.type ||
            (stored.type === ESearchType.KEYWORD ||
            item.type === ESearchType.KEYWORD
              ? stored.value !== item.value
              : stored.value.id !== item.value.id),
        ),
      ];
      localStorage.setItem(
        "searchHistory",
        JSON.stringify(updatedHistory.slice(0, 5)),
      );
      return updatedHistory.slice(0, 5);
    });
  };

  const removeHistoryItem = (index: number) => {
    setSearchHistory((prev) => {
      const updatedHistory = prev.filter((_, i) => i !== index);
      localStorage.setItem("searchHistory", JSON.stringify(updatedHistory));
      return updatedHistory;
    });
  };

  const clearSearchHistory = (event: React.MouseEvent) => {
    event.stopPropagation();
    setSearchHistory([]);
    localStorage.removeItem("searchHistory");
  };

  const handleOpenMenu = () => {
    setIsSearchActive(true);
    setSearchPanelOpen(true);
    setSearchAnchorEl(inputRef?.current);
  };

  const handleCloseMenu = () => {
    setSearchPanelOpen(false);
    setTimeout(() => setIsSearchActive(false), 1000);
    setAnchorEl(null);
  };

  const handleOpenModalSetting = () => {
    setIsModalSettingOpen(true);
    handleMenuClose();
  };

  const handleOpenModalRegisterRingbackTone = () => {
    setIsModalRegisterRingbackTone(true);
    handleMenuClose();
  };

  const drawerSearch = (
    <div className="bg-[linear-gradient(#3F1414,#161110_40%)] h-dvh w-dvw text-white flex flex-col gap-5">
      <div className="mt-5 px-5 flex justify-between gap-2.5">
        <div className="bg-[#441B1B] px-3 py-1.5 w-full rounded-[10px] flex items-center">
          <IconSearchHome onClick={handleSearchIconClick} />
          <InputBase
            className="w-full pl-2 pr-7"
            inputProps={{className: "!text-white"}}
            placeholder={t("common.search")}
            value={searchValue}
            onChange={handleInputChange}
            onKeyDown={handleKeyPress}
          />
          {searchValue && (
            <div
              onClick={handleClearInput}
              className="cursor-pointer flex items-center absolute right-24 text-white p-1 rounded-full 
              z-[1400] pointer-events-auto"
            >
              <IconCancel className="w-4 h-4" />
            </div>
          )}
        </div>
        <button
          className="border border-solid border-[#FFFFFF12] py-1.5 px-2.5 rounded-[10px]"
          onClick={() => setIsDrawerSearch(false)}
        >
          {t("common.cancel")}
        </button>
      </div>
      <div className="flex flex-col gap-5 pb-4 px-5 overflow-y-auto">
        {dataSearchSuggestions?.searchLogs?.length &&
        dataSearchSuggestions.searchLogs.length > 0 ? (
          <div className="flex flex-col gap-1">
            <span className="text-white text-base font-medium">
              {t("search.related_keyword")}
            </span>
            <div className="flex w-full flex-col gap-1">
              {dataSearchSuggestions.searchLogs
                .slice(0, 4)
                .map((item, index) => (
                  <SearchItem
                    item={{
                      type: ESearchType.KEYWORD,
                      value: item.keyword,
                    }}
                    key={`search-suggestion-keyword-${index}`}
                    onClick={() => {
                      handleClickSuggestion({
                        type: ESearchType.KEYWORD,
                        value: item.keyword,
                      });
                      handleCloseMenu();
                      setTimeout(() => {
                        const activeElement =
                          document?.activeElement as HTMLElement;
                        if (activeElement) activeElement.blur();
                      }, 0);
                    }}
                  />
                ))}
            </div>
          </div>
        ) : (
          <div className="flex flex-col gap-[7px]">
            <span className="text-white text-base font-medium">
              {t("search.top_keyword")}
            </span>
            <ul className="text-[#FFFFFF99] text-sm bg-[#FFFFFF0D] rounded-lg flex flex-col gap-1.5 p-2">
              {isFetching ? (
                <li className="flex items-center justify-center w-full">
                  <Skeleton
                    variant="rounded"
                    className="flex flex-row justify-between text-[#FFFFFF80] items-center transition-all transition-300 ease-out w-full px-3 pt-2"
                    sx={{
                      maxWidth: "100%",
                      width: "100%",
                      height: "50px",
                      bgcolor: "#752121CC",
                    }}
                  />
                </li>
              ) : (
                Array.isArray(dataTopSearch?.data) &&
                dataTopSearch?.data?.map((item: string, index: number) => (
                  <li
                    key={index}
                    className="py-2 px-3 hover:bg-[#FFFFFF0D] hover:text-[#FFFFFF] rounded-md cursor-pointer flex items-center justify-between"
                    onClick={() => {
                      handleClickSuggestion({
                        type: ESearchType.KEYWORD,
                        value: item,
                      });
                      setIsDrawerSearch(false);
                      setTimeout(() => {
                        const activeElement =
                          document?.activeElement as HTMLElement;
                        if (activeElement) activeElement.blur();
                      }, 0);
                    }}
                  >
                    <div className="flex gap-3">
                      <span className="flex-shrink-0">{index + 1}</span>
                      <span className="truncate block max-w-[180px] sm:max-w-[500px] md:max-w-[600px]">
                        {item}
                      </span>
                    </div>
                  </li>
                ))
              )}
            </ul>
          </div>
        )}
        {dataSearchSuggestions?.songs?.length ||
        dataSearchSuggestions?.artists.length ? (
          <>
            {dataSearchSuggestions.songs.length > 0 && (
              <div className="flex flex-col gap-1">
                <span className="text-white text-base font-medium">
                  {t("search.related_songs")}
                </span>
                <div className="flex w-full flex-col">
                  {dataSearchSuggestions.songs
                    .slice(0, 3)
                    .map((item, index) => (
                      <SearchItem
                        item={{type: ESearchType.SONG, value: item}}
                        key={`search-suggestion-song-${index}`}
                        onClick={() => {
                          handleClickSuggestion({
                            type: ESearchType.SONG,
                            value: item,
                          });
                          handleCloseMenu();
                          setTimeout(() => {
                            const activeElement =
                              document?.activeElement as HTMLElement;
                            if (activeElement) activeElement.blur();
                          }, 0);
                        }}
                      />
                    ))}
                </div>
              </div>
            )}
            {dataSearchSuggestions.artists.length > 0 && (
              <div className="flex flex-col gap-1">
                <span className="text-white text-base font-medium">
                  {t("search.related_artists")}
                </span>
                {dataSearchSuggestions.artists
                  .slice(0, 3)
                  .map((item, index) => (
                    <SearchItem
                      item={{type: ESearchType.ARTIST, value: item}}
                      key={`search-suggestion-artist-${index}`}
                      onClick={() => {
                        handleClickSuggestion({
                          type: ESearchType.ARTIST,
                          value: item,
                        });
                        handleCloseMenu();
                        setTimeout(() => {
                          const activeElement =
                            document?.activeElement as HTMLElement;
                          if (activeElement) activeElement.blur();
                        }, 0);
                      }}
                    />
                  ))}
              </div>
            )}
          </>
        ) : (
          <div className="flex flex-col gap-[7px]">
            <div className="flex justify-between items-center">
              <span className="text-white text-base font-medium">
                {t("search.history_search")}
              </span>
              {searchHistory.length > 0 && (
                <button
                  onClick={clearSearchHistory}
                  onMouseDown={(e) => e.stopPropagation()}
                  className="text-[#FF4319] text-sm font-normal"
                >
                  {t("common.delete_all")}
                </button>
              )}
            </div>
            {searchHistory?.length > 0 ? (
              <div className="flex flex-col gap-2">
                {searchHistory?.map((item, index) => (
                  <SearchItem
                    key={index}
                    item={item}
                    onClick={() => {
                      handleClickSuggestion(item);
                    }}
                    onRemove={() => removeHistoryItem(index)}
                  />
                ))}
              </div>
            ) : (
              <p className="text-gray-500 text-sm italic">
                {t("search.no_history_search")}
              </p>
            )}
          </div>
        )}
      </div>
    </div>
  );

  useEffect(() => {
    const storedHistory = localStorage.getItem("searchHistory");
    if (storedHistory) {
      setSearchHistory(JSON.parse(storedHistory));
    }
  }, []);

  return (
    <div className={clsx("sticky w-full z-20 top-0", className)}>
      {location.pathname === "/upgrade" && (
        <div className="absolute top-0 left-0 w-full h-full !bg-[rgb(255,255,255,0.1)] !backdrop-blur-[15px] z-[-1]"></div>
      )}
      <div className="flex pt-2 lg:pt-4 pb-2 px-4 sm:px-6 md:px-8 justify-between items-center">
        {/* Mobile */}
        <ICHamburgerMenu
          onClick={() => toggleDrawer(true)}
          className="block lg:hidden cursor-pointer"
        />
        {/* Desktop */}
        <div className="items-center justify-center gap-[10px] lg:flex hidden">
          {location.pathname !== "/" && (
            <button
              onClick={handleGoBack}
              className="text-white"
              aria-label="Go back"
            >
              <IconArrowLeft />
            </button>
          )}
          <div
            className={`${location.pathname === "/upgrade" ? "!bg-[rgb(255,255,255,0.1)]" : "bg-[#441B1B]"} rounded-t-[20px] px-6 py-1.5 flex items-center gap-x-3 w-[36vw] transition-all duration-300 sm:min-w-56 relative z-[1500] ${searchPanelOpen ? "rounded-b-none" : "rounded-b-[20px]"}`}
          >
            <IconSearchHome
              className="cursor-pointer"
              onClick={handleSearchIconClick}
            />
            <InputBase
              id="search-input"
              className="w-full pr-4 autofill-input"
              inputProps={{className: "!text-white"}}
              placeholder={t("search.question_search")}
              value={searchValue}
              onChange={handleInputChange}
              onClick={handleOpenMenu}
              onKeyDown={handleKeyPress}
              inputRef={inputRef}
            />
            {searchValue && (
              <div
                onClick={handleClearInput}
                className="cursor-pointer flex items-center absolute right-3 text-white p-1 rounded-full 
              z-[1400] pointer-events-auto"
              >
                <IconCancel className="w-4 h-4 p-1 bg-[#FFFFFF66] rounded-full" />
              </div>
            )}
          </div>
        </div>
        {/* Mobile */}
        <img
          src="/image/logo.png"
          className="w-40 cursor-pointer max-sm:w-32 block lg:hidden"
          alt="logo"
          onClick={() => navigate("/")}
        />
        <div className="lg:flex hidden gap-1 md:gap-6 items-center">
          <button
            onClick={() => setIsModalAppInstallOpen(true)}
            className="hidden px-4 py-2 text-white lg:flex lg:items-center lg:justify-between lg:gap-3"
          >
            <IconDownApp />
            {t("common.download_app")}
          </button>
          <Tooltip title={t("common.settings")} arrow>
            <div
              className="items-center text-sm gap-x-2 text-white flex cursor-pointer p-2 rounded-full bg-[#FFFFFF1A]"
              onClick={handleOpenModalSetting}
            >
              <SettingsIcon />
            </div>
          </Tooltip>
          <Divider
            orientation="vertical"
            className="bg-[#FFFFFF36] w-0.5 hidden md:block"
            sx={{height: 16}}
          />
          {!accessToken && (
            <GlobalButton
              text={t("auth.login")}
              className="!rounded-[28px] line-clamp-1 text-center hidden md:flex"
              textClassName="text-white text-sm"
              onClick={() => dispatch(showModal(EGlobalModal.AUTH_MODAL))}
            />
          )}
          {accessToken && (
            <button onClick={handleMenuClick} className="hidden md:block">
              <Avatar
                src={
                  accessToken
                    ? userInfo?.avatar || "/image/default-avatar.png"
                    : "/image/default-avatar.png"
                }
                sx={{boxShadow: "0 0 0 4px #FFFFFF1A"}}
                imgProps={{
                  draggable: false,
                  onDragStart: (e) => e.preventDefault(),
                }}
              />
            </button>
          )}
          <Menu
            id="basic-menu"
            anchorEl={anchorEl}
            open={Boolean(anchorEl)}
            onClose={handleMenuClose}
            slotProps={{
              paper: {
                sx: {
                  bgcolor: "#1C1717",
                  color: "white",
                  minWidth: 336,
                  borderRadius: "12px",
                  pt: "16px",
                  pb: "12px",
                  px: "16px",
                },
              },
              list: {"aria-labelledby": "basic-button"},
            }}
          >
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                p: "8px",
              }}
            >
              <Avatar
                src={userInfo?.avatar || "/image/default-avatar.png"}
                sx={{width: 48, height: 48, mr: "12px"}}
              />
              <Box>
                <div className="flex flex-col gap-[6px]">
                  <p className="text-[15px]">
                    {getFullName ?? userInfo?.username ?? "Tinasoft VN"}
                  </p>

                  <span className="text-xs p-1 bg-[#FFE7E117] text-[#FF4319] rounded-lg flex justify-center items-center gap-1">
                    <IconVerify2 />
                    {userInfo?.accountType === AccountType.PRO
                      ? t("common.pro")
                      : userInfo?.accountType === AccountType.PREMIUM
                        ? t("common.premium")
                        : t("common.basic")}
                  </span>
                </div>
              </Box>
            </Box>

            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
                mt: "8px",
                borderRadius: 3,
                border: "1px solid #eeecec17",
                backgroundColor: "#FFFFFF0A",
                cursor: "pointer",
                padding: "8px 12px",
              }}
              onClick={() => {
                handleMenuClose();
                navigate("/upgrade");
              }}
            >
              <div className="flex items-center gap-x-3">
                <img src="/image/vip-box.png" className="w-8 h-8" />
                <span className="text-sm text-white">
                  {userInfo?.accountType === AccountType.PRO
                    ? t("common.package_pro")
                    : userInfo?.accountType === AccountType.PREMIUM
                      ? t("common.package_premium")
                      : t("common.package_basic")}
                </span>
              </div>
              <span className="text-right items-end text-[#FF4319] text-[13px]">
                {t("common.upgrade")}
              </span>
            </Box>
            <Divider
              orientation="vertical"
              className="bg-[#292222] w-full"
              sx={{height: 1.5, marginTop: "8px", marginBottom: "8px"}}
            />
            <MenuItem
              onClick={handleOpenModalRegisterRingbackTone}
              sx={{
                "display": "flex",
                "alignItems": "center",
                "gap": "12px",
                "color": "#d1d5db",
                "paddingTop": "10px",
                "paddingBottom": "10px",
                "paddingLeft": "12px",
                "borderRadius": 3,
                "&:hover": {
                  bgcolor: "#FFE7E10D",
                  cursor: "pointer",
                  color: "#ffffff",
                },
              }}
            >
              <ICRingbackTone />
              <span className="text-sm">
                {t("services.ringback_tone.register_title")}
              </span>
            </MenuItem>
            <MenuItem
              onClick={() => {
                handleMenuClose();
                navigate("/profile");
              }}
              sx={{
                "display": "flex",
                "alignItems": "center",
                "gap": "12px",
                "color": "#d1d5db",
                "paddingTop": "10px",
                "paddingBottom": "10px",
                "paddingLeft": "12px",
                "borderRadius": 3,
                "&:hover": {
                  bgcolor: "#FFE7E10D",
                  cursor: "pointer",
                  color: "#ffffff",
                },
              }}
            >
              <IconUser3 />
              <span className="text-sm">{t("common.profile")}</span>
            </MenuItem>

            <Divider
              orientation="vertical"
              className="bg-[#292222] w-full"
              sx={{height: 1.5, marginTop: "8px", marginBottom: "8px"}}
            />

            <MenuItem
              onClick={() => {
                setIsModalLogOut(true);
              }}
              sx={{
                "display": "flex",
                "gap": "12px",
                "color": "#d1d5db",
                "paddingTop": "10px",
                "paddingBottom": "10px",
                "paddingLeft": "12px",
                "borderRadius": 3,
                "&:hover": {
                  bgcolor: "#FFE7E10D",
                  cursor: "pointer",
                  color: "#ffffff",
                },
              }}
            >
              <IconLogout />
              <span className="text-sm">{t("auth.logout")}</span>
            </MenuItem>
          </Menu>
        </div>
        {/* Mobile */}
        <IconSearchHome
          className="block lg:hidden cursor-pointer"
          onClick={() => setIsDrawerSearch(true)}
        />
      </div>

      <ModalSettingGeneral
        open={isModalSettingOpen}
        onCancel={() => setIsModalSettingOpen(false)}
      />

      <ModalConfirm
        open={isModalLogout}
        title={t("auth.logout")}
        onConfirm={handleConfirmLogout}
        onCancel={handleCloseModalLogout}
      >
        <span>{t("auth.logout_confirm")}</span>
      </ModalConfirm>

      <ModalAppInstall
        open={isModalAppInstallOpen}
        onClose={() => setIsModalAppInstallOpen(false)}
      />

      <Popover
        anchorEl={searchAnchorEl}
        open={searchPanelOpen}
        onClose={handleCloseMenu}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "center",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "center",
        }}
        classes={{
          paper:
            "lg:block hidden w-[36vw] lg:min-w-56 pt-12 -mt-[38px] -ml-[6px] !bg-[#441B1B] !rounded-t-[20px] shadow-lg !z-[1000]",
        }}
        disableAutoFocus
        disableEnforceFocus
        disablePortal
      >
        <div className="border-t border-solid border-[#FFFFFF12] -mt-1" />
        <div className="flex flex-col gap-2 pt-3 max-h-[60vh] p-4 overflow-auto">
          {dataSearchSuggestions?.searchLogs?.length &&
          dataSearchSuggestions.searchLogs.length > 0 ? (
            <div className="flex flex-col gap-1">
              <span className="text-white text-base font-medium">
                {t("search.related_keyword")}
              </span>
              <div className="flex w-full flex-col gap-1">
                {dataSearchSuggestions.searchLogs
                  .slice(0, 4)
                  .map((item, index) => (
                    <SearchItem
                      item={{
                        type: ESearchType.KEYWORD,
                        value: item.keyword,
                      }}
                      key={`search-suggestion-keyword-${index}`}
                      onClick={() => {
                        handleClickSuggestion({
                          type: ESearchType.KEYWORD,
                          value: item.keyword,
                        });
                        handleCloseMenu();
                        setTimeout(() => {
                          const activeElement =
                            document?.activeElement as HTMLElement;
                          if (activeElement) activeElement.blur();
                        }, 0);
                      }}
                    />
                  ))}
              </div>
            </div>
          ) : (
            <div className="flex flex-col gap-2">
              <span className="text-white text-base font-medium">
                {t("search.top_keyword")}
              </span>
              <div className="text-[#E3E3E3] text-sm rounded-lg flex flex-wrap gap-2.5">
                {isFetching ? (
                  <div className="flex items-center justify-center w-full">
                    <Skeleton
                      variant="rounded"
                      className="flex flex-row justify-between text-[#FFFFFF80] items-center transition-all transition-300 ease-out w-full px-3 pt-2"
                      sx={{
                        maxWidth: "100%",
                        width: "100%",
                        height: "50px",
                        bgcolor: "#752121CC",
                      }}
                    />
                  </div>
                ) : (
                  Array.isArray(dataTopSearch?.data) &&
                  dataTopSearch?.data?.map((item: string, index: number) => (
                    <div
                      key={index}
                      className="py-2 px-3 bg-[#FFFFFF1A] hover:bg-[#FFFFFF26] hover:border-[#FFFFFF] hover:text-[#FFFFFF] rounded-[20px] cursor-pointer flex items-center justify-between border-[0.5px] border-solid border-[transparent] max-w-[198px]"
                      onClick={() => {
                        handleClickSuggestion({
                          type: ESearchType.KEYWORD,
                          value: item,
                        });
                        handleCloseMenu();
                        setTimeout(() => {
                          const activeElement =
                            document?.activeElement as HTMLElement;
                          if (activeElement) activeElement.blur();
                        }, 0);
                      }}
                    >
                      <span className="truncate w-full block">{item}</span>
                    </div>
                  ))
                )}
              </div>
            </div>
          )}
          {dataSearchSuggestions?.songs?.length ||
          dataSearchSuggestions?.artists.length ? (
            <>
              {dataSearchSuggestions.songs.length > 0 && (
                <div className="flex flex-col gap-1">
                  <span className="text-white text-base font-medium">
                    {t("search.related_songs")}
                  </span>
                  <div className="flex w-full flex-col">
                    {dataSearchSuggestions.songs
                      .slice(0, 3)
                      .map((item, index) => (
                        <SearchItem
                          item={{type: ESearchType.SONG, value: item}}
                          key={`search-suggestion-song-${index}`}
                          onClick={() => {
                            handleClickSuggestion({
                              type: ESearchType.SONG,
                              value: item,
                            });
                            handleCloseMenu();
                            setTimeout(() => {
                              const activeElement =
                                document?.activeElement as HTMLElement;
                              if (activeElement) activeElement.blur();
                            }, 0);
                          }}
                        />
                      ))}
                  </div>
                </div>
              )}
              {dataSearchSuggestions.artists.length > 0 && (
                <div className="flex flex-col gap-1">
                  <span className="text-white text-base font-medium">
                    {t("search.related_artists")}
                  </span>
                  {dataSearchSuggestions.artists
                    .slice(0, 3)
                    .map((item, index) => (
                      <SearchItem
                        item={{type: ESearchType.ARTIST, value: item}}
                        key={`search-suggestion-artist-${index}`}
                        onClick={() => {
                          handleClickSuggestion({
                            type: ESearchType.ARTIST,
                            value: item,
                          });
                          handleCloseMenu();
                          setTimeout(() => {
                            const activeElement =
                              document?.activeElement as HTMLElement;
                            if (activeElement) activeElement.blur();
                          }, 0);
                        }}
                      />
                    ))}
                </div>
              )}
            </>
          ) : (
            <div className="flex flex-col gap-2">
              <div className="flex justify-between items-center">
                <span className="text-white text-base font-medium">
                  {t("search.history_search")}
                </span>
                {searchHistory.length > 0 && (
                  <button
                    onClick={clearSearchHistory}
                    onMouseDown={(e) => e.stopPropagation()}
                    className="text-[#FF4319] text-sm font-normal"
                  >
                    {t("common.delete_all")}
                  </button>
                )}
              </div>
              {searchHistory?.length > 0 ? (
                <div className="flex w-full flex-col gap-y-1">
                  {searchHistory?.map((item, index) => (
                    <SearchItem
                      item={item}
                      key={`search-history-keyword-${index}`}
                      onRemove={() => removeHistoryItem(index)}
                      onClick={() => {
                        handleClickSuggestion(item);
                      }}
                    />
                  ))}
                </div>
              ) : (
                <p className="text-gray-500 text-sm italic">
                  {t("search.no_history_search")}
                </p>
              )}
            </div>
          )}
        </div>
      </Popover>

      <Drawer
        open={isDrawerSearch}
        className="lg:hidden block"
        anchor="top"
        onClose={() => setIsDrawerSearch(false)}
      >
        {drawerSearch}
      </Drawer>
      <ModalRingbackTone
        open={isModalRegisterRingbackTone}
        onCancel={() => setIsModalRegisterRingbackTone(false)}
      />
    </div>
  );
}
