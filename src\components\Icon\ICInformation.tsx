const ICInformation = ({...props}: React.SVGProps<SVGSVGElement>) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M12.4375 16.125V10.625H9.6875V12H11.0625V16.125H9V17.5H14.5V16.125H12.4375Z"
      fill="currentColor"
    />
    <path
      d="M12.0313 6.5C11.8273 6.5 11.6279 6.56048 11.4583 6.6738C11.2887 6.78711 11.1566 6.94817 11.0785 7.13661C11.0004 7.32504 10.98 7.53239 11.0198 7.73244C11.0596 7.93248 11.1578 8.11623 11.302 8.26045C11.4463 8.40468 11.63 8.50289 11.8301 8.54269C12.0301 8.58248 12.2375 8.56205 12.4259 8.484C12.6143 8.40595 12.7754 8.27377 12.8887 8.10418C13.002 7.93459 13.0625 7.73521 13.0625 7.53125C13.0625 7.25775 12.9539 6.99544 12.7605 6.80205C12.5671 6.60865 12.3048 6.5 12.0313 6.5Z"
      fill="currentColor"
    />
    <path
      d="M11.625 21.625C9.72136 21.625 7.86046 21.0605 6.27764 20.0029C4.69482 18.9453 3.46116 17.4421 2.73266 15.6833C2.00417 13.9246 1.81356 11.9893 2.18495 10.1223C2.55633 8.25519 3.47302 6.54018 4.8191 5.1941C6.16518 3.84802 7.88019 2.93133 9.74726 2.55995C11.6143 2.18856 13.5496 2.37917 15.3083 3.10766C17.0671 3.83616 18.5703 5.06982 19.6279 6.65264C20.6855 8.23546 21.25 10.0964 21.25 12C21.25 14.5527 20.2359 17.0009 18.4309 18.8059C16.6259 20.6109 14.1777 21.625 11.625 21.625ZM11.625 3.75C9.99331 3.75 8.39826 4.23386 7.04155 5.14038C5.68484 6.0469 4.62742 7.33538 4.003 8.84287C3.37858 10.3504 3.2152 12.0092 3.53353 13.6095C3.85185 15.2098 4.63759 16.6799 5.79137 17.8336C6.94516 18.9874 8.41517 19.7732 10.0155 20.0915C11.6159 20.4098 13.2747 20.2464 14.7821 19.622C16.2896 18.9976 17.5781 17.9402 18.4846 16.5835C19.3911 15.2268 19.875 13.6317 19.875 12C19.875 9.81197 19.0058 7.71355 17.4586 6.16637C15.9115 4.6192 13.813 3.75 11.625 3.75Z"
      fill="currentColor"
    />
  </svg>
);
export default ICInformation;
