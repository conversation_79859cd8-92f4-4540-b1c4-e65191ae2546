import {convertPlayerTime} from "src/utils/timeUtils";
import store from "@redux/store";
import {handleNextSong, pauseMusic, playMusic} from "@redux/slices/PlayerSlice";
import ApiSong from "@api/ApiSong";
import PlayerUtil from "./PlayerUtil";
import {toast} from "react-toastify";
import {IBasePlayer} from "./types";

class PlayerListenerHandler {
  isSliderDragging = false;
  private currentLyricLineIndex = -1;
  private isUserScrollLyrics = false;
  private manualScrollTimeout: ReturnType<typeof setTimeout> | null = null;
  private listenTimeout: ReturnType<typeof setTimeout> | null = null;

  private lowerBoundLyricSearch(
    low: number,
    high: number,
    time: number,
  ): number {
    if (low >= high) return low;
    const mid = Math.floor((low + high) / 2);
    if (PlayerUtil.instance.lyricTimeLines[mid] >= time)
      return this.lowerBoundLyricSearch(low, mid - 1, time);
    return this.lowerBoundLyricSearch(mid + 1, high, time);
  }

  async setUpFollowLyrics() {
    const lyricsContainer = document.getElementById("lyrics-container");
    if (!lyricsContainer) return;

    lyricsContainer.addEventListener("scroll", () => {
      if (this.isUserScrollLyrics) return;

      this.isUserScrollLyrics = true;

      if (this.manualScrollTimeout) {
        clearTimeout(this.manualScrollTimeout);
      }

      this.manualScrollTimeout = setTimeout(() => {
        this.isUserScrollLyrics = false;
      }, 5000); // scroll timeout 5s auto scroll
    });

    this.setupScrollListener();
  }

  private setupScrollListener() {
    window.addEventListener(
      "scroll",
      () => {
        this.isUserScrollLyrics = true;
      },
      {passive: true},
    );
    window.addEventListener(
      "scrollend",
      () => {
        if (this.manualScrollTimeout) {
          clearTimeout(this.manualScrollTimeout);
        }

        this.manualScrollTimeout = setTimeout(() => {
          this.isUserScrollLyrics = false;
        }, 5000); // After 5 seconds of user scroll, enable auto-scroll again
      },
      {passive: true},
    );

    // On mobile, use touchstart and touchend events
    window.addEventListener(
      "touchstart",
      () => {
        this.isUserScrollLyrics = true;
      },
      {passive: true},
    );

    window.addEventListener(
      "touchend",
      () => {
        if (this.manualScrollTimeout) {
          clearTimeout(this.manualScrollTimeout);
        }

        this.manualScrollTimeout = setTimeout(() => {
          this.isUserScrollLyrics = false;
        }, 5000);
      },
      {passive: true},
    );
  }

  handleOndurationChange(player: IBasePlayer) {
    if (player.type === PlayerUtil.instance.type) {
      const newDurationString = convertPlayerTime(player.duration);
      const durationElements = document.querySelectorAll(".player-duration");
      durationElements.forEach(
        (element) => (element.innerHTML = newDurationString),
      );
    }
  }

  handleOnTimeChange(player: IBasePlayer) {
    if (player.type === PlayerUtil.instance.type) {
      if (!this.isSliderDragging) {
        PlayerUtil.instance.updateSlider();
        PlayerUtil.instance.updatePositionText();
      }
      const isOpenLyrics = store.getState().player.isOpenLyrics;
      if (isOpenLyrics && PlayerUtil.instance.lyricTimeLines.length) {
        let newLyricLineIndex = this.lowerBoundLyricSearch(
          0,
          PlayerUtil.instance.lyricTimeLines.length - 1,
          player.currentTime,
        );
        if (
          PlayerUtil.instance.lyricTimeLines[newLyricLineIndex] >
          player.currentTime
        ) {
          newLyricLineIndex -= 1;
        }

        if (this.currentLyricLineIndex !== newLyricLineIndex) {
          if (this.currentLyricLineIndex >= 0) {
            document
              .getElementById(`lyric-index-${this.currentLyricLineIndex}`)
              ?.classList.remove("lyric-line-active");
          }
          if (newLyricLineIndex >= 0) {
            const currentLine = document.getElementById(
              `lyric-index-${newLyricLineIndex}`,
            );
            currentLine?.classList.add("lyric-line-active");
            this.currentLyricLineIndex = newLyricLineIndex;

            if (!this.isUserScrollLyrics && currentLine) {
              if (currentLine.getBoundingClientRect().x <= window.innerWidth) {
                currentLine.scrollIntoView({
                  behavior: "smooth",
                  block: "center",
                });
              }
            }
          }
        }
      }
    }
  }

  handleOnReady(player: IBasePlayer) {
    if (player.type === PlayerUtil.instance.type) {
      const playerPosition = PlayerUtil.getLocalValue("PLAYER_POSITION") || 0;
      if (playerPosition.player === PlayerUtil.instance.type) {
        PlayerUtil.instance.seek(playerPosition.position);
        PlayerUtil.instance.pause();
      }
    }
  }

  handleOnLoadedMetadata(player: IBasePlayer) {
    if (player.type === PlayerUtil.instance.type) {
      const {paused} = store.getState().player;
      if (!paused) {
        PlayerUtil.instance.play();
      }
    }
  }

  handleOnStart(player: IBasePlayer) {
    if (player.type === PlayerUtil.instance.type) {
      if (this.listenTimeout) {
        clearTimeout(this.listenTimeout);
      }

      this.listenTimeout = setTimeout(() => {
        const currentSong = store.getState().player.currentSong;
        if (currentSong?.id) {
          ApiSong.listenSong(currentSong.id);
        }
      }, 5000);
    }
  }

  handleOnPlay(player: IBasePlayer) {
    if (player.type === PlayerUtil.instance.type) {
      store.dispatch(playMusic());
    }
  }
  handleOnPause(player: IBasePlayer) {
    if (player.type === PlayerUtil.instance.type) {
      store.dispatch(pauseMusic());
    }
  }
  handleOnEnded(player: IBasePlayer) {
    if (player.type === PlayerUtil.instance.type) {
      const {timeToNextSong, removeSilent} = store.getState().settings;
      if (!removeSilent) {
        setTimeout(() => {
          store.dispatch(handleNextSong({isManual: false}));
        }, timeToNextSong * 1000);
      } else {
        store.dispatch(handleNextSong({isManual: false}));
      }
    }
  }

  handleOnError(error: Error) {
    toast.error(error.message);
  }
}

export default new PlayerListenerHandler();
