import {ISong} from "src/types";
import {fetcher, fetcherWithMetadata, IDataWithMeta} from "./Fetcher";
import {GridRowId} from "@mui/x-data-grid";

export interface IGetSongsParams {
  themeId?: string;
  genreId?: string;
  albumId?: string;
  updatedAt?: string;
  page?: number;
  pageSize?: number;
  order?: string;
  direction?: string;
  keyword?: string;
  fromDate?: string;
  toDate?: string;
}

export interface IValue {
  id: string;
  label: string;
}

export interface ICreateSong {
  name?: string;
  image?: File;
  themeIds?: IValue[];
  genreIds?: IValue[];
  releaseDate?: string;
  description?: string;
  albumId?: IValue;
  musicianIds?: IValue[];
  artistIds?: IValue[];
  audioHq?: File;
  audio?: File;
  textLyrics?: string;
  lrcLyrics?: File;
  license?: File;
  ageLimit?: number;
}

const path = {
  cmsSong: "/cms/song",
};

function getCmsSongs(params: IGetSongsParams): Promise<IDataWithMeta<[]>> {
  return fetcherWithMetadata(
    {
      url: path.cmsSong,
      method: "get",
      params,
    },
    {
      displayError: true,
    },
  );
}

function getCmsSongById(id: GridRowId): Promise<ISong> {
  return fetcher(
    {
      url: `${path.cmsSong}/${id}`,
      method: "get",
    },
    {
      displayError: true,
    },
  );
}

function createSong(body: ICreateSong): Promise<void> {
  return fetcher(
    {
      url: path.cmsSong,
      method: "post",
      data: body,
      formSerializer: {
        indexes: null,
      },
    },
    {
      displayError: true,
      isFormData: true,
    },
  );
}

function updateSong({
  id,
  ...restData
}: ICreateSong & {id: string}): Promise<void> {
  return fetcher(
    {
      url: `${path.cmsSong}/${id}`,
      method: "patch",
      data: restData,
      formSerializer: {
        indexes: null,
      },
    },
    {
      displayError: true,
      isFormData: true,
    },
  );
}

function deleteSong(id: string) {
  return fetcher(
    {
      url: `${path.cmsSong}/${id}`,
      method: "delete",
    },
    {displayError: false},
  );
}

export default {
  getCmsSongs,
  getCmsSongById,
  createSong,
  updateSong,
  deleteSong,
};
