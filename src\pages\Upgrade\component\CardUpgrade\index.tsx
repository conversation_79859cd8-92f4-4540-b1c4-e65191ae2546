import React, {useMemo} from "react";
import CheckIcon from "@mui/icons-material/Check";
import {useTranslation} from "react-i18next";
import {IMusicPackage} from "src/types";
import {Button} from "@mui/material";
import {formatNumberWithCommas} from "src/utils/numberUtils";

interface CardUpgradeProps {
  data: IMusicPackage;
  onClick: () => void;
}

const CardUpgrade: React.FC<CardUpgradeProps> = ({
  data,
  onClick,
}: CardUpgradeProps) => {
  const {t} = useTranslation();
  const textType = useMemo(() => {
    switch (data.type) {
      case 0:
        return t("common.upgrade_page.day");
      case 1:
        return t("common.upgrade_page.week");
      case 2:
        return t("common.upgrade_page.month");
      case 3:
        return t("common.upgrade_page.year");
      default:
        return "";
    }
  }, [data.type, t]);

  return (
    <div
      className={`p-[1px] rounded-[10px] sm:mx-0 mx-4 w-full sm:max-w-[400px]`}
      style={{
        background:
          "linear-gradient(315.02deg, rgba(255,0,0,0.2) -1.75%, rgba(255, 111, 111, 0.1) 53.19%)",
        border: "1px solid",
        borderColor: "#FF8D22",
      }}
    >
      <div
        className="rounded-[10px] p-6 text-white w-full h-full"
        style={{background: "linear-gradient(315deg, #510404 0%, #150404 45%)"}}
      >
        {/* Header */}
        <div className="flex gap-[10px] items-center mb-3">
          <img
            src="/image/logo.png"
            alt="Logo"
            className="h-12 rounded-[4px]"
          />
          <span
            className={`bg-white text-[#FF0101] text-xs font-[800] uppercase px-[10px] py-[5px] rounded`}
          >
            {textType}
          </span>
        </div>

        <div className="flex flex-col justify-start gap-3 items-start">
          <div className="flex gap-3 flex-wrap">
            <p className="text-base font-bold">
              {formatNumberWithCommas(data.specialPrice || data.price)}{" "}
              <span className="uppercase">{data.priceUnit}</span>
              {`/${textType}`}
            </p>
            <p className="text-base font-bold">
              {data.specialPrice ? (
                <span className="line-through text-gray-500">
                  {formatNumberWithCommas(data.price)}{" "}
                  <span className="uppercase">{data.priceUnit}</span>
                  {`/${textType}`}
                </span>
              ) : null}
            </p>
          </div>

          <ul className="flex flex-col gap-3 m-0 space-y-1 text-sm list-none">
            <li>
              <p className="text-sm font-semibold">
                {t("common.upgrade_page.privileges")}
              </p>
            </li>
            {data.features.map((feature, idx) => (
              <li key={idx} className="flex items-center gap-2 m-0">
                <CheckIcon />
                {feature}
              </li>
            ))}
          </ul>
          <hr className="my-2 border-[rgba(255,255,255,0.4)] w-full" />
          <div className="flex flex-col w-full gap-4">
            <div className="flex items-center gap-2">
              <img src="/image/coin.png" alt="" />
              <p className="text-base font-bold">
                {formatNumberWithCommas(data.specialPrice || data.price)}{" "}
                <span className="uppercase">{data.priceUnit}</span>
                {`/${textType}`}
              </p>
            </div>
            <Button
              className="!bg-orange-500 !py-2.5 !px-8 !rounded-[20px] !text-[#FBFDFF] !font-bold w-full"
              onClick={onClick}
            >
              {t("common.upgrade_page.button_upgrade")}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CardUpgrade;
