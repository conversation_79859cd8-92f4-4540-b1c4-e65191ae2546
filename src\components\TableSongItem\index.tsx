import {SwapVertRounded} from "@mui/icons-material";
import {IRootState} from "@redux/store";
import clsx from "clsx";
import {useState} from "react";
import {useTranslation} from "react-i18next";
import {useSelector} from "react-redux";
import {ISong} from "src/types";
import PlayerUtil from "src/core/PlayerUtil";

import SongItem from "../SongItem";
import "./index.scss";

interface TableSongItemProps {
  songs: ISong[];
  className?: string;
  showDuration?: boolean;
  showAlbumInfo?: boolean;
  showReleaseDate?: boolean;
  showNumber?: boolean;
  onPlaySong?: (song: ISong) => void;
  extraMenuActions?: {
    icon?: React.ReactElement;
    label: React.ReactElement | string;
    action?: (song: ISong) => void;
    isAuth?: boolean;
  }[];
  onSortSongs?: () => void;
}

export default function TableSongItem({
  songs,
  className,
  showDuration = true,
  showAlbumInfo = true,
  showReleaseDate = true,
  showNumber = true,
  onPlaySong,
}: TableSongItemProps) {
  const {t} = useTranslation();
  const {currentSong, paused} = useSelector(
    (state: IRootState) => state?.player,
  );
  const [sortedSongs, setSortedSongs] = useState<ISong[]>(songs);
  const [isReversed, setIsReversed] = useState(false);

  const handlePlayMusic = (song: ISong) => {
    if (onPlaySong) {
      onPlaySong(song);
    } else {
      const isPlay = currentSong?.id === song?.id;
      if (isPlay) {
        if (paused) {
          PlayerUtil.instance.play();
        } else {
          PlayerUtil.instance.pause();
        }
      }
    }
  };

  const handleSortListSong = () => {
    if (isReversed) {
      const newSongs = [...songs].sort((a, b) => {
        if (a?.name && b?.name) {
          return a?.name?.localeCompare(b?.name);
        }
        return 0;
      });
      setSortedSongs(newSongs);
      setIsReversed(false);
    } else {
      const newSongs = [...songs].sort((a, b) => {
        if (a?.name && b?.name) {
          return b?.name?.localeCompare(a?.name);
        }
        return 0;
      });
      setSortedSongs(newSongs);
      setIsReversed(true);
    }
  };

  return (
    <>
      <div className={clsx("table-song-item overflow-x-auto", className)}>
        <div className="table w-full border-collapse">
          {/* Table Header */}
          <div className="table-header-group">
            <div className="table-row border-b border-[rgba(255,255,255,0.1)]">
              {showNumber && (
                <div className="hidden md:table-cell align-middle text-center px-2 py-3 font-bold text-white w-16">
                  <p className="whitespace-nowrap overflow-hidden text-ellipsis md:text-xl">
                    #
                  </p>
                </div>
              )}
              <div className="table-cell align-middle px-2 py-3 font-bold text-white w-full">
                <div className="flex items-center gap-2 overflow-hidden whitespace-nowrap text-ellipsis">
                  <p className="md:text-xl">{t("common.song")}</p>
                  <SwapVertRounded
                    sx={{"&:hover": {cursor: "pointer"}}}
                    onClick={handleSortListSong}
                  />
                </div>
              </div>

              {showAlbumInfo && (
                <div className="hidden lg:table-cell align-middle px-2 py-3 font-bold text-white w-32">
                  <p className="whitespace-nowrap overflow-hidden text-ellipsis md:text-xl">
                    {t("common.album")}
                  </p>
                </div>
              )}

              {showReleaseDate && (
                <div className="hidden md:table-cell align-middle px-2 py-3 font-bold text-white w-32">
                  <p className="whitespace-nowrap overflow-hidden text-ellipsis md:text-xl">
                    {t("common.release_date")}
                  </p>
                </div>
              )}

              <div className="table-cell align-middle px-2 py-3 font-bold text-white text-right w-24">
                <p className="whitespace-nowrap overflow-hidden text-ellipsis md:text-xl">
                  {showDuration && t("common.duration")}
                </p>
              </div>
            </div>
          </div>

          {/* Table Body */}
          <div className="table-row-group">
            {sortedSongs.map((song, index) => (
              <SongItem
                key={song.id}
                song={song}
                tableLayout={true}
                showNumber={showNumber}
                showDuration={showDuration}
                showAlbumInfo={showAlbumInfo}
                showReleaseDate={showReleaseDate}
                index={index}
                handlePlayMusic={() => handlePlayMusic(song)}
              />
            ))}
          </div>
        </div>
      </div>
    </>
  );
}
