import ApiSong from "@api/ApiSong";
import LikeButton from "@components/AuthButton/LikeButton";
import IconMoreHorizontal from "@components/Icon/IconMoreHorizontal";
import IconPlay from "@components/Icon/IconPlay";
import IconAdd24px from "@components/Icon/IconAdd24px";
import IconShare from "@components/Icon/IconShare";
import PopupMenu from "@components/PopupMenu";
import ModalAddToPlaylist from "@components/ModalAddToPlaylist";
import ModalShare from "@components/ModalShare";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
} from "@mui/material";
import {addSongsToQueue} from "@redux/slices/PlayerSlice";
import {IRootState} from "@redux/store";
import {useMutation} from "@tanstack/react-query";
import clsx from "clsx";
import {useMemo, useState} from "react";
import {useTranslation} from "react-i18next";
import {useDispatch, useSelector} from "react-redux";
import {useNavigate} from "react-router-dom";
import {ESongType, ISong} from "src/types";
import {convertDateSong, convertPlayerTime} from "src/utils/timeUtils";
import {useWindowWidth} from "src/utils/hooks";
import {toast} from "react-toastify";
import {generateShareLink} from "src/utils/global";
import PlayerUtil from "src/core/PlayerUtil";
import {handleLikeSong} from "src/utils/like";
import "./index.scss";
import {SwapVertRounded} from "@mui/icons-material";

interface TableSongItemProps {
  songs: ISong[];
  className?: string;
  showDuration?: boolean;
  showAlbumInfo?: boolean;
  showReleaseDate?: boolean;
  showNumber?: boolean;
  onPlaySong?: (song: ISong) => void;
  extraMenuActions?: {
    icon?: React.ReactElement;
    label: React.ReactElement | string;
    action?: (song: ISong) => void;
    isAuth?: boolean;
  }[];
  onSortSongs?: () => void;
}

export default function TableSongItem({
  songs,
  className,
  showDuration = true,
  showAlbumInfo = true,
  showReleaseDate = true,
  showNumber = true,
  onPlaySong,
  extraMenuActions,
}: TableSongItemProps) {
  const {t} = useTranslation();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const {currentSong, paused, queueList} = useSelector(
    (state: IRootState) => state?.player,
  );
  const width = useWindowWidth();
  const isMobile = useMemo(() => width <= 834, [width]);
  const [openModalShare, setOpenModalShare] = useState<boolean>(false);
  const [addToPlaylistOpen, setAddToPlaylistOpen] = useState(false);
  const [menuAnchor, setMenuAnchor] = useState<null | HTMLElement>(null);
  const [selectedSong, setSelectedSong] = useState<ISong | null>(null);
  const [sortedSongs, setSortedSongs] = useState<ISong[]>(songs);
  const [isReversed, setIsReversed] = useState(false);
  const shareMutate = useMutation({
    mutationFn: ApiSong.shareSong,
    onSuccess: () => {
      if (selectedSong) {
        const link = generateShareLink({type: "song", data: selectedSong});
        navigator.clipboard.writeText(link).then(() => {
          toast.success(t("common.copy_link_success"));
        });
      }
    },
    onError: () => {
      toast.error(t("common.copy_link_failed"));
    },
  });

  const handlePlayMusic = (song: ISong) => {
    if (onPlaySong) {
      onPlaySong(song);
    } else {
      const isPlay = currentSong?.id === song?.id;
      if (isPlay) {
        if (paused) {
          PlayerUtil.instance.play();
        } else {
          PlayerUtil.instance.pause();
        }
      }
    }
  };

  const handleOpenMenu = (
    event: React.MouseEvent<HTMLButtonElement>,
    song: ISong,
  ) => {
    event.stopPropagation();
    const target = event.currentTarget;
    setSelectedSong(song);
    setTimeout(() => {
      setMenuAnchor(target);
    }, 0);
  };

  const handleCloseMenu = () => {
    setMenuAnchor(null);
  };

  const handleOpenModalShare = (song: ISong) => {
    setSelectedSong(song);
    setOpenModalShare(true);
  };

  const handleCloseModalShare = () => {
    setOpenModalShare(false);
    setSelectedSong(null);
  };

  const handleCopyLink = () => {
    if (selectedSong) {
      shareMutate.mutateAsync(selectedSong?.id ?? "");
    }
  };

  const handleAddSingleSongToQueue = (song: ISong) => {
    if (!song) {
      toast.info(t("common.song_not_found"));
      return;
    }

    const isExisted = queueList?.some((qSong) => qSong?.id === song?.id);

    if (isExisted) {
      toast.info(t("common.song_exist_queue"));
    } else {
      dispatch(addSongsToQueue([song]));
      toast.success(`${t("common.add_to_successfully")}`);
    }
  };

  const openAddToPlaylist = (song: ISong) => {
    setSelectedSong(song);
    setAddToPlaylistOpen(true);
  };

  const handleSortListSong = () => {
    if (isReversed) {
      const newSongs = [...songs].sort((a, b) => {
        if (a?.name && b?.name) {
          return a?.name?.localeCompare(b?.name);
        }
        return 0;
      });
      setSortedSongs(newSongs);
      setIsReversed(false);
    } else {
      const newSongs = [...songs].sort((a, b) => {
        if (a?.name && b?.name) {
          return b?.name?.localeCompare(a?.name);
        }
        return 0;
      });
      setSortedSongs(newSongs);
      setIsReversed(true);
    }
  };

  return (
    <>
      <div className={`overflow-x-auto ${className}`}>
        <TableContainer sx={{"& td, & th": {color: "white"}}}>
          <Table>
            <TableHead>
              <TableRow
                sx={{
                  "td,th": {borderColor: "rgba(255,255,255,0.1)"},
                }}
              >
                {showNumber && (
                  <TableCell
                    align="center"
                    sx={{
                      "fontWeight": "bold",
                      "display": {
                        xs: "none",
                        sm: "none",
                        md: "table-cell",
                      },
                      "width": {
                        xs: 40,
                        sm: 40,
                        md: 60,
                      },
                      "&:hover": {
                        cursor: "pointer",
                      },
                    }}
                  >
                    <p className="whitespace-nowrap overflow-hidden text-ellipsis md:text-xl">
                      #
                    </p>
                  </TableCell>
                )}
                <TableCell
                  sx={{
                    "fontWeight": "bold",
                    "width": "100%",
                    "&:hover": {
                      cursor: "pointer",
                    },
                  }}
                >
                  <div className="flex items-center gap-2 overflow-hidden whitespace-nowrap text-ellipsis">
                    <p className="md:text-xl">{t("common.song")}</p>
                    <SwapVertRounded
                      sx={{"&:hover": {cursor: "pointer"}}}
                      onClick={handleSortListSong}
                    />
                  </div>
                </TableCell>
                {showAlbumInfo && (
                  <TableCell
                    sx={{
                      "fontWeight": "bold",
                      "width": 100,
                      "display": {
                        xs: "none",
                        sm: "none",
                        lg: "table-cell",
                      },
                      "&:hover": {
                        cursor: "pointer",
                      },
                    }}
                  >
                    <p className="whitespace-nowrap overflow-hidden text-ellipsis md:text-xl">
                      {t("common.album")}
                    </p>
                  </TableCell>
                )}
                {showReleaseDate && (
                  <TableCell
                    sx={{
                      "fontWeight": "bold",
                      "width": 100,
                      "display": {
                        xs: "none",
                        sm: "none",
                        lg: "table-cell",
                      },
                      "&:hover": {
                        cursor: "pointer",
                      },
                    }}
                  >
                    <p className="whitespace-nowrap overflow-hidden text-ellipsis md:text-xl">
                      {t("common.release_date")}
                    </p>
                  </TableCell>
                )}
                {showDuration && (
                  <TableCell
                    align="right"
                    sx={{
                      "fontWeight": "bold",
                      "width": 100,
                      "&:hover": {
                        cursor: "pointer",
                      },
                      "fontSize": {
                        sm: "16px",
                        md: "18px",
                        lg: "20px",
                      },
                    }}
                  >
                    <p className="whitespace-nowrap overflow-hidden text-ellipsis md:text-xl">
                      {width > 640 ? t("common.duration") : ""}
                    </p>
                  </TableCell>
                )}
              </TableRow>
            </TableHead>
            <TableBody>
              {sortedSongs.map((song, index) => {
                const isPlay = currentSong?.id === song?.id;
                const hasPlaylist = (song?.playlists?.length ?? 0) > 0;

                return (
                  <TableRow
                    key={song?.id}
                    className="song-item group"
                    sx={{
                      "cursor": "pointer",
                      "backgroundColor": "inherit",
                      "&:hover": {
                        backgroundColor: "#ffffff1a",
                      },
                      "td,th": {borderColor: "rgba(255,255,255,0.1)"},
                    }}
                    onClick={() => handlePlayMusic(song)}
                  >
                    {showNumber && (
                      <TableCell align="center">
                        <div className="font-bold text-base text-center">
                          {index + 1}
                        </div>
                      </TableCell>
                    )}

                    <TableCell>
                      <div className="flex items-center gap-4">
                        <div className="relative min-w-10 min-h-10 w-10 h-10">
                          <img
                            src={
                              song?.images?.SMALL ||
                              song?.images?.DEFAULT ||
                              "/image/default-music.png"
                            }
                            className="w-full h-full rounded object-cover"
                          />
                          {isPlay ? (
                            <div className="flex items-center justify-center absolute top-0 left-0 w-10 h-10 rounded bg-[rgba(0,0,0,0.5)]">
                              {paused ? (
                                <IconPlay />
                              ) : (
                                <img
                                  src="/image/animation_play.gif"
                                  style={{
                                    filter:
                                      "grayscale(100%) brightness(0) invert(1)",
                                  }}
                                />
                              )}
                            </div>
                          ) : (
                            <div className="avatar-mask absolute top-0 left-0 w-10 h-10 rounded bg-[rgba(0,0,0,0.5)]">
                              <IconPlay />
                            </div>
                          )}
                        </div>
                        <div className="flex flex-col gap-y-1">
                          <div
                            className={`text-sm font-semibold line-clamp-1 ${
                              !isMobile && song.type === ESongType.SONG
                                ? "hover:text-sky-500"
                                : ""
                            }`}
                            onClick={(e) => {
                              if (
                                !isMobile &&
                                hasPlaylist &&
                                song.type === ESongType.SONG
                              ) {
                                e.stopPropagation();
                                navigate(
                                  `/playlist/${song?.playlists?.[0]?.urlSlug}`,
                                );
                              }
                            }}
                          >
                            {song?.name}
                          </div>
                          <div className="text-xs text-[rgba(255,255,255,0.5)]">
                            {song?.artists?.map((artist, artistIndex) => (
                              <span key={`song_item_artist_${artist?.id}`}>
                                <span
                                  className={clsx(
                                    !isMobile &&
                                      song.type === ESongType.SONG &&
                                      "hover:text-sky-500 hover:underline",
                                  )}
                                  onClick={(e) => {
                                    if (
                                      !isMobile &&
                                      song.type === ESongType.SONG
                                    ) {
                                      e.stopPropagation();
                                      navigate(`/artist/${artist?.urlSlug}`);
                                    }
                                  }}
                                >
                                  {artist?.stageName ?? artist?.name}
                                </span>
                                {artistIndex < (song.artists?.length || 0) - 1
                                  ? ", "
                                  : ""}
                              </span>
                            ))}
                          </div>
                        </div>
                      </div>
                    </TableCell>

                    {showAlbumInfo && (
                      <TableCell
                        sx={{
                          display: {
                            xs: "none",
                            sm: "none",
                            lg: "table-cell",
                          },
                        }}
                      >
                        <div className="text-[#FFFFFF80] text-sm">
                          {song?.playlists?.map((playlist, playlistIndex) => (
                            <span key={`song_item_album_${playlist?.urlSlug}`}>
                              <span
                                className={clsx(
                                  !isMobile &&
                                    "hover:text-sky-500 hover:underline",
                                )}
                                onClick={(e) => {
                                  if (!isMobile) {
                                    e.stopPropagation();
                                    navigate(`/playlist/${playlist?.urlSlug}`);
                                  }
                                }}
                              >
                                {playlist?.name}
                              </span>
                              {playlistIndex <
                                (song.playlists?.length || 0) - 1 && (
                                <span>,&nbsp;</span>
                              )}
                            </span>
                          ))}
                        </div>
                      </TableCell>
                    )}

                    {showReleaseDate && (
                      <TableCell
                        sx={{
                          display: {
                            xs: "none",
                            sm: "none",
                            lg: "table-cell",
                          },
                        }}
                      >
                        <span className="text-[#FFFFFF80] text-sm">
                          {convertDateSong(song?.releaseDate)}
                        </span>
                      </TableCell>
                    )}

                    {showDuration && (
                      <TableCell align="right">
                        <div className="flex items-center justify-end sm:w-[84px] ml-auto">
                          <div
                            className="flex justify-between items-center w-fit box-border sm:gap-4"
                            onClick={(e) => {
                              e.stopPropagation();
                            }}
                          >
                            <LikeButton
                              isLiked={song?.isLiked}
                              className="song-action hover:rounded-full"
                              action={() => handleLikeSong(song)}
                              songId={song?.id}
                            />
                            <IconButton
                              className="song-action"
                              onClick={(e) => handleOpenMenu(e, song)}
                            >
                              <IconMoreHorizontal />
                            </IconButton>
                          </div>
                          {showDuration && (
                            <div className="song-duration text-[#FFFFFF80] w-[80px] flex items-center justify-end line-clamp-1">
                              {convertPlayerTime(song?.duration)}
                            </div>
                          )}
                        </div>
                      </TableCell>
                    )}
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </TableContainer>
      </div>

      {selectedSong && (
        <>
          <PopupMenu
            data={selectedSong}
            menuArray={[
              {
                icon: <IconAdd24px />,
                label: t("common.menu.add_to_playlist"),
                action: () => openAddToPlaylist(selectedSong),
                isAuth: true,
              },
              {
                icon: <IconAdd24px />,
                label: t("common.add_to_queue"),
                action: () => handleAddSingleSongToQueue(selectedSong),
              },
              ...(extraMenuActions?.map((action) => ({
                ...action,
                action: () => action.action?.(selectedSong),
              })) || []),
            ].concat(
              selectedSong.type === ESongType.SONG
                ? {
                    icon: <IconShare height={22} width={22} />,
                    label: t("common.share"),
                    action: () => handleOpenModalShare(selectedSong),
                  }
                : [],
            )}
            anchorEl={menuAnchor}
            open={Boolean(menuAnchor)}
            onClose={handleCloseMenu}
          />

          <ModalAddToPlaylist
            open={addToPlaylistOpen}
            onClose={() => setAddToPlaylistOpen(false)}
            songData={selectedSong}
          />

          <ModalShare
            open={openModalShare}
            onCancel={handleCloseModalShare}
            handleCopyLink={handleCopyLink}
            image={selectedSong?.images?.SMALL || selectedSong?.images?.DEFAULT}
            name={selectedSong?.name}
            artists={selectedSong?.artists}
            shareUrl={generateShareLink({type: "song", data: selectedSong})}
          />
        </>
      )}
    </>
  );
}
