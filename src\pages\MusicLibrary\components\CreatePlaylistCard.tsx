import ModalCreatePlaylist from "@components/ModalCreatePlaylist";
import AddIcon from "@mui/icons-material/Add";
import {useState} from "react";
import {useTranslation} from "react-i18next";

interface IAlbumCardProps {
  className?: string;
  haveLayer?: boolean;
}

export default function CreatePlaylistCard({
  className,
  haveLayer = false,
}: IAlbumCardProps): JSX.Element {
  const {t} = useTranslation();
  const [openModal, setOpenModal] = useState<boolean>(false);

  const handleOpenModal = () => {
    setOpenModal(true);
  };

  const handleCloseModal = () => {
    setOpenModal(false);
  };

  return (
    <div
      className={`${className} relative w-full aspect-square group rounded-lg p-3 hover:bg-[#FFFFFF0F] cursor-pointer ${haveLayer ? "pt-[calc(10%+12px)]" : ""}`}
    >
      <ModalCreatePlaylist open={openModal} onClose={handleCloseModal} />
      <div className="relative rounded-lg flex" onClick={handleOpenModal}>
        {haveLayer && (
          <>
            <div className="bg-[#D9D9D91A] absolute left-1/2 bottom-0 -translate-x-1/2 rounded-lg w-4/5 h-[110%]" />
            <div className="bg-[#D9D9D94D] absolute rounded-lg w-[90%] left-1/2 z-5 h-[105%] translate-x-[-50%] bottom-0" />
          </>
        )}
        <div className="w-full gap-4 lg:gap-6 aspect-square rounded-lg z-10 bg-[linear-gradient(180deg,_#6F2A2A_8%,_#381212_100%)] text-white flex items-center justify-center flex-col">
          <div
            className="bg-[#FFFFFF1A] rounded-full w-8 h-8 sm:w-9 sm:h-9 md:w-10 md:h-10 lg:w-11 lg:h-11 flex items-center justify-center"
            onClick={handleOpenModal}
          >
            <AddIcon
              sx={{
                fontSize: {
                  xs: "18px",
                  sm: "20px",
                  md: "22px",
                  lg: "24px",
                  xl: "25px",
                },
              }}
            />
          </div>
          <div className="text-sm lg:text-base font-normal line-clamp-1">
            {t("common.create_new_playlist")}
          </div>
        </div>
      </div>
    </div>
  );
}
