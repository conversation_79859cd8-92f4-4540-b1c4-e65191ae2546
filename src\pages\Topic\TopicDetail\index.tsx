import {useParams} from "react-router-dom";
import ListSongs from "@components/ListSongs";
import ApiTopic from "@api/ApiTopic";
import {useQuery} from "@tanstack/react-query";
import QUERY_KEY from "@api/QueryKey";
import {useTranslation} from "react-i18next";
import {Skeleton} from "@mui/material";
import NotFound from "@components/NotFound/clientNotFound";
import {IParamsDefault} from "src/types";
import ListAlbums from "@components/ListAlbums";
import ListArtists from "@components/ListArtists";
import HeaderTitle from "@components/HeaderTitle";

const songParams: IParamsDefault = {
  page: 0,
  pageSize: 20,
};

const playlistParams: IParamsDefault = {
  page: 0,
  pageSize: 15,
};

const artistParams: IParamsDefault = {
  page: 0,
  pageSize: 10,
};

export default function TopicDetail() {
  const {slug} = useParams();
  const {t} = useTranslation();
  const {
    data: themeData,
    isLoading,
    isError,
  } = useQuery({
    queryKey: [QUERY_KEY.GENRE.GET_GENRE, slug],
    queryFn: () => ApiTopic.getGenre(slug || ""),
    enabled: !!slug,
  });

  const {data: songsData, isLoading: songLoading} = useQuery({
    queryKey: [QUERY_KEY.TOPIC.GET_SONG, slug, songParams],
    queryFn: () => ApiTopic.getThemeSongs(slug || "", songParams),
    enabled: !!slug,
  });

  const {data: playlistsData, isLoading: playlistLoading} = useQuery({
    queryKey: [QUERY_KEY.TOPIC.GET_PLAYLIST, slug, playlistParams],
    queryFn: () => ApiTopic.getThemePlaylists(slug || "", playlistParams),
    enabled: !!slug,
  });

  const {data: artistsData, isLoading: artistLoading} = useQuery({
    queryKey: [QUERY_KEY.TOPIC.GET_ARTIST, slug, artistParams],
    queryFn: () => ApiTopic.getThemeArtists(slug || "", artistParams),
    enabled: !!slug,
  });

  if (isError) {
    return (
      <div className="flex flex-1 justify-center items-center">
        <NotFound className="max-w-[90vw] max-h-[80vh]" />
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-4 sm:gap-5 md:gap-6 lg:gap-7 pt-5 px-4 sm:px-6 md:px-8">
      <HeaderTitle
        title={t("common.home_sidebar.topic")}
        name={themeData?.name}
      />
      <div className="relative overflow-hidden rounded-lg shadow-sm shadow-gray-700 w-full h-32 md:h-52 cursor-pointer">
        {isLoading ? (
          <Skeleton
            variant="rectangular"
            width="100%"
            height="100%"
            sx={{borderRadius: "8px", bgcolor: "#240606"}}
          />
        ) : (
          <>
            <img
              src={
                themeData?.images?.DEFAULT ||
                themeData?.images?.SMALL ||
                "/image/default-music.png"
              }
              alt={themeData?.name}
              className="object-cover w-full h-full"
            />
            <span className="text-center px-3 absolute inset-0 flex items-center justify-center font-black text-2xl md:text-5xl lg:text-7xl font-sans text-white bg-black/40">
              {themeData?.name}
            </span>
          </>
        )}
      </div>
      <ListAlbums
        data={playlistsData?.data}
        isLoading={playlistLoading}
        title={`${t("topic.albums_list_theme")} "${themeData ? themeData.name : ""}"`}
      />
      <ListSongs data={songsData?.data} isLoading={songLoading} />
      <ListArtists data={artistsData?.data} isLoading={artistLoading} />
    </div>
  );
}
