@use "../../styles/global.scss" as globals;

.song-table-wrapper {
  .table {
    display: table;
    width: 100%;
    border-collapse: collapse;
  }

  .table-header-group {
    display: table-header-group;
  }

  .table-row-group {
    display: table-row-group;
  }

  .table-row {
    display: table-row;
  }

  .table-cell {
    display: table-cell;
  }

  // Responsive behavior for table layout
  @media screen and (max-width: globals.$md) {
    .hidden-md {
      display: none !important;
    }
  }

  @media screen and (max-width: globals.$lg) {
    .hidden-lg {
      display: none !important;
    }
  }
}
