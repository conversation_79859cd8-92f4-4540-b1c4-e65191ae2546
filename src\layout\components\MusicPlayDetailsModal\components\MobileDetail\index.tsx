import {useMemo} from "react";
import {toggleLyrics} from "@redux/slices/PlayerSlice";
import {IRootState} from "@redux/store";
import {useDispatch, useSelector} from "react-redux";
import LyricsSong from "@components/LyricsSong";
import LikeButton from "@components/AuthButton/LikeButton";
import Controller from "src/layout/components/BottomPlayer/Controller";
import {useTranslation} from "react-i18next";
import {ESongType, IPlaylist} from "src/types";
import {useNavigate} from "react-router-dom";
import {handleLikeSong} from "src/utils/like";
import {Swiper, SwiperSlide} from "swiper/react";
import {Pagination} from "swiper/modules";
import "./index.scss";
import MusicQueue from "src/layout/components/MusicQueue";

interface MobileDetailProps {
  onSlideChange: (index: number) => void;
}

export default function MobileDetail({onSlideChange}: MobileDetailProps) {
  const {t} = useTranslation();
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const {currentSong, currentPlaylistId, paused} = useSelector(
    (state: IRootState) => state?.player,
  );

  const playlistUrl = useMemo(() => {
    return currentPlaylistId
      ? currentSong?.playlists?.find(
          (playlist: IPlaylist) => playlist?.id === currentPlaylistId,
        )?.urlSlug || currentSong?.playlists?.[0]?.urlSlug
      : currentSong?.playlists?.[0]?.urlSlug || "unknown-playlist";
  }, [currentSong, currentPlaylistId]);

  return (
    <div className="flex flex-col flex-1 relative overflow-hidden mobile-detail">
      <Swiper
        className="flex-1"
        onSlideChange={(swiper) => {
          onSlideChange(swiper.activeIndex);
        }}
        scrollbar={{
          hide: true,
        }}
        slidesPerView={1}
        modules={[Pagination]}
        pagination={{clickable: true}}
      >
        <SwiperSlide>
          <div className="h-full w-full flex flex-col justify-between text-white text-center py-12">
            <div className="flex flex-col items-center gap-4 justify-center">
              <div
                className="w-full overflow-hidden"
                id="mobile-youtube-player"
              />
              {currentSong?.type === ESongType.SONG ? (
                <img
                  id="spin-thumb"
                  src={
                    currentSong?.images?.DEFAULT ||
                    currentSong?.images?.SMALL ||
                    "/image/default-music.png"
                  }
                  style={{animationPlayState: paused ? "paused" : "running"}}
                  className="w-[80vw] rounded-full object-cover border-white border-2 animate-[spin_18s_linear_infinite]"
                />
              ) : null}
            </div>
            <div className="flex flex-col gap-y-10">
              <div className="flex flex-col text-left px-4 text-white">
                <div className="flex items-center justify-between">
                  <div
                    onClick={() => {
                      navigate(`/playlist/${playlistUrl}`);
                      dispatch(toggleLyrics());
                    }}
                    className="line-clamp-1 font-semibold text-xl text-[#FBFDFF]"
                  >
                    {currentSong?.name}
                  </div>
                  <LikeButton
                    isLiked={currentSong?.isLiked}
                    className="song-action hover:rounded-full"
                    action={() => handleLikeSong(currentSong)}
                    songId={currentSong?.id}
                  />
                </div>
                <div className="text-[#FBFDFF8C] text-sm">
                  {currentSong?.artists?.length
                    ? currentSong?.artists?.map((artist, index) => (
                        <span
                          key={artist?.id || index}
                          onClick={() => {
                            dispatch(toggleLyrics());
                            navigate(`/artist/${artist?.urlSlug || "unknown"}`);
                          }}
                        >
                          {artist?.stageName ?? artist?.name}
                          {index < (currentSong?.artists?.length ?? 0) - 1 &&
                            ", "}
                        </span>
                      ))
                    : t("common.not_info_artist")}
                </div>
              </div>
            </div>
          </div>
        </SwiperSlide>
        <SwiperSlide className="overflow-y-auto">
          <LyricsSong className="w-11/12 h-full text-center py-4" />
        </SwiperSlide>
        <SwiperSlide className="overflow-y-auto text-left">
          <MusicQueue className="h-full w-full pt-4" />
        </SwiperSlide>
      </Swiper>
      <Controller className="w-full py-4 text-white" inLyricDetail />
    </div>
  );
}
