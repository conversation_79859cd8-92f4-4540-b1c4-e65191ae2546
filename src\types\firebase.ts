import {AudioQualityTypeEnum, ESharePlatform} from ".";

type IFirebaseLogEventMapping = {
  logout: {
    device_type?: string;
    status?: string;
  };
  play_song: {
    song_id?: string;
    song_name?: string;
    song_artist?: string;
    play_position?: number;
    song_duration?: number;
    song_likes?: number;
    artist_names?: string;
    playlist_id?: string;
    playlist_name?: string;
    quality?: AudioQualityTypeEnum;
  };
  next_song: {
    song_id?: string;
    song_name?: string;
    play_position?: number;
    song_likes?: number;
    artist_names?: string;
    playlist_id?: string;
    playlist_name?: string;
  };
  pause_song: {
    song_id?: string;
    song_name?: string;
    play_position?: number;
    device_state?: string;
  };
  share_song: {
    song_name?: string;
    like_status?: string;
    share_platform?: ESharePlatform;
  };
  share_artist: {
    artist_id?: string;
    artist_name?: string;
    share_platform?: ESharePlatform;
  };
  skip_song: {
    song_id?: string;
    song_name?: string;
    play_position?: number;
    new_song_id?: string;
    new_song_name?: string;
    skip_position?: number;
    skip_direction?: string;
  };
  search_song: {
    search_query?: string;
    selected_result_id?: string;
    search_type?: string;
    is_select_search_result?: boolean;
    is_playing_song?: boolean;
    song_id?: string;
    song_name?: string;
    song_artist?: string;
  };
  add_to_playlist: {
    song_id?: string;
    song_name?: string;
    song_artist?: string;
    playlist_id?: string;
    playlist_name?: string;
    quality?: AudioQualityTypeEnum;
  };
  remove_from_playlist: {
    song_id?: string;
    song_name?: string;
    song_artist?: string;
    playlist_id?: string;
    playlist_name?: string;
    song_liked?: boolean;
    quality?: AudioQualityTypeEnum;
  };
  remove_from_queue: {
    song_id?: string;
    song_name?: string;
    song_artist?: string;
    play_position?: number;
    playlist_id?: string;
    playlist_name?: string;
    quality?: AudioQualityTypeEnum;
    next_song_id?: string;
    next_song_name?: string;
    queue_list_size?: number;
  };
  download_song: {
    song_id?: string;
    song_name?: string;
    song_artist?: string;
    play_position?: number;
    quality?: AudioQualityTypeEnum;
    like_count?: number;
    share_count?: number;
  };
  like_song: {
    song_id?: string;
    song_name?: string;
    song_artist?: string;
    play_position?: number;
    quality?: AudioQualityTypeEnum;
    like_status?: string;
    is_playing_song?: boolean;
  };
  unlike_song: {
    song_id?: string;
    song_name?: string;
    song_artist?: string;
    play_position?: number;
    quality?: AudioQualityTypeEnum;
    like_status?: string;
    is_playing_song?: boolean;
    is_skipping_song?: boolean;
    is_pause_song?: boolean;
    is_song_deleted_from_queue?: boolean;
  };
  interact_with_recommendation: {
    current_song_id?: string;
    current_playlist_id?: string;
    action?: string;
    next_song_id?: string;
    next_playlist_id?: string;
    recommendation_rank?: number;
  };
  favorite_artist: {
    current_song_id?: string;
    current_song_name?: string;
    current_song_artist?: string;
    artist_id?: string;
    artist_name?: string;
  };
  unfavorite_artist: {
    current_song_id?: string;
    current_song_name?: string;
    current_song_artist?: string;
    artist_id?: string;
    artist_name?: string;
  };
  on_off_loop_music: {
    song_id?: string;
    song_name?: string;
    song_artist?: string;
    play_position?: number;
  };
  lyric_song: {
    song_id?: string;
    song_name?: string;
    song_artist?: string;
    play_position?: number;
    is_song_liked?: boolean;
    like_count?: number;
    share_count?: number;
  };
  change_volume: {
    song_id?: string;
    song_name?: string;
    song_artist?: string;
    play_position?: number;
    volume_level?: number;
    old_volume_level?: number;
  };
  change_audio_settings: {
    setting_type?: string;
    new_value?: AudioQualityTypeEnum;
    song_id?: string;
    song_name?: string;
    play_position?: number;
  };
  change_language_settings: {
    setting_type?: string;
    new_value?: string;
  };
  change_profile: {
    which_value_changed?: string;
  };
  purchase_premium: {
    current_account_pack?: string;
    new_account_pack?: string;
    is_success?: boolean;
    country?: string;
  };
};

export type IFirebaseLogEventName = keyof IFirebaseLogEventMapping;
export type IFirebaseLogEventParams<T extends IFirebaseLogEventName> =
  IFirebaseLogEventMapping[T];
