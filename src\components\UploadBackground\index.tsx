import React from "react";
import {useState} from "react";

export default function UploadBackground() {
  const [selectedImage, setSelectedImage] = useState<File | null>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setSelectedImage(e.target.files[0]);
    }
  };
  return (
    <div>
      <label className="block text-sm font-bold text-gray-700">
        Ảnh bìa <span className="text-red-600">*</span>
      </label>
      {selectedImage ? (
        <div className="flex gap-4">
          <div className="mt-2 flex items-center justify-center rounded-lg w-full h-40">
            <img
              src={URL.createObjectURL(selectedImage)}
              alt="Preview"
              className="object-cover rounded-lg w-full h-full"
            />
          </div>
          {/* <div className="flex items-center justify-center">
            <div className="h-20 w-[2px] bg-[#C5C5C5]"></div>
          </div>
          <div className="mt-2 flex items-center justify-center rounded-lg border-2 border-dashed border-orange-500 bg-orange-800 h-40 w-40">
            <label className="flex flex-col items-center justify-center cursor-pointer">
              <span className="text-sm text-orange-500">+ Thay ảnh</span>
              <input
                type="file"
                accept="image/*"
                className="hidden"
                onChange={handleFileChange}
              />
            </label>
          </div> */}
        </div>
      ) : (
        <label className="mt-2 flex items-center justify-center w-full rounded-lg border-2 border-dashed  cursor-pointer border-orange-500 bg-orange-800 h-40">
          <div className="flex flex-col items-center justify-center ">
            <span className="text-sm text-orange-500">+ Thêm ảnh</span>
            <input
              type="file"
              accept="image/*"
              className="hidden"
              onChange={handleFileChange}
            />
          </div>
        </label>
      )}
    </div>
  );
}
