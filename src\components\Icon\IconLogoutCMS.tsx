import {SVGProps} from "react";

function IconLogoutCMS({
  width = "20",
  height = "20",
  ...props
}: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox={`0 0 ${width} ${height}`}
      className={props.className}
      fill="none"
      {...props}
    >
      <path
        d="M12.5 14.6876C12.4383 16.231 11.1525 17.541 9.43 17.4993C9.02917 17.4893 8.53333 17.3493 7.5425 17.0701C5.15833 16.3976 3.08833 15.2668 2.59167 12.7343C2.5 12.2701 2.5 11.746 2.5 10.6976V9.30263C2.5 8.25513 2.5 7.73097 2.59167 7.26513C3.08833 4.73347 5.15833 3.60263 7.5425 2.93013C8.53417 2.65097 9.02917 2.51097 9.43 2.50097C11.1525 2.4593 12.4383 3.7693 12.5 5.31263M17.5 10.0001H8.33333M17.5 10.0001C17.5 9.4168 15.8383 8.3268 15.4167 7.9168M17.5 10.0001C17.5 10.5835 15.8383 11.6735 15.4167 12.0835"
        stroke={props.stroke || "currentColor"}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}

export default IconLogoutCMS;
