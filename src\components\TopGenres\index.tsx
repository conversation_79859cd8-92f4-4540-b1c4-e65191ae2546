import {useQuery} from "@tanstack/react-query";
import {useNavigate} from "react-router-dom";
import {SwiperSlide} from "swiper/react";
import ApiHome, {IGetTopGenreParam} from "@api/ApiHome";
import QUERY_KEY from "@api/QueryKey";
import Slider from "@components/Slider";
import Subtitle from "@components/Subtitle";
import AlbumCardSkeleton from "@components/AlbumCardSkeleton";
import SubTitleSkeleton from "@components/SubTitleSkeleton";
import CommonAlbumCard from "@components/CommonAlbumCard";
import {IThemeAndGenre, PlaylistType} from "src/types";

interface ITopGenresProps {
  page: number;
  pageSize: number;
  playlistType: PlaylistType;
}

export default function TopGenres(props: ITopGenresProps) {
  const navigate = useNavigate();

  const params: IGetTopGenreParam = {
    page: props.page,
    pageSize: props.pageSize,
    playlistType: props.playlistType,
  };

  const {data: dataGetTopGenre, isLoading} = useQuery({
    queryKey: [QUERY_KEY.GENRE.GET_TOP_GENRE, params],
    queryFn: () => ApiHome.getTopGenre(params),
    // staleTime: 5 * 60 * 1000,
  });

  const goToSeeMoreGenres = (item: IThemeAndGenre) => {
    navigate(`/topic/${item.urlSlug}`);
  };

  return (
    <>
      {isLoading && (
        <div className="flex flex-col gap-2 sm:gap-3 md:gap-4 lg:gap-5">
          <SubTitleSkeleton />
          <Slider slidesPerView={5}>
            {[...Array(7)].map((_, index) => (
              <SwiperSlide key={index} virtualIndex={index}>
                <AlbumCardSkeleton isMultipleInfo={false} />
              </SwiperSlide>
            ))}
          </Slider>
        </div>
      )}
      {dataGetTopGenre?.data &&
        dataGetTopGenre?.data?.length > 0 &&
        dataGetTopGenre?.data?.map((item) => (
          <div
            key={`top_genre_${item.id}`}
            className="flex flex-col gap-2 sm:gap-3 md:gap-4 lg:gap-5"
          >
            <Subtitle
              subtitle={item?.name}
              handleClick={() => goToSeeMoreGenres(item)}
            />
            <Slider slidesPerView={5} spaceBetween={16}>
              {item?.playlists?.map((itemPlaylist, index) => (
                <SwiperSlide
                  key={`top_genre_playlist_${itemPlaylist.id}`}
                  virtualIndex={index}
                >
                  <CommonAlbumCard data={itemPlaylist} haveLayer={false} />
                </SwiperSlide>
              ))}
            </Slider>
          </div>
        ))}
    </>
  );
}
