import {SVGProps} from "react";

function IconAttachFile({
  width = "16",
  height = "16",
  ...props
}: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox={`0 0 ${width} ${height}`}
      className={props.className}
      fill="none"
      {...props}
    >
      <path
        d="M9.33317 1.33398H3.99984C3.64622 1.33398 3.30708 1.47446 3.05703 1.72451C2.80698 1.97456 2.6665 2.3137 2.6665 2.66732V13.334C2.6665 13.6876 2.80698 14.0267 3.05703 14.2768C3.30708 14.5268 3.64622 14.6673 3.99984 14.6673H11.9998C12.3535 14.6673 12.6926 14.5268 12.9426 14.2768C13.1927 14.0267 13.3332 13.6876 13.3332 13.334V5.33398L9.33317 1.33398Z"
        stroke={props.stroke || "#242728"}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M9.3335 1.33398V5.33398H13.3335"
        stroke={props.stroke || "#242728"}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M10.6668 8.66602H5.3335"
        stroke={props.stroke || "#242728"}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M10.6668 11.334H5.3335"
        stroke={props.stroke || "#242728"}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M6.66683 6H6.00016H5.3335"
        stroke={props.stroke || "#242728"}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}

export default IconAttachFile;
