import GlobalModalSlice from "@redux/slices/GlobalModalSlice";
import GlobalUpgradeModalSlice from "@redux/slices/UpgradeAccountSlice";
import LanguageSlice from "@redux/slices/LanguageSlice";
import GlobalSettingSlice from "@redux/slices/GlobalSettingSlice";
import PlayerSlice from "@redux/slices/PlayerSlice";
import UserReducer from "@redux/slices/UserSlice";
import WishlistSlice from "@redux/slices/WishlistSlice";
import {configureStore} from "@reduxjs/toolkit";
import {combineReducers} from "redux";
import {
  FLUSH,
  PAUSE,
  PERSIST,
  persistReducer,
  persistStore,
  PURGE,
  REGISTER,
  REHYDRATE,
} from "redux-persist";
import createWebStorage from "redux-persist/lib/storage/createWebStorage";
import Config from "../config";

const createNoopStorage = (): {
  getItem: (_key: string) => Promise<null>;
  setItem: (_key: string, value: unknown) => Promise<unknown>;
  removeItem: (_key: string) => Promise<void>;
} => {
  return {
    getItem(): Promise<null> {
      return Promise.resolve(null);
    },
    setItem(_key, value): Promise<unknown> {
      return Promise.resolve(value);
    },
    removeItem(): Promise<void> {
      return Promise.resolve();
    },
  };
};

const storage =
  typeof window !== "undefined"
    ? createWebStorage("local")
    : createNoopStorage();

const persistConfig = {
  key: Config.STORE_NAME,
  version: 1,
  storage: storage,
  blacklist: ["modal", "player", "accType"],
};

const playerPersistConfig = {
  key: "player",
  storage: storage,
  blacklist: ["paused", "isOpenLyrics", "suggestedSongs"],
};

const settingsPersistConfig = {
  key: "settings",
  storage: storage,
  blacklist: [],
};

const rootReducers = combineReducers({
  user: UserReducer,
  language: LanguageSlice,
  player: persistReducer(playerPersistConfig, PlayerSlice),
  wishlist: WishlistSlice,
  modal: GlobalModalSlice,
  settings: persistReducer(settingsPersistConfig, GlobalSettingSlice),
  accType: GlobalUpgradeModalSlice,
});

const persistedReducer = persistReducer(persistConfig, rootReducers);

const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER],
      },
    }),
});

export const persistor = persistStore(store);

export default store;

export type IRootState = ReturnType<typeof store.getState>;
export type IAppDispatch = typeof store.dispatch;
