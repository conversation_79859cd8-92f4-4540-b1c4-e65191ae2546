import React from "react";
import TableSongItemV2 from "./index";
import { ISong } from "../../types/song";

// Demo data
const demoSongs: ISong[] = [
  {
    id: "1",
    name: "Blinding Lights",
    duration: 200320, // 3:20 in milliseconds
    artists: [
      {
        id: "artist1",
        name: "The Weeknd",
        stageName: "The Weeknd",
        urlSlug: "the-weeknd"
      }
    ],
    images: {
      SMALL: "/image/default-music.png",
      DEFAULT: "/image/default-music.png"
    },
    playlists: [
      {
        id: "playlist1",
        name: "After Hours",
        urlSlug: "after-hours"
      }
    ],
    releaseDate: "2019-11-29",
    isLiked: false,
    type: "SONG" as any
  },
  {
    id: "2", 
    name: "Shape of <PERSON>",
    duration: 233712, // 3:53
    artists: [
      {
        id: "artist2",
        name: "<PERSON>", 
        stageName: "<PERSON>",
        urlSlug: "ed-sheeran"
      }
    ],
    images: {
      SMALL: "/image/default-music.png",
      DEFAULT: "/image/default-music.png"
    },
    playlists: [
      {
        id: "playlist2",
        name: "÷ (Divide)",
        urlSlug: "divide"
      }
    ],
    releaseDate: "2017-01-06",
    isLiked: true,
    type: "SONG" as any
  },
  {
    id: "3",
    name: "Someone Like You",
    duration: 285000, // 4:45
    artists: [
      {
        id: "artist3",
        name: "Adele",
        stageName: "Adele", 
        urlSlug: "adele"
      }
    ],
    images: {
      SMALL: "/image/default-music.png",
      DEFAULT: "/image/default-music.png"
    },
    playlists: [
      {
        id: "playlist3",
        name: "21",
        urlSlug: "21"
      }
    ],
    releaseDate: "2011-01-24",
    isLiked: false,
    type: "SONG" as any
  }
];

export default function TableSongItemV2Demo() {
  const handlePlaySong = (song: ISong) => {
    console.log("Playing song:", song.name);
  };

  const handleSortSongs = () => {
    console.log("Sorting songs");
  };

  return (
    <div className="p-4 bg-gray-900 min-h-screen">
      <h1 className="text-white text-3xl mb-6">TableSongItemV2 Demo</h1>
      
      <div className="mb-8">
        <h2 className="text-white text-xl mb-4">New Table Layout (using SongItem)</h2>
        <div className="bg-gray-800 rounded-lg p-4">
          <TableSongItemV2
            songs={demoSongs}
            onPlaySong={handlePlaySong}
            onSortSongs={handleSortSongs}
            showNumber={true}
            showDuration={true}
            showAlbumInfo={true}
            showReleaseDate={true}
            extraMenuActions={[
              {
                icon: <span>🎵</span>,
                label: "Custom Action",
                action: (song) => console.log("Custom action for:", song.name)
              }
            ]}
          />
        </div>
      </div>

      <div className="mb-8">
        <h2 className="text-white text-xl mb-4">Features</h2>
        <ul className="text-white space-y-2">
          <li>✅ Reuses SongItem component with table layout</li>
          <li>✅ Consistent styling and behavior</li>
          <li>✅ Responsive design (columns hide on smaller screens)</li>
          <li>✅ Sortable by song name</li>
          <li>✅ All SongItem features (like, menu, play/pause)</li>
          <li>✅ Custom menu actions support</li>
          <li>✅ Configurable columns (number, album, release date, duration)</li>
        </ul>
      </div>

      <div className="mb-8">
        <h2 className="text-white text-xl mb-4">Responsive Behavior</h2>
        <ul className="text-white space-y-2">
          <li>📱 Number column: Hidden on screens &lt; md (768px)</li>
          <li>📱 Album column: Hidden on screens &lt; lg (1024px)</li>
          <li>📱 Release Date column: Hidden on screens &lt; md (768px)</li>
          <li>📱 Duration column: Always visible</li>
        </ul>
      </div>
    </div>
  );
}
