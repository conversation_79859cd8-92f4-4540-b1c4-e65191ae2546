import ICBigCircle from "@components/Icon/ICBigCircle";
import ICCircle from "@components/Icon/ICCircle";
import ICEmptyCircle from "@components/Icon/ICEmptyCircle";
import ICStart from "@components/Icon/ICStart";
import {
  But<PERSON>,
  Card,
  CardActions,
  CardContent,
  Typography,
} from "@mui/material";
import {EGlobalModal, showModal} from "@redux/slices/GlobalModalSlice";
import {IRootState} from "@redux/store";
import clsx from "clsx";
import {useTranslation} from "react-i18next";
import {useDispatch, useSelector} from "react-redux";

interface BoxSuggestLoginProps {
  toggleDrawer: (value: boolean) => void;
}

export default function BoxSuggestLogin({
  toggleDrawer,
}: BoxSuggestLoginProps): JSX.Element {
  const {t} = useTranslation();
  const {accessToken} = useSelector((state: IRootState) => state.user);
  const dispatch = useDispatch();

  return (
    <div
      className={clsx(
        "w-full flex justify-center mt-3.5 box-border overflow-hidden rounded-xl",
        accessToken && "hidden",
      )}
    >
      <Card
        sx={{
          width: 200,
          borderRadius: "12px",
          background:
            "linear-gradient(128.48deg, #FF8C19 8.36%, #FF4501 87.81%)",
          color: "white",
          textAlign: "center",
          position: "relative",
        }}
      >
        <CardContent
          sx={{
            paddingBottom: "5px",
            display: "flex",
            gap: "8px",
            flexDirection: "column",
            overflow: "hidden",
          }}
        >
          <Typography
            gutterBottom
            variant="body2"
            component="div"
            sx={{fontWeight: "700", lineHeight: "17px"}}
          >
            {t("common.listen_music_free")}
          </Typography>
          <Typography
            sx={{
              color: "white",
              fontSize: "11px",
              fontWeight: "400",
              lineHeight: "17px",
            }}
          >
            {t("common.login_and_discover")}
          </Typography>
        </CardContent>
        <CardActions
          sx={{
            width: "100%",
            display: "flex",
            justifyContent: "center",
            marginBottom: "10px",
          }}
        >
          <Button
            onClick={() => {
              toggleDrawer(false);
              dispatch(showModal(EGlobalModal.AUTH_MODAL));
            }}
            size="small"
            sx={{
              color: "#FF4319",
              background: "white",
              borderRadius: "38px",
              width: "150px",
              fontWeight: "600",
              lineHeight: "14px",
              height: "30px",
              display: "flex",
              alignItems: "center",
              zIndex: "30",
            }}
          >
            {t("auth.login")}
          </Button>
        </CardActions>
        <div className="absolute w-fit h-fit left-0 top-1/2">
          <ICStart width={21} height={23} />
        </div>
        <div className="absolute left-0 top-1/2">
          <ICCircle />
        </div>
        <div className="absolute left-[55px] -top-4">
          <ICEmptyCircle />
        </div>
        <div className="absolute w-fit h-fit right-2 top-8">
          <ICStart width={21} height={23} />
        </div>
        <div className="absolute -top-4 left-[65px]">
          <ICBigCircle />
        </div>
      </Card>
    </div>
  );
}
