import ApiAutofill from "@api/ApiAutofill";
import IconAdd from "@components/Icon/IconAdd";
import SelectAutofill from "@components/SelectAutofill";
import {GridColDef, GridRowId, GridSortModel} from "@mui/x-data-grid";
import CmsTable from "@pages/Cms/components/CmsTable";
import {useEffect, useState} from "react";
import {
  convertDate,
  convertNumber,
  convertSongDuration,
} from "src/utils/timeUtils";
import {useTranslation} from "react-i18next";
import {EThemeAndGenreType, ISong, IThemeAndGenre} from "src/types";
import {keepPreviousData, useQuery} from "@tanstack/react-query";
import QUERY_KEY from "@api/QueryKey";
import SearchInput from "@pages/Cms/components/SearchInput";
import {LocalizationProvider} from "@mui/x-date-pickers/LocalizationProvider/LocalizationProvider";
import {AdapterDayjs} from "@mui/x-date-pickers/AdapterDayjs";
import {DatePicker} from "@mui/x-date-pickers/DatePicker";
import dayjs from "dayjs";
import GlobalButton from "@components/ButtonGlobal";
import ModalAddEditSong from "@pages/Cms/song/components/ModalAddEditSong";
import {IconButton, Tooltip} from "@mui/material";
import IconCmsEdit from "@components/Icon/IconCmsEdit";
import IconCmsDelete from "@components/Icon/IconCmsDelete";
import ModalDetailSong from "@pages/Cms/song/components/ModalDetailSong";
import ApiArtist from "@api/ApiArtist";

interface ListSongArtistProps {
  artistId?: string | null;
}

export default function ListSongArtist({artistId}: ListSongArtistProps) {
  const {t} = useTranslation();
  const [page, setPage] = useState(0);
  const [pageSize, setPageSize] = useState(10);

  const [sortModel, setSortModel] = useState<GridSortModel>([]);
  const [searchText, setSearchText] = useState("");
  const [debouncedSearchText, setDebouncedSearchText] = useState("");
  const [theme, setTheme] = useState("");
  const [genre, setGenre] = useState("");
  const [updateTime, setUpdateTime] = useState("");

  const [openModalSong, setOpenModalSong] = useState(false);
  const [openModalDetail, setOpenModalDetail] = useState(false);
  const [songId, setSongId] = useState<GridRowId>("");

  const getListSongArtist = useQuery({
    queryKey: [
      QUERY_KEY.ARTIST.GET_ALL_ARTIST_SONGS_CMS,
      page,
      pageSize,
      sortModel,
      theme,
      genre,
      debouncedSearchText,
      updateTime,
      artistId,
    ],
    placeholderData: keepPreviousData,
    queryFn: () =>
      ApiArtist.getArtistSongs(artistId ?? "", {
        page,
        pageSize,
        keyword: debouncedSearchText,
        themeId: theme,
        genreId: genre,
        updatedAt: updateTime,
        direction: sortModel[0]?.sort || "",
      }),
  });

  const tableRows = getListSongArtist?.data?.data?.map((item: ISong) => ({
    id: item?.id,
    image: item?.images?.DEFAULT,
    name: item?.name ?? "-",
    themes: item?.genres?.filter(
      (item) => item?.type === EThemeAndGenreType.THEME,
    ),
    genres: item?.genres?.filter(
      (item) => item?.type === EThemeAndGenreType.GENRE,
    ),
    releaseDate: item?.releaseDate,
    updatedAt: item?.updatedAt,
    favoriteCount: item?.totalLikes ?? "0",
    shareCount: item?.totalShares ?? "0",
    playlistCount: item?.totalAddedToPlaylists ?? "0",
    duration: item?.duration ?? "0",
  }));

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedSearchText(searchText);
    }, 300);

    return () => {
      clearTimeout(handler);
    };
  }, [searchText]);

  const columns: GridColDef[] = [
    {
      field: "image",
      headerName: t("common.cover_img"),
      width: 134,
      sortable: false,
      headerAlign: "center",
      align: "center",
      renderCell: (params) => (
        <div className="w-full h-full flex justify-center items-center">
          <img
            src={params.value || "/image/default-music.png"}
            className="h-12 w-12 rounded-[4px] object-cover"
          />
        </div>
      ),
    },
    {
      field: "name",
      headerName: t("cms.song.song_name"),
      width: 190,
      sortable: false,
      renderCell: (params) => (
        <Tooltip placement="left" arrow title={params.value}>
          <div
            style={{whiteSpace: "normal", wordWrap: "break-word"}}
            className="line-clamp-3"
          >
            {params.value}
          </div>
        </Tooltip>
      ),
    },
    {
      field: "themes",
      headerName: t("common.theme"),
      width: 105,
      sortable: false,
      headerAlign: "center",
      renderCell: (params) => {
        return (
          <Tooltip
            placement="left"
            arrow
            title={params?.value?.map((item: IThemeAndGenre) => (
              <span key={item?.id}>
                {item?.name}
                {params?.value?.length > 1 &&
                params?.value?.indexOf(item) < params?.value?.length - 1
                  ? ", "
                  : ""}
              </span>
            ))}
          >
            <div className="line-clamp-3">
              {params?.value?.map((item: IThemeAndGenre) => (
                <span key={item?.id}>
                  {item?.name}
                  {params?.value?.length > 1 &&
                  params?.value?.indexOf(item) < params?.value?.length - 1
                    ? ", "
                    : ""}
                </span>
              ))}
            </div>
          </Tooltip>
        );
      },
    },
    {
      field: "genres",
      headerName: t("common.genre"),
      sortable: false,
      width: 164,
      renderCell: (params) => {
        return (
          <div className="flex flex-wrap gap-2 max-h-[94px] overflow-hidden">
            {params?.value?.map((item: IThemeAndGenre) => (
              <span
                className="rounded-lg  py-0.5 px-2 bg-[#F2F2F3] border border-[#DCDCDC]"
                key={item?.id}
              >
                {item?.name}
              </span>
            ))}
          </div>
        );
      },
    },
    {
      field: "releaseDate",
      headerName: t("cms.song.release_time"),
      width: 186,
      sortable: false,
      headerAlign: "center",
      align: "left",
      renderCell: (params) => {
        return convertDate(params?.value);
      },
    },
    {
      field: "updatedAt",
      headerName: t("common.updated_time"),
      width: 186,
      sortable: false,
      headerAlign: "center",
      align: "left",
      renderCell: (params) => {
        return convertDate(params?.value);
      },
    },
    {
      field: "favoriteCount",
      headerName: t("common.likes"),
      type: "number",
      width: 160,
      headerAlign: "center",
      align: "left",
      sortable: false,
      hideSortIcons: false,
      renderCell: (params) => (
        <span className="text-[#FF4319]">{convertNumber(params?.value)}</span>
      ),
    },
    {
      field: "shareCount",
      headerName: t("common.shares"),
      sortable: false,
      type: "number",
      width: 118,
      headerAlign: "center",
      align: "left",
      renderCell: (params) => {
        return convertNumber(params?.value);
      },
    },
    {
      field: "playlistCount",
      headerName: t("cms.song.add_to_playlist_collection"),
      sortable: false,
      type: "number",
      width: 185,
      headerAlign: "center",
      align: "left",
      renderCell: (params) => {
        return convertNumber(params?.value);
      },
    },

    {
      field: "duration",
      headerName: t("cms.song.duration"),
      sortable: false,
      width: 133,
      renderCell: (params) => {
        return convertSongDuration(params?.value);
      },
    },
    {
      field: "actions",
      headerName: t("common.actions"),
      minWidth: 30,
      width: 120,
      sortable: false,
      disableColumnMenu: true,
      align: "center",
      headerAlign: "center",
      headerClassName: "sticky-header",
      cellClassName: "sticky-cell",
      renderCell: () => (
        <div className="flex justify-center">
          <IconButton
          // onClick={() => {
          //   handleOpenModalEdit(params.row.id);
          // }}
          >
            <IconCmsEdit />
          </IconButton>
          <IconButton
          // onClick={() => {
          //   handleDeleteArtist(params.row.id);
          // }}
          >
            <IconCmsDelete />
          </IconButton>
        </div>
      ),
    },
  ];

  return (
    <div className="bg-white space-y-4 rounded-[20px]">
      <div className="flex justify-between sm:items-center sm:flex-row flex-col sm:gap-0 gap-3 items-start">
        <div className="flex gap-3 flex-wrap w-full">
          <SearchInput
            searchText={searchText}
            className="py-[5px]"
            onChange={(v) => setSearchText(v)}
            placeholder={t("cms.song.song_name")}
          />

          <SelectAutofill
            name="selectTheme"
            suggestionAPI={ApiAutofill.autoTheme}
            selectProps={{
              className: "cms-select-gray",
              size: "small",
            }}
            onChange={(v) => setTheme(v)}
            placeholder={t("common.theme")}
          />

          <SelectAutofill
            name="selectGenre"
            suggestionAPI={ApiAutofill.autoGenre}
            selectProps={{
              className: "cms-select-gray",
              size: "small",
            }}
            onChange={(v) => setGenre(v)}
            placeholder={t("common.genre")}
          />
          <LocalizationProvider dateAdapter={AdapterDayjs}>
            <DatePicker
              disableFuture
              onChange={(v) => {
                if (v) {
                  setUpdateTime(dayjs(v).format("YYYY-MM-DD"));
                }
              }}
              slotProps={{
                textField: {
                  size: "small",
                  placeholder: t("cms.song.placeholder_updateTime"),
                },
                field: {
                  clearable: true,
                  onClear: () => setUpdateTime(""),
                },
              }}
              className="cms-datepicker-gray"
            />
          </LocalizationProvider>
        </div>
        <GlobalButton
          text={t("cms.song.btn_add")}
          startIcon={<IconAdd />}
          className="w-auto whitespace-nowrap"
          onClick={() => setOpenModalSong(true)}
        />
      </div>

      <CmsTable
        rows={tableRows}
        ordinalColumn
        columns={columns}
        loading={getListSongArtist?.isLoading}
        totalItems={getListSongArtist?.data?.meta?.totalItems || 0}
        currentPage={page}
        onPageChange={(newPage) => setPage(newPage)}
        rowsPerPage={pageSize}
        onRowsPerPageChange={(newPageSize) => setPageSize(newPageSize)}
        onSortModelChange={(model) => setSortModel(model)}
        onRowDoubleClick={(params) => {
          setOpenModalDetail(true);
          setSongId(params?.id);
        }}
        hideFooter
        className="max-h-[50vh] overflow-y-auto"
      />

      <ModalAddEditSong
        open={openModalSong}
        onClose={() => setOpenModalSong(false)}
        refetch={getListSongArtist.refetch}
      />

      {openModalDetail && (
        <ModalDetailSong
          open={openModalDetail}
          onClose={() => {
            setOpenModalDetail(false);
            setSongId("" as GridRowId);
          }}
          songId={songId}
        />
      )}
    </div>
  );
}
