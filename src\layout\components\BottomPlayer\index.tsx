import {IRootState} from "@redux/store";
import {useDispatch, useSelector} from "react-redux";
import ActionBar from "./ActionBar";
import Controller from "./Controller";
import SongInfo from "./SongInfo";
import {toggleLyrics, updateCurrentSong} from "@redux/slices/PlayerSlice";
import {useWindowWidth} from "src/utils/hooks";
import Color from "color";
import {useLayoutEffect, useMemo} from "react";
import clsx from "clsx";
import {useQuery} from "@tanstack/react-query";
import ApiSong from "@api/ApiSong";
import QUERY_KEY from "@api/QueryKey";

interface IBottomPlayerProps {
  className?: string;
}

export default function BottomPlayer({className}: IBottomPlayerProps) {
  const dispatch = useDispatch();
  const width = useWindowWidth();
  const isMobile = useMemo(() => width <= 834, [width]);
  const {currentSong, isOpenLyrics} = useSelector(
    (state: IRootState) => state?.player,
  );
  const {data: dataInteractionsSong, isSuccess} = useQuery({
    queryKey: [QUERY_KEY.SONG.GET_INTERACTIONS_SONG, currentSong?.id],
    queryFn: () => ApiSong.getInteractionsSongById(currentSong?.id),
    enabled: !!currentSong?.id,
  });

  useLayoutEffect(() => {
    if (isSuccess && currentSong && currentSong.id) {
      dispatch(
        updateCurrentSong({
          ...currentSong,
          isLiked:
            dataInteractionsSong?.isLiked ?? currentSong?.isLiked ?? false,
          totalLikes:
            dataInteractionsSong?.totalLikes ?? currentSong?.totalLikes ?? 0,
        }),
      );
    }
  }, [isSuccess]);

  return (
    <div
      className={clsx(
        "w-full transition-500 justify-between items-center text-white py-4 px-8 z-30 max-[568px]:px-[10px]",
        currentSong && !isOpenLyrics ? "flex" : "hidden",
        className,
      )}
      style={{
        background: `linear-gradient(to top,
        ${Color(currentSong?.keyColor || "#2D0707CC")
          .desaturate(0.5)
          .darken(0.8)
          .string()} 0%,
        ${Color(currentSong?.keyColor || "#2D0707CC")
          .desaturate(0)
          .darken(0.7)
          .string()} 50%,
        ${Color(currentSong?.keyColor || "#2D0707CC")
          .lighten(0.5)
          .darken(0.7)
          .string()} 100%)`,
      }}
      onClick={() => isMobile && dispatch(toggleLyrics())}
    >
      <SongInfo />
      <Controller
        className="md:w-3/5 mx-20 max-[834px]:mx-0"
        timeClassName="hidden md:block"
        sliderClassName="max-[568px]:pointer-events-none max-[568px]:absolute max-[568px]:left-0 max-[568px]:bottom-0 max-[568px]:bg-transparent"
      />
      <ActionBar data={currentSong} />
    </div>
  );
}
