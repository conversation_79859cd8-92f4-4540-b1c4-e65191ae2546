// NAME
const STORE_NAME = "state";

// NETWORK
const NETWORK_CONFIG = {
  API_BASE_URL: import.meta.env.VITE_API_BASE_URL + "/api/v1",
  TIMEOUT: 30000,
  RETRY: false,
  USE_TOKEN: true,
  WITH_METADATA: false,
  DISPLAY_ERROR: true,
};

// PATHNAME
const PATHNAME = {
  HOME: "/",
  LOGIN: "/login",
};

// LAYOUT
const LAYOUT_CONFIG = {
  useSidebar: true,
  useNavbar: true,
  useFooter: true,
  useBottomNavigator: true,
};

// LANGUAGE
const LANGUAGE = {
  DEFAULT: "lo",
};

// FIREBASE
const FIREBASE_DEV = {
  apiKey: "AIzaSyA1G8H8AuTmdZK5UUusN1-DIFH4fasdcc8",
  authDomain: "tina-music.firebaseapp.com",
  projectId: "tina-music",
  storageBucket: "tina-music.firebasestorage.app",
  messagingSenderId: "654586374553",
  appId: "1:654586374553:web:af9a72e69babc9fbfe4b08",
  measurementId: "G-Y6R6JHTXP5",
};

const FIREBASE = {
  apiKey: "AIzaSyAKGWrx6EunfnvrwrHsfDznPTz5b7tsdbQ",
  authDomain: "tinamusic-34a00.firebaseapp.com",
  projectId: "tinamusic-34a00",
  storageBucket: "tinamusic-34a00.firebasestorage.app",
  messagingSenderId: "1046155418802",
  appId: "1:1046155418802:web:75d827dd2191afe192bdab",
  measurementId: "G-H0B9H7RRMZ",
};

const SERVICE_CODE = {
  code: "704241602201",
  subserviceCode: {
    laoMusicPremium: "70424160220101",
  },
};

export default {
  STORE_NAME,
  NETWORK_CONFIG,
  PATHNAME,
  LAYOUT_CONFIG,
  LANGUAGE,
  FIREBASE,
  FIREBASE_DEV,
  SERVICE_CODE,
};
