import {EActivityStatus, EArtistType, GenderEnum, IArtist} from "src/types";
import {fetcher, fetcherWithMetadata, IDataWithMeta} from "./Fetcher";

export interface IGetAllArtistCmsParams {
  page?: number;
  pageSize?: number;
  status?: EActivityStatus;
  artistType?: EArtistType;
  country?: string;
  order?: string;
  direction?: string;
  keyword?: string;
  fromDate?: string;
  toDate?: string;
  gender?: GenderEnum;
}

export interface IGetArtistSongsAndAlbumsParam {
  themeId?: string;
  genreId?: string;
  albumId?: string;
  updatedAt?: string;
  keyword?: string;
  fromDate?: string;
  toDate?: string;
  order?: string;
  direction?: string;
  page: number;
  pageSize: number;
}

export interface ICreateArtist {
  image?: string;
  name: string;
  stageName?: string;
  dateOfBirth?: string;
  gender: GenderEnum;
  type?: EArtistType;
  company?: string;
  country: string;
  biography?: string;
}

const path = {
  cmsArtist: "cms/artist",
};

function getAllArtist(
  params: IGetAllArtistCmsParams,
): Promise<IDataWithMeta<[]>> {
  return fetcherWithMetadata(
    {
      url: path.cmsArtist,
      method: "get",
      params,
    },
    {
      displayError: false,
    },
  );
}

function addArtist(data: ICreateArtist): Promise<IArtist> {
  return fetcher(
    {
      url: path.cmsArtist,
      method: "post",
      data: data,
      headers: {"Content-Type": "multipart/form-data"},
    },
    {displayError: true},
  );
}

function getArtist(id: string): Promise<IArtist> {
  return fetcher(
    {url: `${path.cmsArtist}/${id}`, method: "get"},
    {displayError: false},
  );
}

function updateArtist({
  id,
  ...restData
}: {id: string} & ICreateArtist): Promise<IArtist> {
  return fetcher(
    {
      url: `${path.cmsArtist}/${id}`,
      method: "patch",
      data: restData,
    },
    {
      isFormData: true,
    },
  );
}

function deleteArtist(id: string) {
  return fetcher({
    url: `${path.cmsArtist}/${id}`,
    method: "delete",
  });
}

function getArtistSongs(
  id: string,
  params: IGetArtistSongsAndAlbumsParam,
): Promise<IDataWithMeta<[]>> {
  return fetcherWithMetadata(
    {
      url: `${path.cmsArtist}/${id}/songs`,
      method: "get",
      params,
    },
    {
      displayError: false,
    },
  );
}

function getArtistAlbums(
  id: string,
  params: IGetArtistSongsAndAlbumsParam,
): Promise<IDataWithMeta<[]>> {
  return fetcherWithMetadata(
    {
      url: `${path.cmsArtist}/${id}/playlists`,
      method: "get",
      params,
    },
    {
      displayError: false,
    },
  );
}

export default {
  getAllArtist,
  addArtist,
  getArtist,
  updateArtist,
  deleteArtist,
  getArtistSongs,
  getArtistAlbums,
};
