import {SVGProps} from "react";

function IconUser3({
  width = "24",
  height = "24",
  ...props
}: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox={`0 0 ${width} ${height}`}
      className={props.className}
      fill="none"
      {...props}
    >
      <path
        d="M17.1334 18.479C16.7468 17.8968 16.2271 17.3996 15.5891 17.0073C14.5816 16.3874 13.34 16.0602 12.0001 16.0602C10.6861 16.0602 9.41101 16.402 8.40932 17.0222C7.77724 17.4138 7.26126 17.9059 6.87582 18.479M20.5 12C20.5 16.6944 16.6944 20.5 12 20.5C7.30558 20.5 3.5 16.6944 3.5 12C3.5 7.30558 7.30558 3.5 12 3.5C16.6944 3.5 20.5 7.30558 20.5 12ZM10.3322 8.80307C10.7891 8.31259 11.4208 8.05882 12.0983 8.05882C12.7746 8.05882 13.4068 8.318 13.8609 8.80591C14.3206 9.29978 14.5321 9.96067 14.4833 10.646C14.4373 11.2825 14.1826 11.8878 13.7548 12.3476C13.3154 12.8195 12.7293 13.1021 12.0983 13.1021C11.4675 13.1021 10.8809 12.8189 10.4409 12.3479C10.0125 11.8878 9.75733 11.2821 9.71235 10.6453C9.66375 9.95746 9.87342 9.2956 10.3322 8.80307Z"
        stroke={props.fill || "currentColor"}
        strokeWidth="2"
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}

export default IconUser3;
