<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Embed-Laomusic</title>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100..900;1,100..900&display=swap"
      rel="stylesheet"
    />
    <style>
      body {
        font-family: "Roboto", sans-serif;
        height: 100vh;
        padding: 0 !important;
        margin: 0 !important;
        box-sizing: inherit;
      }

      .embed-card {
        display: flex;
        background-color: #771313;
        border-radius: 16px;
        padding: 20px;
        color: #fff;
        gap: 28px;
        align-items: flex-start;
        position: relative;
      }

      img {
        height: auto;
        width: 30%;
        border-radius: 10px;
        object-fit: cover;
        aspect-ratio: 1 / 1;
        border: 1px solid #ffffff12;
      }

      .embed-card_infor {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        width: 100%;
        height: calc(100vh - 40px);
      }

      .embed-card-content {
        display: flex;
        flex-direction: column;
        gap: 12px;
      }

      .embed-card-title {
        font-size: 1.75rem;
        font-weight: 500;
        overflow: hidden;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 1;
      }

      .embed-card-meta {
        display: flex;
        align-items: center;
        gap: 8px;
        color: #ffffff;
        font-size: 1rem;
        font-weight: 400;
      }

      .embed-card-badge {
        background-color: #390d0d;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 0.75rem;
      }

      .embed-card-artist {
        font-size: 1.125rem;
        color: #ffffff80;
        overflow: hidden;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 1;
      }

      .embed-card-actions {
        display: flex;
        flex-direction: row;
        align-items: center;
        gap: 8px;
        justify-content: flex-end;
      }

      .embed-card-actions svg {
        cursor: pointer;
      }

      #overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.8);
        color: white;
        display: none;
        flex-direction: column;
        justify-content: center;
        align-items: flex-start;
        z-index: 9999;
      }

      #overlay-text {
        font-size: 1.5rem;
        margin: 0 0 20px 20px;
      }

      #btn-close-overlay {
        position: absolute;
        top: 20px;
        right: 20px;
        font-size: 1.25rem;
        padding: 5px 10px;
        cursor: pointer;
        background: none;
        border: none;
        color: white;
      }

      #btn-link {
        font-size: 1.125rem;
        padding: 10px 20px;
        cursor: pointer;
        border-radius: 100px;
        margin-left: 20px;
      }

      #loading-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: #771313;
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
      }

      .spinner {
        border: 4px solid #f3f3f3;
        border-top: 4px solid #771313;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        animation: spin 1s linear infinite;
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }

      .embed-card.hidden {
        visibility: hidden;
      }

      @media (max-height: 220px) {
        .embed-card {
          padding: 15px;
        }
        .embed-card_infor {
          height: calc(100vh - 30px);
        }
        .embed-card-content {
          gap: 8px;
        }
        .embed-card-title {
          font-size: 1.5rem;
        }
        .embed-card-badge {
          font-size: 0.625rem;
        }
        .embed-card-artist {
          font-size: 1rem;
        }
        #overlay-text {
          font-size: 1.25rem;
          margin-bottom: 16px;
        }
        #btn-link {
          font-size: 1rem;
          padding: 8px 16px;
        }
      }

      @media (max-height: 180px) {
        .embed-card {
          padding: 10px;
        }
        .embed-card_infor {
          height: calc(100vh - 20px);
        }
        .embed-card-content {
          gap: 4px;
        }
        .embed-card-title {
          font-size: 1.25rem;
        }
        .embed-card-badge {
          font-size: 0.5rem;
        }
        .embed-card-artist {
          font-size: 0.875rem;
        }
        #overlay-text {
          font-size: 1rem;
          margin-bottom: 12px;
        }
        #btn-link {
          font-size: 0.875rem;
          padding: 6px 12px;
        }
      }
    </style>
  </head>
  <body>
    <div class="embed-card">
      <img src="" alt="Playlist Thumbnail" id="thumbnail" />
      <div class="embed-card_infor">
        <div class="embed-card-content">
          <span class="embed-card-title" id="song-title"></span>
          <div class="embed-card-meta">
            <span class="embed-card-badge">Preview</span>
            <span class="embed-card-artist" id="song-artist"></span>
          </div>
        </div>
        <div class="embed-card-actions">
          <svg
            width="36"
            height="36"
            viewBox="0 0 36 36"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M9.5 18C10.3284 18 11 17.3284 11 16.5C11 15.6716 10.3284 15 9.5 15C8.67157 15 8 15.6716 8 16.5C8 17.3284 8.67157 18 9.5 18Z"
              fill="white"
              fill-opacity="0.7"
            />
            <path
              d="M16.5 18C17.3284 18 18 17.3284 18 16.5C18 15.6716 17.3284 15 16.5 15C15.6716 15 15 15.6716 15 16.5C15 17.3284 15.6716 18 16.5 18Z"
              fill="white"
              fill-opacity="0.7"
            />
            <path
              d="M23.5 18C24.3284 18 25 17.3284 25 16.5C25 15.6716 24.3284 15 23.5 15C22.6716 15 22 15.6716 22 16.5C22 17.3284 22.6716 18 23.5 18Z"
              fill="white"
              fill-opacity="0.7"
            />
          </svg>

          <svg
            id="btn-play"
            width="36"
            height="36"
            viewBox="0 0 36 36"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <circle cx="18" cy="18" r="18" fill="white" />
            <path
              fill-rule="evenodd"
              clip-rule="evenodd"
              d="M24.764 17.1239C25.4548 17.5038 25.4548 18.4964 24.764 18.8764L14.9819 24.2565C14.3155 24.6231 13.5 24.1409 13.5 23.3803V12.62C13.5 11.8594 14.3155 11.3772 14.9819 11.7438L24.764 17.1239Z"
              fill="#771313"
            />
          </svg>
        </div>
      </div>
    </div>

    <audio id="audio-player" src=""></audio>

    <script>
      const embedCard = document.querySelector(".embed-card");
      embedCard.classList.add("hidden");

      const loadingOverlay = document.createElement("div");
      loadingOverlay.id = "loading-overlay";
      loadingOverlay.innerHTML = `<div class="spinner"></div>`;
      document.body.appendChild(loadingOverlay);

      const audio = document.getElementById("audio-player");
      const btnPlay = document.getElementById("btn-play");
      const thumbnail = document.getElementById("thumbnail");
      const songTitle = document.getElementById("song-title");
      const artistSpan = document.getElementById("song-artist");
      let currentSongData = null;
      const urlParams = new URLSearchParams(window.location.search);
      const songId = urlParams.get("songId");

      if (songId) {
        fetch(`https://laomusic.net/api/v1/songs/embedded/${songId}`)
          .then((response) => {
            if (!response.ok) {
              const overlay = document.getElementById("overlay");
              const overlayText = document.getElementById("overlay-text");
              const btnClose = document.getElementById("btn-close-overlay");

              if (overlay && overlayText) {
                overlay.style.display = "flex";
                btnClose.style.display = "none";
              }
              return;
            }
            return response.json();
          })
          .then((data) => {
            currentSongData = data?.data;

            if (!currentSongData) {
              throw new Error("Không tìm thấy bài hát");
            }

            loadingOverlay.remove();
            embedCard.classList.remove("hidden");

            songTitle.textContent = currentSongData?.name;
            thumbnail.src =
              currentSongData?.images?.DEFAULT ||
              currentSongData?.images?.SMALL ||
              "/image/default-music.png";
            thumbnail.alt = currentSongData?.name || "Thumbnail";
            audio.src = currentSongData?.audioUrl || "";

            artistSpan.innerHTML = "";
            currentSongData?.artist?.forEach((artist, index) => {
              const span = document.createElement("span");
              span.textContent = artist?.stageName || artist?.name;
              span.style.cursor = "pointer";
              span.addEventListener("click", () => {
                const artistUrl = `https://laomusic.net/artist/${artist?.urlSlug}`;
                window.open(artistUrl, "_blank");
              });
              artistSpan.appendChild(span);

              if (index < currentSongData?.artist.length - 1) {
                artistSpan.appendChild(document.createTextNode(", "));
              }
            });

            function handleClickPlaylist() {
              const urlSlug = currentSongData?.playlistUrlSlug;
              const playlistUrl = `https://laomusic.net/playlist/${urlSlug}`;
              window.open(playlistUrl, "_blank");
            }

            thumbnail.style.cursor = "pointer";
            thumbnail.addEventListener("click", handleClickPlaylist);

            songTitle.style.cursor = "pointer";
            songTitle.addEventListener("click", handleClickPlaylist);

            btnPlay.addEventListener("click", () => {
              if (currentSongData?.type === 1) {
                // Hiện overlay
                overlay.style.display = "flex";
                // Nếu đang play thì pause luôn
                if (isPlaying) {
                  audio.pause();
                }
              } else {
                if (isPlaying) {
                  audio.pause();
                } else {
                  audio.play();
                }
              }
            });
          })
          .catch((error) => {
            const overlay = document.getElementById("overlay");
            const overlayText = document.getElementById("overlay-text");
            const btnClose = document.getElementById("btn-close-overlay");

            if (overlay && overlayText) {
              overlay.style.display = "flex";
              btnClose.style.display = "none";
            }
            return;
          });
      }

      let isPlaying = false;

      const overlay = document.createElement("div");
      overlay.id = "overlay";
      embedCard.appendChild(overlay);
      overlay.style.display = "none";
      overlay.innerHTML = `
        <div id="overlay-text">Bài hát này không được hỗ trợ nhúng</div>
        <button id="btn-close-overlay">x</button>
        <button id="btn-link">Truy cập laomusic.net</button>
      `;
      document.body.appendChild(overlay);

      document.getElementById("btn-close-overlay").onclick = () => {
        overlay.style.display = "none";
      };

      document.getElementById("btn-link").onclick = () => {
        window.open("https://laomusic.net/", "_blank");
      };

      audio.addEventListener("play", () => {
        isPlaying = true;
        btnPlay.innerHTML = `
          <circle cx="18" cy="18" r="18" fill="white" />
          <rect x="13" y="12" width="4" height="12" fill="#771313"/>
          <rect x="19" y="12" width="4" height="12" fill="#771313"/>
        `;
      });

      audio.addEventListener("pause", () => {
        isPlaying = false;
        btnPlay.innerHTML = `
          <circle cx="18" cy="18" r="18" fill="white" />
          <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M24.764 17.1239C25.4548 17.5038 25.4548 18.4964 24.764 18.8764L14.9819 24.2565C14.3155 24.6231 13.5 24.1409 13.5 23.3803V12.62C13.5 11.8594 14.3155 11.3772 14.9819 11.7438L24.764 17.1239Z"
            fill="#771313"
          />
        `;
      });
    </script>
  </body>
</html>
