import IconPlay from "@components/Icon/IconPlay";
import IconTrashSearch from "@components/Icon/IconTrashSearch";
import {IRootState} from "@redux/store";
import {useSelector} from "react-redux";
import {ESearchType, IArtist, ISearchItem, ISong} from "src/types";
import "./SearchItem.scss";
import PlayerUtil from "src/core/PlayerUtil";
import {useNavigate} from "react-router-dom";
import {useWindowWidth} from "src/utils/hooks";
import {useMemo} from "react";
import {useTranslation} from "react-i18next";

interface SearchItemProps {
  item: ISearchItem;
  onRemove?: () => void;
  onClick: () => void;
}

interface KeywordSearchItemProps {
  item: string;
  onRemove?: () => void;
  onClick: () => void;
}

interface SongSearchItemProps {
  item: ISong;
  onRemove?: () => void;
  onClick: () => void;
}

interface ArtistSearchItemProps {
  item: IArtist;
  onRemove?: () => void;
  onClick: () => void;
}

const KeywordSearchItem = ({
  item,
  onRemove,
  onClick,
}: KeywordSearchItemProps) => {
  return (
    <button
      onClick={() => onClick()}
      className="flex items-center justify-between hover:bg-[#FFFFFF12] text-[#FFFFFF] p-2.5 rounded-lg cursor-pointer text-sm flex-shrink-0"
    >
      <div className="text-sm font-medium truncate block">{item}</div>
      {onRemove && (
        <div>
          <IconTrashSearch
            onClick={(e) => {
              e.stopPropagation();
              onRemove?.();
            }}
            onMouseDown={(e) => e.stopPropagation()}
            className="text-gray-400 hover:text-white text-xs"
          />
        </div>
      )}
    </button>
  );
};

const SongSearchItem = ({item, onRemove, onClick}: SongSearchItemProps) => {
  const navigate = useNavigate();
  const {currentSong, paused} = useSelector(
    (state: IRootState) => state?.player,
  );
  const hasPlaylist = (item?.playlists?.length ?? 0) > 0;
  const isPlay = currentSong?.id === item?.id;
  const width = useWindowWidth();
  const isMobile = useMemo(() => width < 1024, [width]);

  return (
    <button
      onClick={() => {
        if (isPlay) {
          if (paused) {
            PlayerUtil.instance.play();
          } else {
            PlayerUtil.instance.pause();
          }
        } else {
          onClick();
        }
      }}
      className="search-song-item flex items-center justify-between hover:bg-[#FFFFFF12] text-[#FFFFFF] py-1.5 px-2.5 rounded-lg cursor-pointer text-sm"
    >
      <div className="flex items-center gap-4">
        <div className="flex-shrink-0 relative avatar-wrapper w-10 h-10">
          <img
            src={
              item?.images?.SMALL ||
              item?.images?.DEFAULT ||
              "/image/default-music.png"
            }
            className="w-full h-full rounded object-cover"
          />
          {isPlay ? (
            <div className="flex items-center justify-center absolute  top-0 left-0 w-10 h-10 rounded bg-[rgba(0,0,0,0.5)]">
              {paused ? (
                <IconPlay />
              ) : (
                <img
                  src="/image/animation_play.gif"
                  style={{filter: "grayscale(100%) brightness(0) invert(1)"}}
                />
              )}
            </div>
          ) : (
            <div className="avatar-mask absolute top-0 left-0 w-10 h-10 rounded bg-[rgba(0,0,0,0.5)]">
              <IconPlay />
            </div>
          )}
        </div>
        <div className="flex flex-col text-left gap-y-1 justify-center overflow-hidden">
          <div className="flex items-center gap-x-2.5 text-white">
            <div
              className={
                "text-sm font-semibold line-clamp-1 lg:hover:text-sky-500"
              }
              onClick={(e) => {
                if (!isMobile && hasPlaylist) {
                  e.stopPropagation();
                  navigate(`/playlist/${item?.playlists?.[0]?.urlSlug}`);
                } else {
                  PlayerUtil.instance?.play?.();
                }
              }}
            >
              {item?.name}
            </div>
          </div>
          <div className="text-xs text-[rgba(255,255,255,0.5)] overflow-hidden text-ellipsis whitespace-nowrap lg:w-44 group-hover:w-36 group-hover:max-md:w-28 max-md:w-32 max-[412px]:w-28">
            {item?.artists && item?.artists?.length > 0 ? (
              item?.artists?.map((artist, index) => (
                <span key={`song_item_artist_${artist?.id}`}>
                  <span
                    className="lg:hover:text-sky-500 hover:underline"
                    onClick={(e) => {
                      if (!isMobile) {
                        e.stopPropagation();
                        navigate(`/artist/${artist?.urlSlug}`);
                      } else {
                        PlayerUtil.instance?.play?.();
                      }
                    }}
                  >
                    {artist?.stageName ?? artist?.name}
                  </span>
                  {index < (item.artists?.length || 0) - 1 ? ", " : ""}
                </span>
              ))
            ) : (
              <span />
            )}
          </div>
        </div>
      </div>
      {onRemove && (
        <div>
          <IconTrashSearch
            onClick={(e) => {
              e.stopPropagation();
              onRemove?.();
            }}
            onMouseDown={(e) => e.stopPropagation()}
            className="text-gray-400 hover:text-white text-xs"
          />
        </div>
      )}
    </button>
  );
};

const ArtistSearchItem = ({item, onRemove, onClick}: ArtistSearchItemProps) => {
  const {t} = useTranslation();
  return (
    <button
      onClick={() => onClick()}
      className="flex items-center justify-between hover:bg-[#FFFFFF12] text-[#FFFFFF] py-1.5 px-2.5 rounded-lg cursor-pointer text-sm flex-shrink-0"
    >
      <div className="flex items-center gap-4">
        <div className="relative avatar-wrapper flex-shrink-0 w-10 h-10">
          <img
            src={
              item?.images?.SMALL ||
              item?.images?.DEFAULT ||
              "/image/default-avatar.png"
            }
            className="w-full h-full object-cover rounded-full"
          />
        </div>
        <div className="flex flex-col text-left gap-y-1 justify-center overflow-hidden">
          <div className="flex items-center gap-x-2.5 text-white">
            <div className={"text-sm font-semibold line-clamp-1"}>
              {item.stageName || item?.name}
              <span className="ml-2 text-xs text-[rgba(255,255,255,0.5)] overflow-hidden text-ellipsis">
                {item.totalLikes} {t("common.likes")}
              </span>
            </div>
          </div>
        </div>
      </div>
      {onRemove && (
        <div>
          <IconTrashSearch
            onClick={(e) => {
              e.stopPropagation();
              onRemove?.();
            }}
            onMouseDown={(e) => e.stopPropagation()}
            className="text-gray-400 hover:text-white text-xs"
          />
        </div>
      )}
    </button>
  );
};

export default function SearchItem({item, onClick, onRemove}: SearchItemProps) {
  switch (item.type) {
    case ESearchType.KEYWORD:
      return (
        <KeywordSearchItem
          item={item.value}
          onClick={onClick}
          onRemove={onRemove}
        />
      );
    case ESearchType.ARTIST:
      return (
        <ArtistSearchItem
          item={item.value}
          onClick={onClick}
          onRemove={onRemove}
        />
      );
    default:
      return (
        <SongSearchItem
          item={item.value}
          onClick={onClick}
          onRemove={onRemove}
        />
      );
  }
}
