import ImageCropper from "@components/ImageCropper";
import {DialogActions} from "@mui/material";
import {ErrorMessage, Field, Form, Formik} from "formik";
import "./index.scss";
import ApiAutofill from "@api/ApiAutofill";
import AutoCompleteAutofill from "@components/AutoCompleteAutofill";
import {useTranslation} from "react-i18next";
import {LocalizationProvider} from "@mui/x-date-pickers/LocalizationProvider/LocalizationProvider";
import {AdapterDayjs} from "@mui/x-date-pickers/AdapterDayjs";
import {DatePicker} from "@mui/x-date-pickers/DatePicker";
import dayjs from "dayjs";
import {IPlaylist} from "src/types";
import ApiCMSPlaylist, {ICreatePlaylist} from "@api/ApiCMSPlaylist";
import {useMutation} from "@tanstack/react-query";
import {toast} from "react-toastify";
import GlobalButton from "@components/ButtonGlobal";
import * as Yup from "yup";
import CUModal from "@components/CUModal";
import MetaLanguageSelect from "@pages/Cms/components/MetaLanguageSelect";

interface IModalAddEditAlbum {
  open: boolean;
  onClose: () => void;
  initValue?: IPlaylist;
  refetch: () => void;
}

export default function ModalAddEditAlbum({
  open,
  onClose,
  initValue,
  refetch,
}: IModalAddEditAlbum) {
  const {t} = useTranslation();
  const isEdit = initValue !== undefined;
  const validationSchema = Yup.object({
    image: Yup.mixed()
      .test(
        "is-required",
        t("validation.field_is_require", {
          field: t("common.cover"),
        }),
        (file) => {
          if (initValue && !file) return true;
          return !!file;
        },
      )
      .test(
        "size",
        t("validation.file_size_limit_exceed", {size: "10MB"}),
        (file) => {
          if (initValue && !file) return true;
          return file && (file as File).size < 10 * 1024 * 1024;
        },
      ),
    name: Yup.string().required(
      t("validation.field_is_require", {
        field: t("cms.album.album_name"),
      }),
    ),
  });

  const initialValues = {
    image: undefined,
    name: initValue?.name,
    genreIds: initValue?.genres?.map((genre) => ({
      id: genre.id,
      label: genre.name,
    })),
    themeIds: initValue?.themes?.map((theme) => ({
      id: theme.id,
      label: theme.name,
    })),
    releaseDate: initValue?.releaseDate,
    songIds: initValue?.songs?.map((song) => ({
      id: song.id ?? "",
      label: song.name ?? "",
    })),
    description: initValue?.description,
    language: initValue?.language || "en",
  };

  const {mutateAsync: createAlbumMutationAsync, isPending: isCreating} =
    useMutation({
      mutationFn: ApiCMSPlaylist.createAlbum,
      onSuccess: () => {
        toast.success(t("common.add_successfully"));
        refetch();
        onClose();
      },
    });

  const {mutateAsync: updateAlbumMuatationAsync, isPending: isUpdating} =
    useMutation({
      mutationFn: ApiCMSPlaylist.updatePlaylist,
      onSuccess: () => {
        toast.success(t("common.update_successfully"));
        refetch();
        onClose();
      },
    });

  const handleSubmit = async (values: ICreatePlaylist) => {
    const formattedValues = {
      ...values,
      genreIds: values.genreIds?.map((genre) => genre.id),
      themeIds: values.themeIds?.map((theme) => theme.id),
      songIds: values.songIds?.map((song) => song.id),
    };

    if (isEdit) {
      await updateAlbumMuatationAsync({
        id: initValue.id,
        ...(formattedValues as ICreatePlaylist),
      });
    } else {
      await createAlbumMutationAsync(formattedValues as ICreatePlaylist);
    }
  };

  return (
    <CUModal
      onClose={onClose}
      open={open}
      title={isEdit ? t("cms.edit_album") : t("cms.new_album")}
    >
      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
        enableReinitialize
      >
        {({setFieldValue, values, handleChange, handleSubmit}) => (
          <>
            <Form
              className="flex flex-col gap-4 p-6 max-h-[600px] overflow-y-auto border-b border-gray-300"
              onKeyDown={(e) => {
                if (e.key === "Enter") {
                  e.preventDefault();
                }
              }}
            >
              <div className="flex flex-col gap-2.5">
                <div className="flex flex-col gap-2">
                  <label className="text-sm font-bold text-gray-700">
                    {t("common.cover")}
                    <span className="text-red-600"> *</span>
                  </label>
                  <div>
                    <ImageCropper
                      onChange={(file) => {
                        setFieldValue("image", file);
                      }}
                      initialImageUrl={initValue?.images?.DEFAULT}
                      className="w-[120px] aspect-square object-cover"
                    />
                    <ErrorMessage
                      name="image"
                      component="div"
                      className="text-red-500 text-sm mt-1"
                    />
                  </div>
                </div>
              </div>
              <div className="flex flex-col gap-2.5">
                <span className="text-[#000000D9] text-sm font-semibold">
                  {t("cms.album_name")}
                  <span className="text-red-600"> *</span>
                </span>
                <div className="relative">
                  <Field
                    name="name"
                    value={values.name}
                    placeholder={t("cms.album_name")}
                    className="custom-input-info w-full"
                    onChange={handleChange("name")}
                  />
                  <ErrorMessage
                    name="name"
                    component="div"
                    className="text-red-500 text-sm mt-1"
                  />
                </div>
              </div>
              <div className="flex flex-col gap-2.5 w-full">
                <span className="text-[#000000D9] text-sm font-semibold">
                  {t("cms.list_song")}
                </span>
                <div className="relative">
                  <AutoCompleteAutofill
                    placeHolder={t("cms.list_song")}
                    name="autofillSong"
                    suggestionAPI={ApiAutofill.autoSong}
                    multiple
                    value={values.songIds}
                    onChange={(val) => setFieldValue("songIds", val)}
                  />
                </div>
              </div>
              <div className="flex gap-4 justify-between">
                <div className="flex flex-col gap-2.5 w-full">
                  <span className="text-[#000000D9] text-sm font-semibold">
                    {t("common.theme")}
                  </span>
                  <div className="relative">
                    <AutoCompleteAutofill
                      placeHolder={t("common.theme")}
                      name="autofillTheme"
                      suggestionAPI={ApiAutofill.autoTheme}
                      multiple
                      value={values.themeIds}
                      onChange={(val) => setFieldValue("themeIds", val)}
                    />
                  </div>
                </div>
                <div className="flex flex-col gap-2.5 w-full">
                  <span className="text-[#000000D9] text-sm font-semibold">
                    {t("common.genre")}
                  </span>
                  <div className="relative">
                    <AutoCompleteAutofill
                      placeHolder={t("common.genre")}
                      name="autofillGenre"
                      suggestionAPI={ApiAutofill.autoGenre}
                      multiple
                      value={values.genreIds}
                      onChange={(val) => setFieldValue("genreIds", val)}
                    />
                  </div>
                </div>
              </div>
              <div className="flex gap-4 justify-between">
                <div className="flex flex-col gap-2.5 w-full">
                  <span className="text-[#000000D9] text-sm font-semibold">
                    {t("cms.language")}
                  </span>
                  <MetaLanguageSelect
                    onChange={(val) => setFieldValue("language", val)}
                    initValue={values.language}
                  />
                </div>
                <div className="flex flex-col gap-2.5 w-full">
                  <span className="text-[#000000D9] text-sm font-semibold">
                    {t("cms.song.release_time")}
                  </span>
                  <LocalizationProvider dateAdapter={AdapterDayjs}>
                    <DatePicker
                      name="releaseDate"
                      disableFuture
                      value={
                        values.releaseDate ? dayjs(values.releaseDate) : null
                      }
                      onChange={(value) => {
                        if (value) {
                          setFieldValue(
                            "releaseDate",
                            value.format("YYYY-MM-DD"),
                          );
                        } else {
                          setFieldValue("releaseDate", null);
                        }
                      }}
                      slotProps={{
                        textField: {
                          size: "small",
                          placeholder: t("common.release_date"),
                        },
                        field: {
                          clearable: true,
                        },
                      }}
                    />
                  </LocalizationProvider>
                </div>
              </div>
              <div className="flex flex-col gap-2.5">
                <label className="text-sm font-bold text-gray-700">
                  {t("common.description_album")}
                </label>
                <textarea
                  name="description"
                  value={values.description}
                  placeholder={t("common.description_album")}
                  className="border border-gray-300 rounded p-2 text-sm w-full"
                  onChange={handleChange("description")}
                />
              </div>
            </Form>
            <DialogActions>
              <GlobalButton
                onClick={onClose}
                text={t("common.cancel")}
                color="white"
                className="w-20"
                textClassName="text-[#000000D9]"
                disabled={isCreating || isUpdating}
              />
              <GlobalButton
                text={t("common.confirm")}
                className="w-30"
                onClick={handleSubmit}
                isLoading={isCreating || isUpdating}
              />
            </DialogActions>
          </>
        )}
      </Formik>
    </CUModal>
  );
}
