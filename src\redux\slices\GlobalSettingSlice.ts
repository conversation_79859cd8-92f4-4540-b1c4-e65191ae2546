import {createSlice, PayloadAction} from "@reduxjs/toolkit";
import {persistReducer} from "redux-persist";
import storage from "redux-persist/lib/storage";
import {AudioQualityTypeEnum} from "src/types";
import Config from "@config";

interface ISettingState {
  timeToNextSong: number;
  removeSilent: boolean;
  musicQuality: AudioQualityTypeEnum;
  language: string;
}

const initialState: ISettingState = {
  timeToNextSong: 3,
  removeSilent: false,
  musicQuality: AudioQualityTypeEnum.KBPS_320,
  language: Config.LANGUAGE.DEFAULT,
};

const GlobalSettingSlice = createSlice({
  name: "settings",
  initialState,
  reducers: {
    setTimeToNextSong(state, action: PayloadAction<number>) {
      state.timeToNextSong = action.payload;
    },
    setRemoveSilent(state, action: PayloadAction<boolean>) {
      state.removeSilent = action.payload;
    },
    setMusicQuality(state, action: PayloadAction<AudioQualityTypeEnum>) {
      state.musicQuality = action.payload;
    },
    resetSettings(state) {
      state.timeToNextSong = initialState.timeToNextSong;
      state.removeSilent = initialState.removeSilent;
      state.musicQuality = initialState.musicQuality;
    },
    setLanguage(state, action: PayloadAction<string>) {
      state.language = action.payload;
    },
  },
});

export const {
  setTimeToNextSong,
  setRemoveSilent,
  setMusicQuality,
  resetSettings,
  setLanguage,
} = GlobalSettingSlice.actions;

const persistConfig = {
  key: "settings",
  storage,
};

export const persistedSettingReducer = persistReducer(
  persistConfig,
  GlobalSettingSlice.reducer,
);

export default GlobalSettingSlice.reducer;
