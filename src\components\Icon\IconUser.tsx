import {SVGProps} from "react";

const IconUser = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={24}
    height={24}
    fill="none"
    {...props}
  >
    <path
      stroke="#fff"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeMiterlimit={10}
      strokeOpacity={0.8}
      strokeWidth={1.2}
      d="M17.133 18.479a4.936 4.936 0 0 0-1.544-1.472c-1.007-.62-2.249-.947-3.589-.947-1.314 0-2.589.342-3.59.962a5.002 5.002 0 0 0-1.534 1.457M20.5 12a8.5 8.5 0 1 1-17 0 8.5 8.5 0 0 1 17 0ZM10.332 8.803c.457-.49 1.089-.744 1.766-.744s1.309.259 1.763.747c.46.494.671 1.155.622 1.84a2.795 2.795 0 0 1-.728 1.702c-.44.471-1.026.754-1.657.754-.63 0-1.217-.283-1.657-.754a2.788 2.788 0 0 1-.729-1.703c-.048-.688.161-1.35.62-1.842Z"
    />
  </svg>
);
export default IconUser;
