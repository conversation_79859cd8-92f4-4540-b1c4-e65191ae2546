import {GridColDef, GridRowId} from "@mui/x-data-grid";
import CmsTable from "@pages/Cms/components/CmsTable";
import {useEffect, useState} from "react";
import {
  convertDate,
  convertNumber,
  convertSongDuration,
} from "src/utils/timeUtils";
import {useTranslation} from "react-i18next";
import {
  EThemeAndGenreType,
  IArtist,
  IPlaylist,
  ISong,
  IThemeAndGenre,
} from "src/types";
import {keepPreviousData, useQuery} from "@tanstack/react-query";
import QUERY_KEY from "@api/QueryKey";
import SearchInput from "@pages/Cms/components/SearchInput";
import {LocalizationProvider} from "@mui/x-date-pickers/LocalizationProvider/LocalizationProvider";
import {AdapterDayjs} from "@mui/x-date-pickers/AdapterDayjs";
import {DatePicker} from "@mui/x-date-pickers/DatePicker";
import dayjs from "dayjs";
import {Avatar, AvatarGroup, IconButton, Tooltip} from "@mui/material";
import ApiPlaylist from "@api/ApiCMSPlaylist";
import IconCmsEdit from "@components/Icon/IconCmsEdit";
import IconCmsDelete from "@components/Icon/IconCmsDelete";
import SelectAutofill from "@components/SelectAutofill";
import ApiAutofill from "@api/ApiAutofill";
import GlobalButton from "@components/ButtonGlobal";
import IconAdd from "@components/Icon/IconAdd";
import ModalAddEditSong from "@pages/Cms/song/components/ModalAddEditSong";
import ModalDetailSong from "@pages/Cms/song/components/ModalDetailSong";

interface ListSongPlaylistsProps {
  albumData?: IPlaylist | undefined;
}

export default function ListSongPlaylist({albumData}: ListSongPlaylistsProps) {
  const {t} = useTranslation();
  const [page, setPage] = useState(0);
  const [pageSize, setPageSize] = useState(10);

  const [searchText, setSearchText] = useState("");
  const [debouncedSearchText, setDebouncedSearchText] = useState("");
  const [themeId, setThemeId] = useState("");
  const [genreId, setGenreId] = useState("");
  const [updateTime, setUpdateTime] = useState("");
  const [openModalSong, setOpenModalSong] = useState(false);
  const [openModalDetail, setOpenModalDetail] = useState(false);
  const [songId, setSongId] = useState<GridRowId>("");

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedSearchText(searchText);
    }, 300);

    return () => {
      clearTimeout(handler);
    };
  }, [searchText]);

  const getListSongAlbums = useQuery({
    queryKey: [
      QUERY_KEY.PLAYLIST.GET_LIST_PLAYLIST_SONGS_CMS,
      page,
      pageSize,
      themeId,
      genreId,
      debouncedSearchText,
      updateTime,
      albumData?.id,
    ],
    placeholderData: keepPreviousData,
    queryFn: () =>
      ApiPlaylist.getPlaylistSongs(albumData?.id ?? "", {
        page,
        pageSize,
        keyword: debouncedSearchText,
        themeId: themeId,
        genreId: genreId,
        updatedAt: updateTime,
      }),
  });

  const tableRows = getListSongAlbums?.data?.data?.map((item: ISong) => ({
    id: item?.id,
    image: item?.images?.SMALL || item?.images?.DEFAULT,
    name: item?.name ?? "-",
    artists: item?.artists || "",
    themes: item?.genres?.filter(
      (item) => item?.type === EThemeAndGenreType.THEME,
    ),
    genres: item?.genres?.filter(
      (item) => item?.type === EThemeAndGenreType.GENRE,
    ),
    releaseDate: item?.releaseDate,
    updatedAt: item?.updatedAt,
    favoriteCount: item?.totalLikes ?? "0",
    shareCount: item?.totalShares ?? "0",
    playlistCount: item?.totalAddedToPlaylists ?? "0",
    duration: item?.duration ?? "0",
  }));

  const columns: GridColDef[] = [
    {
      field: "image",
      headerName: t("common.cover_img"),
      width: 134,
      sortable: false,
      headerAlign: "center",
      align: "center",
      renderCell: (params) => (
        <div className="w-full h-full flex justify-center items-center">
          <img
            src={params.value || "/image/default-music.png"}
            className="h-12 w-12 rounded-[4px] object-cover"
          />
        </div>
      ),
    },
    {
      field: "name",
      headerName: t("cms.song.song_name"),
      width: 190,
      sortable: false,
      renderCell: (params) => (
        <Tooltip placement="left" arrow title={params.value}>
          <div
            style={{whiteSpace: "normal", wordWrap: "break-word"}}
            className="line-clamp-3"
          >
            {params.value}
          </div>
        </Tooltip>
      ),
    },
    {
      field: "artists",
      headerName: t("common.performing_artist_composer"),
      sortable: false,
      width: 250,
      renderCell: (params) => (
        <div className="w-full h-full flex justify-start items-center space-x-1">
          <AvatarGroup
            max={3}
            sx={{
              "& .MuiAvatar-root": {
                width: 24,
                height: 24,
                fontSize: 12,
              },
            }}
          >
            {params?.row?.artists.map((artist: IArtist, index: number) => (
              <Avatar
                key={`avatar_${index}`}
                src={
                  artist?.images?.SMALL ||
                  artist?.images?.DEFAULT ||
                  "/image/default-avatar.png"
                }
              />
            ))}
          </AvatarGroup>
          <span className="font-semibold text-sm text-[#242728]">
            {params?.row?.artists
              ?.slice(0, 2)
              .map((artist: IArtist) => artist?.stageName ?? artist?.name)
              .join(", ")}
            {params?.row?.artists?.length > 2 &&
              ` & ${t("common.lots_artist")}`}
          </span>
        </div>
      ),
    },
    {
      field: "themes",
      headerName: t("common.theme"),
      width: 105,
      sortable: false,
      headerAlign: "center",
      renderCell: (params) => {
        return (
          <Tooltip
            placement="left"
            arrow
            title={params?.value?.map((item: IThemeAndGenre) => (
              <span key={item?.id}>
                {item?.name}
                {params?.value?.length > 1 &&
                params?.value?.indexOf(item) < params?.value?.length - 1
                  ? ", "
                  : ""}
              </span>
            ))}
          >
            <div className="line-clamp-3">
              {params?.value?.map((item: IThemeAndGenre) => (
                <span key={item?.id}>
                  {item?.name}
                  {params?.value?.length > 1 &&
                  params?.value?.indexOf(item) < params?.value?.length - 1
                    ? ", "
                    : ""}
                </span>
              ))}
            </div>
          </Tooltip>
        );
      },
    },
    {
      field: "genres",
      headerName: t("common.genre"),
      sortable: false,
      width: 164,
      renderCell: (params) => {
        return (
          <div className="flex flex-wrap gap-2 max-h-[94px] overflow-hidden">
            {params?.value?.map((item: IThemeAndGenre) => (
              <span
                className="rounded-lg  py-0.5 px-2 bg-[#F2F2F3] border border-[#DCDCDC]"
                key={item?.id}
              >
                {item?.name}
              </span>
            ))}
          </div>
        );
      },
    },
    {
      field: "releaseDate",
      headerName: t("cms.song.release_time"),
      width: 186,
      sortable: false,
      headerAlign: "center",
      align: "left",
      renderCell: (params) => {
        return convertDate(params?.value);
      },
    },
    {
      field: "updatedAt",
      headerName: t("common.updated_time"),
      width: 186,
      sortable: false,
      headerAlign: "center",
      align: "left",
      renderCell: (params) => {
        return convertDate(params?.value);
      },
    },
    {
      field: "favoriteCount",
      headerName: t("common.likes"),
      type: "number",
      width: 160,
      headerAlign: "center",
      align: "left",
      sortable: false,
      hideSortIcons: false,
      renderCell: (params) => (
        <span className="text-[#FF4319]">{convertNumber(params?.value)}</span>
      ),
    },
    {
      field: "shareCount",
      headerName: t("common.shares"),
      sortable: false,
      type: "number",
      width: 118,
      headerAlign: "center",
      align: "left",
      renderCell: (params) => {
        return convertNumber(params?.value);
      },
    },
    {
      field: "playlistCount",
      headerName: t("cms.song.add_to_playlist_collection"),
      sortable: false,
      type: "number",
      width: 185,
      headerAlign: "center",
      align: "left",
      renderCell: (params) => {
        return convertNumber(params?.value);
      },
    },

    {
      field: "duration",
      headerName: t("cms.song.duration"),
      sortable: false,
      width: 133,
      renderCell: (params) => {
        return convertSongDuration(params?.value);
      },
    },
    {
      field: "actions",
      headerName: t("common.actions"),
      minWidth: 30,
      width: 120,
      sortable: false,
      disableColumnMenu: true,
      align: "center",
      headerAlign: "center",
      headerClassName: "sticky-header",
      cellClassName: "sticky-cell",
      renderCell: () => (
        <div className="flex justify-center">
          <IconButton
          // onClick={() => {
          //   handleOpenModalEdit(params.row.id);
          // }}
          >
            <IconCmsEdit />
          </IconButton>
          <IconButton
          // onClick={() => {
          //   handleDeleteArtist(params.row.id);
          // }}
          >
            <IconCmsDelete />
          </IconButton>
        </div>
      ),
    },
  ];

  return (
    <div className="space-y-4">
      <div className="flex justify-between sm:items-center sm:flex-row flex-col sm:gap-0 gap-3 items-start">
        <div className="flex gap-3 flex-wrap w-full">
          <SearchInput
            searchText={searchText}
            className="py-[5px]"
            onChange={(v) => setSearchText(v)}
            placeholder={t("cms.song.song_artist_name")}
          />

          <SelectAutofill
            name="selectTheme"
            suggestionAPI={ApiAutofill.autoTheme}
            selectProps={{
              className: "cms-select-gray",
              size: "small",
            }}
            onChange={(v) => setThemeId(v)}
            placeholder={t("common.theme")}
          />

          <SelectAutofill
            name="selectGenre"
            suggestionAPI={ApiAutofill.autoGenre}
            selectProps={{
              className: "cms-select-gray",
              size: "small",
            }}
            onChange={(v) => setGenreId(v)}
            placeholder={t("common.genre")}
          />
          <LocalizationProvider dateAdapter={AdapterDayjs}>
            <DatePicker
              disableFuture
              onChange={(v: any) => {
                if (v) {
                  setUpdateTime(dayjs(v).format("YYYY-MM-DD"));
                }
              }}
              slotProps={{
                textField: {
                  size: "small",
                  placeholder: t("cms.song.placeholder_updateTime"),
                },
                field: {
                  clearable: true,
                  onClear: () => setUpdateTime(""),
                },
              }}
              className="cms-datepicker-gray"
            />
          </LocalizationProvider>
        </div>
        <GlobalButton
          text={t("cms.song.btn_add")}
          startIcon={<IconAdd />}
          className="w-auto whitespace-nowrap"
          onClick={() => setOpenModalSong(true)}
        />
      </div>

      <CmsTable
        rows={tableRows}
        ordinalColumn
        columns={columns}
        loading={getListSongAlbums?.isLoading}
        totalItems={getListSongAlbums?.data?.meta?.totalItems || 0}
        currentPage={page}
        onPageChange={(newPage) => setPage(newPage)}
        rowsPerPage={pageSize}
        onRowsPerPageChange={(newPageSize) => setPageSize(newPageSize)}
        onRowDoubleClick={(params) => {
          setOpenModalDetail(true);
          setSongId(params?.id);
        }}
        hideFooter
        className="max-h-[44vh] overflow-y-auto"
      />

      <ModalAddEditSong
        open={openModalSong}
        onClose={() => setOpenModalSong(false)}
        refetch={getListSongAlbums.refetch}
      />

      {openModalDetail && (
        <ModalDetailSong
          open={openModalDetail}
          onClose={() => {
            setOpenModalDetail(false);
            setSongId("" as GridRowId);
          }}
          songId={songId}
        />
      )}
    </div>
  );
}
