{"common": {"register": "Register", "link_to_app": "Get the best LaoMusic experience on the app", "view_detail_artist": "View artist details", "refresh": "Refresh", "renew": "<PERSON>w", "you_offline": "You’re offline", "you_online": "You’re online", "please_connect_internet": "Please connect to the internet and try again.", "view_detail_album": "View album details", "height": "height", "width": "width", "size": "size", "date_format": "MMM, Do", "add_to_successfully": "Added to playlist successfully", "song_exist_playlist": "The song already exists in the playlist", "song_exist_queue": "The song is already in the playlist", "date_format_full": "MMMM, Do, YYYY", "date_time_format": "HH:mm - MMMM Do, YYYY", "out_of": "out of", "record": "record", "no_data": "No data", "record_per_page": "Records per page", "lots_artist": "Many artists", "you_can_upload_file_type": "You can upload {{file}} files", "audio_file": "Audio file", "upload_song_file": "Upload file", "avatar": "Avatar", "name_playlist": "Playlist name", "search": "Search", "ok": "OK", "new_playlist": "New playlist", "listen_music_free": "Listen to music for free", "login_and_discover": "Log in to discover playlists made just for you", "list_of_suggest_song": "List of suggested songs", "account": "Account", "account_email": "Account, email...", "account_status": "Account status", "admin": "Admin", "album": "Album", "album_list_not_found": "Album list not found", "album_name": "Album name", "album_name_artist": "Album name, artist...", "all": "All", "artist": "Artist", "artist_list_not_found": "Artist list not found", "avatar_updated_successfully": "Avatar updated successfully!", "cancel": "Cancel", "change_password": "Change password", "close": "Close", "retype_password": "Retype new password", "cover": "Cover image", "account_created_at": "Account creation date", "description_album": "Album description", "device_login": "Login device", "download_count": "Download count", "duration": "Duration", "favorite_count": "Favorite count", "female": "Female", "favorite_list": "Favorite list", "hashtag_count": "Hashtag count", "error_while_upload_image": "An error occurred while uploading the image", "error_while_update": "An error occurred while updating the information!", "email": "Email", "see_more": "See more", "see_less": "See less", "id": "ID", "last_logged_in": "Last logged in", "listen_count": "Listen count", "lyric_song": "Song lyrics", "male": "Male", "music_detail": "Music details", "new_password": "New password", "no_image": "No image", "old_password": "Old password", "password_change_fail": "Password change failed. Please try again", "password_changed_successful": "Password changed successfully", "performing_artist_composer": "Performing artist - composer", "playlist": "Playlist", "playlist_list_not_found": "Playlist list not found", "mv_list_not_found": "No search results found", "please_select_image_file": "Please select an image file in .jpg, .jpeg, or .png format", "processing": "Processing...", "quantity_song": "Number of songs", "release_date": "Release date", "role": "Role", "detail_play_music": "Play music details", "save": "Save", "singer": "<PERSON>", "song_list_not_found": "Song list not found", "share_count": "Share count", "copy_link_success": "Copied to clipboard", "copy_link_failed": "Failed to copy link", "staff": "Staff", "status": "Status", "unknown": "Unknown", "updated_successfully": "Information updated successfully!", "updated_time": "Updated time", "user": "User", "username": "Username", "home": "Home", "copy_link": "Copy link", "loading": "Loading", "like": "Like", "like_successfully": "You have added the song to your library.", "dislike_successfully": "You have removed the song from your library.", "like_error": "An error occurred while liking the song.", "dislike_error": "An error occurred while disliking the song.", "like_playlist_successfully": "You have added the playlist to your library.", "dislike_playlist_successfully": "You have removed the playlist from your library.", "lyric": "Lyrics", "contribute_lyric": "Contribute lyrics", "add_to": "Add to", "block": "Block", "share": "Share", "list_empty": "Empty list!", "playlist_common": "Playlist", "download": "Download", "not_info_artist": "Unknown artist", "language": {"language": "Language", "vietnamese": "Vietnamese", "english": "English", "lao": "Lao"}, "personal_information": "Account information", "favorite": "Favorite", "add": "Add new", "add_successfully": "Added successfully!", "update": "Update", "update_successfully": "Updated successfully!", "update_failed": "Update failed", "delete_confirm": "Confirm deletion", "delete": "Delete", "delete_successfully": "Deleted successfully!", "basic": "Basic", "pro": "Pro", "premium": "Premium", "package_basic": "Tinamusic Basic Package", "package_pro": "Tinamusic Pro Package", "package_premium": "Tinamusic Premium Package", "upgrade": "Upgrade", "upgrade_page": {"day": "day", "week": "week", "month": "month", "year": "year", "price": "Only from {{price}} LAK/month", "button_upgrade": "UPGRADE NOW", "time_use_one": "<bold>Time of use:</bold> {{period}} day", "time_use_other": "<bold>Time of use:</bold> {{period}} days", "privileges": "Special privileges:", "no_data": "Listen to music without consuming DATA", "hot_song": "Thousands of HOT songs today", "no_ads": "Use without ads", "title_upgrade_page": "Unlimited World of Music", "subtitle_upgrade_page": "Upgrade your account to experience premium features and content", "error_upgrade_message": "An error occurred during the upgrade process. Please try again."}, "upgrade_modal": {"title": "Listen to music without ads?", "description": "Come to us, upgrade to Music Plus package from only <bold>20,000</bold> VND/month. We will give you many other attractive vouchers.", "note": "Don't hesitate, explore now!", "button": "Explore now!"}, "settings": "Settings", "profile": "Profile", "full_name": "Full name", "phone_num": "Phone number", "identification_number": "Identification number", "gender": "Gender", "CCCD_num": "ID number", "genre": "Genre", "operating_status": "Operating status", "country": "Country", "song_number": "Number of songs", "add_new": "Add new", "operation": "Action", "cover_img": "Cover image", "views": "Plays", "likes": "<PERSON>s", "downloads": "Downloads", "shares": "Shares", "add_to_playlist": "Add to playlist count", "add_to_playlist_successfully": "Added to playlist successfully", "remove_from_playlist_successfully": "Removed from playlist successfully", "action_error": "Action failed", "describe": "Description", "locked": "Locked", "create_new_playlist": "Create new playlist", "pending": "Paused", "mandatory_artist_name": "Please enter the artist's name", "mandatory_country": "Please select a country", "mandatory_status": "Please select operating status", "mandatory_genre": "Please select at least one genre", "confirm_img": "Confirm image", "biographical_description": "Biographical description", "mandatory_file": "Please upload the usage license", "mandatory_file_pdf": "Only PDF files are accepted", "mandatory_isrc": "Please enter ISRC", "edit_information": "Edit information", "upload_file": "Upload file", "mandatory_title": "Title is required", "mandatory_message": "Message content is required", "send_notification": "Send notification", "send_notice_to": "Send notification to:", "title": "Title", "message": "Message content", "send": "Send", "confirm_send": "Do you want to send this notification to", "actions": "Actions", "title_logo": "Tinasoft Vietnam", "verified": "Verified", "no_verified": "Not verified", "operation_failed": "Action failed", "favorites": "Favorites", "songs": "Songs", "list_is_empty": "List is empty", "music_details": "Music details", "artist_avatar": "Artist avatar", "add_to_playlists": "Add to playlists", "play_next": "Play next", "add_to_queue": "Add to queue", "lyrics": "Lyrics", "playlist_count": "Playlist additions", "home_sidebar": {"rankings": "Rankings", "library": "Library", "topic": "Topics and genres", "recent": "Recently played", "favorite": "Favorite list"}, "download_app": "Download app", "title_modal_install": "Scan QR code to Install App", "subtitle_modal_install": "Scan this QR code in your browser to verify the app installation.", "popular": "Popular", "introduce": "Introduce", "new_popular_release": "New Popular Releases", "favorite_albums": "Favorite Albums", "singers_combination": "Artists Collaboration", "favorited": "Favorited", "hot": "Hot", "artist_image": "Artist image", "active": "Active", "deactivate": "Deactivate", "confirm": "Confirm", "edit": "Edit", "song": "Song", "theme": "Theme", "mv": "MV", "setting": {"listening_mode": "Listening mode", "logged_in_devices": "Logged in devices", "terms": "Terms, privacy, and security", "privacy_and_security": "Privacy and security", "terms_description": "Welcome to the LaoMusic music website. By accessing or using our service, you agree to abide by these Terms of Service. If you do not agree with any part of these terms, please do not use the App.", "select_language": "Select language", "accept_term": "I agree to the terms.", "advanced_setting": "Advanced settings", "time_to_next_song": "Time to next song", "remove_slient": "Remove silence between songs", "music_quality": "Music quality", "high": "High", "low": "Low", "add_to_playlist": "Add to playlist", "sec": "{{second}}s"}, "library": {"my_playlists": "My playlists", "my_playlist": "My playlists"}, "cancels": "Cancel", "playlists": "Playlists", "albums_list": "Albums list", "password": "Password", "login_success": "Login successful", "login_cms": "Login CMS", "enter_email": "Enter your email", "forgot_password": "Forgot password?", "display_name": "Display name", "enter_name": "Enter your name", "birthday": "Birthday", "province": "Province/City", "select_gender": "Select gender", "other": "Other", "sure_to_save_the_edited_information": "Are you sure you want to save the edited information?", "share_personal_data_with_laomusic": "Share personal data with content providers of LaoMusic for a better experience.", "featured": "Featured", "experience_download_for_free": "Experience Super Music & Download for Free", "discover_amazing_melodies_and": "Discover amazing melodies, keeping up with the ever-expanding music trends from talented and famous artists around the world. Share your unique way of listening to music.", "enter_the_OTP": "Please enter the OTP!", "enter_your_registered_email": "Enter your registered email to receive the OTP", "reset_password": "Reset password", "password_changed_successfully": "Password changed successfully!", "confirm_password": "Confirm password", "otp": "OTP verification", "otp_resent": "OTP resent!", "OTP_verification_successful": "OTP verification successful, you can now change your password!", "OTP_verification": "OTP verification", "enter_OTP_sent_to_email": "Enter the 4-digit OTP sent to your email", "not_receive_otp": "Didn't receive OTP?", "resend": "Resend", "new_user": "New user?", "enter_the_OTP_verification": "Please enter OTP to verify", "agree_to_the_terms_and": "I agree to the LaoMusic terms and policies.", "terms_privacy_policy": "Terms & Privacy Policy.", "already_have_account": "Already have an account?", "account_verification_successful": "Account verification successful!", "registration_verification": "Registration verification", "update_error": "Information update error: {{error}}", "completed": "Completed", "delete_all": "Delete all", "on": "On", "off": "Off", "high": "High", "low": "Low", "general_setting": "General settings", "default": "<PERSON><PERSON><PERSON>", "second": "second", "quality": "Quality", "menu": {"add_to_playlist": "Add to playlist", "shuffle": "Enable shuffle", "un_shuffle": "Disable shuffle", "previous": "Play previous", "play": "Play", "playback": "Enable repeat all", "play_a_song": "Enable repeat one", "un_playback": "Disable repeat", "open_queue": "Open queue", "close_queue": "Close queue", "add_to_playlist_favorite": "Add to playlist favorite", "remove_from_playlist_favorite": "Remove from playlist favorite", "remove_from_playlist": "Remove from playlist", "watch_detail_artist": "Watch artist details", "watch_detail_album": "Watch album details", "share_link_song": "Share song link", "watch_detail_playlist": "Watch playlist details", "share_link_playlist": "Share playlist link", "playlist": "Playlist"}, "featured_songs": "Top 20 Featured Songs", "album_top_100": "Top 100 Albums", "featured_artist": "Top 10 Featured Artists", "clear_all": "Clear selected", "library_recent": "Recently played", "song_not_found": "Song not found", "lyrics_not_found": "Lyrics not found", "theme_name": "Theme name", "genre_name": "Genre name", "trending_ytb": "Youtube Trending", "youtube_playlist": "Youtube Playlist", "video_mv": "Video MV", "country_name": {"vietnam": "Vietnam", "united_states": "United States", "canada": "Canada", "united_kingdom": "United Kingdom", "france": "France", "germany": "Germany", "japan": "Japan", "south_korea": "South Korea", "china": "China", "india": "India", "australia": "Australia", "brazil": "Brazil", "russia": "Russia", "italy": "Italy", "spain": "Spain", "mexico": "Mexico", "indonesia": "Indonesia", "thailand": "Thailand", "malaysia": "Malaysia", "philippines": "Philippines", "south_africa": "South Africa", "laos": "Laos"}, "detail_song": {"from_playlist": "From Playlist \"{{name}}\"", "from_album": "From Album \"{{name}}\"", "from_top_100": "From Top 100 \"{{name}}\""}, "top_song": {"all": "All", "top_100_country": "Top 100 {{country}}"}}, "404": {"not_found_page": "Sorry, this page does not exist", "back_to_home": "Back to homepage"}, "auth": {"logout": "Logout", "logout_confirm": "Are you sure you want to log out?", "logout_confirm_cms": "Do you want to log out?", "login": "<PERSON><PERSON>", "login_with_laoid": "Login with LaoId", "register": "Register", "profile": "Profile", "information": "Personal information"}, "validation": {"file_upload_must_be_type": "The uploaded file must be of type {{type}}", "not_audio_file": "The file is not an audio format", "field_is_require": "{{field}} is required", "file_size_limit_exceed": "File size must not exceed {{size}}", "password_min_length": "New password must be at least 8 characters long", "password_max_length": "Password must not exceed 20 characters", "password_lowercase": "Password must contain at least one lowercase letter", "password_uppercase": "Password must contain at least one uppercase letter", "password_digit": "Password must contain at least one digit", "password_special_character": "Password must contain at least one special character", "password_mismatch": "The new password and confirmation password do not match", "invalid_email": "Invalid email", "phone_number_only": "Phone number must contain only numbers", "password_requirements": "Password must contain at least 1 uppercase letter, 1 lowercase letter, and 1 special character", "otp_length": "OTP must be exactly 4 characters", "terms_agreement": "You must agree to the terms and policies"}, "home": {"listen_today": "What to listen today", "error_fetching": "Error fetching data.", "no_data": "No songs available.", "top_100_song": "TOP 100 Music", "suitable_album": "Suitable Albums", "playlist_must_try": "Playlist to try", "relaxing_music": "Relaxing Music"}, "search": {"top_keyword": "Trending Search", "history_search": "Search History", "no_history_search": "No search history.", "question_search": "What do you want to listen to?", "search_result": "Search Results", "search_no_data": "No search data", "related_keyword": "Related kewords", "related_songs": "Related songs", "related_artists": "Related artists"}, "ranking": {"new_music_chart": "Weekly New Music Chart"}, "topic": {"featured_songs": "Top {{length}} Featured Songs", "featured_artist": "Top 10 Featured Artists", "albums_list_theme": "Thematic Album List"}, "services": {"ringback_tone": {"register_title": "Register Ring<PERSON>", "confirm": "Are you sure you want to subscribe to our Ringback tone service? Click register to use the service now!"}}, "cms": {"language": "Language", "ordinal": "No.", "artist_song": "Performing artist", "add_image": "Add image", "song_name": "Song name", "new_song": "New song", "edit_song": "Edit song", "user_management": "User management", "song_management": "Song management", "album_management": "Album management", "collection_management": "Collection management", "top100_management": "Top 100 management", "playlist_management": "Playlist management", "theme_and_genre_management": "Theme & Genre management", "staff_management": "Staff management", "user_account": "User account", "artist_management": "Artist management", "premium_account_management": "Premium account management", "statistical": "Statistics", "package_management": "Package management", "account_management": "Account management", "general_management": "General management", "other_management": "Other management", "user_statistics": "User statistics", "song_statistics": "Song statistics", "singer_statistics": "Singer statistics", "new_album": "New album", "edit_album": "Edit album", "album_name": "Album name", "new_playlist": "New playlist", "edit_playlist": "Edit playlist", "playlist_name": "Playlist name", "list_song": "Song list", "list_theme": "Theme list", "list_genre": "Genre list", "staff_infomation": "Staff information", "theme_and_genre": {"cover": "Cover image", "name": "Name", "total_listen_count": "Total listen count", "last_updated": "Last updated", "created_date": "Created date", "action": "Action", "theme": "Theme", "genre": "Genre", "theme_name": "Theme name ({{language}})", "genre_name": "Genre name ({{language}})", "des_theme": "Theme description", "des_genre": "Genre description", "placeholder_genres": "What makes the song's music unique", "total_songs": "Total songs", "total_albums": "Total albums", "topic_parent": "Belongs to genre", "genre_detail": "Genre details", "theme_detail": "Theme details"}, "premium": {"year_package": "Yearly package", "cover_image": "Cover image", "device": "Logged-in device", "execution_time": "Execution time", "experience_package": "Experience package", "extension_period": "Extension period", "general_information": "General information", "history_status": "Status change history", "last_logged_in": "Last logged in", "list_logged_in_devices": "List of logged-in devices", "package_type": "Package type", "premium_account_information": "Premium account information", "promotion_package": "Promotional package"}, "artist": {"select_artist_type": "Select artist type", "artist_name": "Artist name", "real_name": "Real name", "management_company": "Management company", "artist_type": "Artist type", "artist_real_name": "Artist name, stage name, ...", "add_artist_success": "Artist added successfully", "add_artist_fail": "Failed to add artist", "delete_artist_success": "Artist deleted successfully", "delete_artist_fail": "Failed to delete artist", "confirm_artist_deletion": "Confirm artist deletion", "question_confirm_delete": "Are you sure you want to delete this artist?", "artist_details": "Artist details", "basic_information": "Basic information", "song_list": "Song list", "copyright_information": "Copyright information", "hide_singer": "Hide artist", "display_singer": "Display artist", "send_singer_notification": "Send artist notification", "delete_singer": "Delete artist", "question_hide_singer": "Do you want to hide this artist?", "confirm_show_artist": "Do you want to display this artist?", "question_delete_singer": "Do you want to delete this artist?", "license_to_use": "License to use", "isrc": "ISRC", "edit_basic_information": "Edit basic information", "musician": "<PERSON>ian", "singer": "<PERSON>", "singer_musician": "Singer - <PERSON>ian", "music_genre": "Music genre", "add_new_artist": "Add new artist", "quantity_album": "Number of albums", "album_list": "Album list", "select_country": "Select country", "bio_artist": "Artist biography", "change_switch_success": "Status changed successfully", "change_switch_fail": "Failed to change status", "hide_or_display": "Hide / Display", "stage_name": "Stage name"}, "song": {"lrc_lyrics": "Synchronized lyrics", "song_detail": "Song details", "lyrics": "Lyrics", "song_file": "Music file", "song_file_high_quality": "High-quality music file", "placeholder_search_song": "Enter song name", "placeholder_singer": "<PERSON>", "placeholder_theme": "Theme", "placeholder_genres": "Genres", "placeholder_album": "Album", "placeholder_updateTime": "Update time", "btn_add": "Add new", "songs_album": "Belongs to album", "share_count": "Share count", "favourite_count": "Favorite count", "genres": "Genres", "theme": "Theme", "album": "Belongs to album", "artist_compose": "Performer", "song_name": "Song name", "release_time": "Release time", "add_to_playlist_collection": "Add to playlist", "duration": "Duration", "hashtags": "Hashtags list", "update_time": "Update time", "image_song": "Cover image", "basic_info": "Basic information", "technical_info": "Technical information", "author_info": "Author information", "copyright_info": "Copyright information", "other_info": "Other information", "song_singer": "Performer", "song_musician": "<PERSON>ian", "song_artist": "Artist", "age_limit": "Age limit", "song_lyrics": "Song lyrics", "song_license": "License to use", "and": "and", "many_artist": "other artists", "song_artist_name": "Song name, artist", "delete_song": "Delete song", "delete_song_success": "Song deleted successfully", "confirm_delete_song": "Confirm song deletion", "question_confirm_delete": "Are you sure you want to delete this song?"}, "playlist": {"cover": "Cover image", "playlist_name": "Playlist name", "playlist_duration": "Duration", "playlist_artist": "Artist", "number_of_song": "Number of songs", "topic": "Topic", "genres": "Genres", "theme": "Theme", "created_time": "Release time", "updated_time": "Update time", "description": "Description", "input_playlist_name": "Enter playlist name", "like_count": "Like count", "download_count": "Download count", "share_count": "Share count", "listen_count": "Listen count", "playlist_add_count": "Add to playlist count", "list_tag": "Tag list", "des_playlist": "Playlist description", "user_create_playlist": "Playlist creator", "playlist_detail": "Playlist details", "toast_success_add_playlist": "Successfully created a new playlist"}, "album": {"album_detail": "Album details", "album_name": "Album name", "belongs_artist": "Belongs to artist", "featured_artist": "Featured artist", "album_artist_name": "Album name, artist ...", "top_album_name": "Top 100 name", "top_album_detail": "Top 100 details", "add_new_top_100": "Add new top 100", "update_top_100": "Edit top 100"}, "dashboard": {"day": "Day", "week": "Week", "month": "Month", "year": "Year", "song": "Song", "type": "Type", "top": "Top", "by": "By", "start": "Start", "end": "End", "music_listening_time": "Average music listening time", "playlist": "Playlist", "listen": "Listens", "visit": "Visit", "visit_over_time": "Visit over time", "listen_over_time": "Listen over time", "minutes": "Minutes", "time": "Time", "mbs": "MB/s", "click_follow": "Click and follow rate", "averange_uses_time": "Average usage time", "followers": "Followers", "total_listen": "Total listens", "unfollowers": "Unfollowers", "speed_download": "Download speed", "amount_songs": "Number of songs", "amount_new_songs": "Number of new songs", "devices_ratio": "Device usage ratio", "max_uses_time": "Peak usage time", "online_accounts": "Online accounts", "mon": "Mon", "tue": "<PERSON><PERSON>", "wed": "Wed", "thu": "<PERSON>hu", "fri": "<PERSON><PERSON>", "sat": "Sat", "sun": "Sun", "title": {"mobile": "Mobile", "tablet": "Tablet", "laptop": "Laptop", "new_songs": "New songs", "total_followers": "Total followers", "total_listens": "Total listens", "total_click_watch": "Total click and watch", "averange_listens": "Average listens time", "averange_speed_download": "Average download speed", "total_accounts": "Total accounts", "total_accounts_online": "Total online accounts", "new_register_accounts": "New registered accounts", "averange_uses_time": "Average usage time", "total_albums": "Total albums", "total_top100": "Total top 100", "total_playlists": "Total playlists", "total_songs": "Total songs"}}}, "playlist": {"error_fetching": "Error fetching data. Please try again.", "songs": "Songs", "no_song": "No songs available.", "play_all": "Play all", "participating_artists": "Participating artists", "no_data": "No data.", "you_may_like": "You may like", "add_to_playlist": "Add to playlist", "favorite_artist": "Favorite artist", "favorite_song": "Favorite song", "not_find_favorite_album": "Favorite album list is empty!", "not_find_favorite_song": "Favorite song list is empty!", "not_find_favorite_artist": "Favorite artist list is empty!", "not_find_playlist": "Playlist is empty!", "next_play": "Play next", "play_similar_content": "Play similar content", "set_up_ringtone": "Set as ringtone", "many_singers": "Many singers", "add_new_playlist": "Add new playlist", "name_playlist": "Name your playlist", "add_new": "Create new", "song": "song", "confirm_delete_playlist": "The playlist you created will be removed from your library. Are you sure you want to delete it?", "delete_playlist": "Delete Playlist", "rename_playlist": "<PERSON>ame Playlist", "rename_your_playlist": "Rename your playlist", "public": "Public", "private": "Private", "des_public_playlist": "Everyone can see this playlist"}, "duration": {"hour_only": "{{count}} hour", "minute_only": "{{count}} minute", "hour_and_minute": "{{hour}}h {{minute}}min"}, "shorten": {"billion": "B", "million": "M", "thousand": "K"}, "youtube_error": {"unsupported_format": "Unsupported format", "invalid_param": "Invalid parameter in the API request.", "html5_error": "The requested content cannot be played in an HTML5 player or another error occurred.", "not_found_or_private": "Video not found or marked as private.", "embedded_not_available": "Video not available for playback in embedded players (possibly region restricted).", "too_short": "Video is too short to play.", "unknown_code_error": "An error with code {{errorCode}} occurred."}}