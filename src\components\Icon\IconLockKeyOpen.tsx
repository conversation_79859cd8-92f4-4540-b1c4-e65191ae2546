import {SVGProps} from "react";

function IconLockKeyOpen({
  width = "24",
  height = "25",
  ...props
}: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox={`0 0 ${width} ${height}`}
      className={props.className}
      fill="none"
      {...props}
    >
      <g clipPath="url(#clip0_7346_464)">
        <path
          d="M20.7113 8.58024H8.51576V5.9669C8.51576 4.04249 10.0758 2.48246 12.0002 2.48246C13.6738 2.48246 15.1798 3.68023 15.5021 5.26892C15.6419 5.92477 16.4392 6.18336 16.9373 5.73437C17.1639 5.53012 17.2671 5.22222 17.2095 4.92266C16.7173 2.49879 14.5264 0.740234 12.0002 0.740234C9.11484 0.743234 6.77654 3.08154 6.77354 5.9669V8.58024H3.2891C2.32687 8.58019 1.54688 9.36022 1.54688 10.3225V22.518C1.54688 23.4802 2.32687 24.2603 3.2891 24.2602H20.7113C21.6735 24.2602 22.4535 23.4802 22.4535 22.518V10.3225C22.4535 9.36028 21.6735 8.58028 20.7113 8.58024ZM20.7113 22.518H3.2891V10.3225H20.7113V22.518ZM12.0002 12.0647C9.65317 12.0655 8.18713 14.6067 9.36133 16.6389C9.75076 17.3129 10.3832 17.8124 11.1291 18.0351V19.9047C11.1291 20.5753 11.855 20.9944 12.4358 20.6591C12.7053 20.5035 12.8713 20.2159 12.8713 19.9047V18.0351C15.1203 17.3637 15.7991 14.5095 14.0932 12.8975C13.5274 12.3629 12.7786 12.0649 12.0002 12.0647ZM12.0002 16.4202C10.9943 16.4202 10.3657 15.3313 10.8686 14.4602C11.3715 13.5891 12.6289 13.5891 13.1318 14.4602C13.2465 14.6589 13.3069 14.8842 13.3069 15.1136C13.3069 15.8352 12.7219 16.4202 12.0002 16.4202Z"
          fill="#FF4319"
        />
      </g>
      <defs>
        <clipPath id="clip0_7346_464">
          <rect
            width="24"
            height="24"
            fill="white"
            transform="translate(0 0.5)"
          />
        </clipPath>
      </defs>
    </svg>
  );
}

export default IconLockKeyOpen;
