import React from "react";
import clsx from "clsx";
import SongItem from "../SongItem";
import { ISong } from "../../types/song";

interface SongTableWrapperProps {
  songs: ISong[];
  className?: string;
  showDuration?: boolean;
  showAlbumInfo?: boolean;
  showReleaseDate?: boolean;
  showNumber?: boolean;
  onPlaySong?: (song: ISong) => void;
  extraMenuActions?: {
    icon?: React.ReactElement;
    label: React.ReactElement | string;
    action?: (song: ISong) => void;
    isAuth?: boolean;
  }[];
}

export default function SongTableWrapper({
  songs,
  className,
  showDuration = true,
  showAlbumInfo = true,
  showReleaseDate = true,
  showNumber = true,
  onPlaySong,
  extraMenuActions,
}: SongTableWrapperProps) {
  const handlePlayMusic = (song: ISong) => {
    if (onPlaySong) {
      onPlaySong(song);
    }
  };

  return (
    <div className={clsx("w-full", className)}>
      <div className="table w-full border-collapse">
        {/* Table Header */}
        <div className="table-header-group">
          <div className="table-row border-b border-[rgba(255,255,255,0.1)]">
            {showNumber && (
              <div className="table-cell align-middle text-center px-2 py-3 font-bold text-white">
                #
              </div>
            )}
            <div className="table-cell align-middle px-2 py-3 font-bold text-white">
              Song
            </div>
            {showAlbumInfo && (
              <div className="hidden lg:table-cell align-middle px-2 py-3 font-bold text-white">
                Album
              </div>
            )}
            {showReleaseDate && (
              <div className="hidden md:table-cell align-middle px-2 py-3 font-bold text-white">
                Release Date
              </div>
            )}
            <div className="table-cell align-middle px-2 py-3 font-bold text-white text-right">
              Duration
            </div>
          </div>
        </div>

        {/* Table Body */}
        <div className="table-row-group">
          {songs.map((song, index) => (
            <SongItem
              key={song.id}
              song={song}
              tableLayout={true}
              showNumber={showNumber}
              showDuration={showDuration}
              showAlbumInfo={showAlbumInfo}
              showReleaseDate={showReleaseDate}
              index={index}
              handlePlayMusic={() => handlePlayMusic(song)}
              extraMenuActions={extraMenuActions?.map(action => ({
                ...action,
                action: action.action ? () => action.action!(song) : undefined,
              }))}
            />
          ))}
        </div>
      </div>
    </div>
  );
}
