import {useTranslation} from "react-i18next";
import IconNoData from "@components/Icon/IconNoData";
import {useInfiniteQuery} from "@tanstack/react-query";
import QUERY_KEY from "@api/QueryKey";
import ApiSearch from "@api/ApiSearch";
import SongCardSkeleton from "@components/SongCardSkeleton";
import SongItem from "@components/SongItem";
import {useDispatch} from "react-redux";
import {playSingleSong} from "@redux/slices/PlayerSlice";
import {useEffect, useRef} from "react";
import {IDataWithMeta} from "@api/Fetcher";
import {ISong} from "src/types";
import {useLocation} from "react-router-dom";
import Subtitle from "@components/Subtitle";

export function SongsView() {
  const {t} = useTranslation();

  const location = useLocation();
  const query = new URLSearchParams(location.search);
  const searchValue = query.get("q");

  const observer = useRef<IntersectionObserver | null>(null);
  const dispatch = useDispatch();
  const lastElementRef = useRef<HTMLDivElement | null>(null);
  const {data, fetchNextPage, isLoading, isError, hasNextPage} =
    useInfiniteQuery<IDataWithMeta<ISong[]>, Error>({
      queryKey: [QUERY_KEY.SEARCH.GET_SEARCH_SONGS, searchValue],
      queryFn: ({pageParam = 0}) =>
        ApiSearch.searchSong({
          keyword: searchValue || "",
          pageSize: 20,
          page: pageParam as number,
        }),
      getNextPageParam: (lastPage) =>
        lastPage?.meta?.totalPage - 1 > lastPage?.meta?.currentPage
          ? (lastPage?.meta?.currentPage || 0) + 1
          : undefined,
      initialPageParam: 0,
    });

  useEffect(() => {
    if (isLoading || isError) return;

    if (observer.current) observer.current.disconnect();

    observer.current = new IntersectionObserver((entries) => {
      if (entries[0].isIntersecting && hasNextPage) {
        fetchNextPage();
      }
    });

    if (lastElementRef?.current) {
      observer.current.observe(lastElementRef.current);
    }

    return () => {
      if (observer.current) {
        observer.current.disconnect();
      }
    };
  }, [isLoading, isError, data, hasNextPage]);

  const isEmpty =
    !data || !data.pages || data.pages.every((page) => page.data.length === 0);

  return (
    <div className="w-full flex flex-col gap-1 sm:gap-2 md:gap-3 lg:gap-4">
      <Subtitle subtitle={t("common.songs")} seeMore={false} />
      {isLoading && !data && (
        <div className="flex flex-col">
          {[...Array(5)].map((_, index) => (
            <SongCardSkeleton key={`skeleton_${index}`} />
          ))}
        </div>
      )}
      {!isLoading && (isError || isEmpty) && (
        <div className="flex justify-center items-center flex-col lg:gap-2.5 gap-1">
          <IconNoData />
          <span className="text-[#E3E3E3] w-full text-base font-normal flex justify-center">
            {t("common.song_list_not_found")}
          </span>
        </div>
      )}
      <div className="max-sm:-mx-2">
        {!isLoading &&
          !(isError || isEmpty) &&
          data?.pages?.map((page, pageIndex) =>
            page?.data?.map((song, index) => {
              const globalIndex = index + 1 + pageIndex * 20;
              return (
                <SongItem
                  song={song}
                  key={`search_song_${song?.id}`}
                  left={
                    <div className="flex gap-x-6 items-center mr-6" key={index}>
                      <div className="text-base text-center lg:w-10 md:w-8 sm:w-6 w-4 text-white font-normal">
                        {globalIndex}
                      </div>
                    </div>
                  }
                  handlePlayMusic={() => {
                    dispatch(playSingleSong(song));
                  }}
                />
              );
            }),
          )}
        {hasNextPage && (
          <div className="flex flex-col">
            <SongCardSkeleton />
          </div>
        )}
      </div>
      {!isLoading && <div ref={lastElementRef} className="h-1"></div>}
    </div>
  );
}
