import {SVGProps} from "react";

function IconChartLine(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={20}
      height={20}
      fill="none"
      {...props}
    >
      <g clipPath="url(#a)">
        <path
          fill={props.fill || "currentColor"}
          d="M2 2a1 1 0 0 1 .993.883L3 3v13h15a1 1 0 0 1 .117 1.993L18 18H2a1 1 0 0 1-.993-.883L1 17V3a1 1 0 0 1 1-1Zm16.194 2.818c.9 0 1.35 1.088.714 1.724l-5.57 5.57a1.1 1.1 0 0 1-1.555 0L9.025 9.354 5.49 12.889a1 1 0 0 1-1.415-1.414l4.172-4.172a1.1 1.1 0 0 1 1.556 0l2.758 2.758 3.242-3.243h-.414a1 1 0 0 1 0-2h2.805Z"
        />
      </g>
      <defs>
        <clipPath id="a">
          <path fill={props.fill || "currentColor"} d="M0 0h20v20H0z" />
        </clipPath>
      </defs>
    </svg>
  );
}

export default IconChartLine;
