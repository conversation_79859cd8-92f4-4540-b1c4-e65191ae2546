import {SVGProps} from "react";

function IconHeart({
  width = 20,
  height = 20,
  ...props
}: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 16 14"
      fill="none"
      {...props}
    >
      <path
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={1}
        d="M14.197 2.06a3.62 3.62 0 0 0-5.12 0l-.698.699-.698-.698a3.62 3.62 0 1 0-5.12 5.12l.697.698L8.378 13 13.5 7.88l.697-.698a3.619 3.619 0 0 0 0-5.121v0Z"
      />
    </svg>
  );
}

export default IconHeart;
