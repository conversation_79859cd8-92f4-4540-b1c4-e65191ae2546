import CustomIconButton from "@components/CustomIconButton";
import IconRandom from "@components/Icon/IconRandom";
import IconRepeat from "@components/Icon/IconRepeat";
import IconSkipBehind from "@components/Icon/IconSkipBehind";
import IconSkipForward from "@components/Icon/IconSkipForward";
import {PauseRounded, PlayArrowRounded} from "@mui/icons-material";
import {Badge, IconButton} from "@mui/material";
import {
  handleNextSong,
  handlePrevSong,
  shuffleQueue,
  unShuffleQueue,
  updatePlayMode,
} from "@redux/slices/PlayerSlice";
import {IRootState} from "@redux/store";
import clsx from "clsx";
import {useTranslation} from "react-i18next";
import {useDispatch, useSelector} from "react-redux";
import {PlayMode} from "src/types";
import {useWindowWidth} from "src/utils/hooks";
import ControllerTime from "./ControllerTime";
import {logEvent} from "src/utils/firebase";
import PlayerUtil from "src/core/PlayerUtil";
import PlayerSlider from "./PlayerSlider";
import CustomTooltip from "@components/CustomTooltip";

interface IControllerProps {
  className?: string;
  sliderClassName?: string;
  timeClassName?: string;
  inLyricDetail?: boolean;
}

export default function Controller({
  className,
  sliderClassName,
  timeClassName,
  inLyricDetail,
}: IControllerProps) {
  const {
    playMode,
    currentSong,
    isShuffle,
    queueList,
    paused,
    isSuggestionEnabled,
    suggestedSongs,
  } = useSelector((state: IRootState) => state.player);
  const {t} = useTranslation();
  const dispatch = useDispatch();
  const currentIndex = queueList?.findIndex(
    (song) => song?.id === currentSong?.id,
  );
  const width = useWindowWidth();

  const getNextTooltip = () => {
    switch (playMode) {
      case PlayMode.REPEAT:
        return t("common.menu.play_a_song");
      case PlayMode.REPEAT_ONE:
        return t("common.menu.un_playback");
      default:
        return t("common.menu.playback");
    }
  };

  return (
    <div
      className={clsx(
        "flex flex-col items-center max-[568px]:gap-0 max-[834px]:gap-2",
        className,
        !inLyricDetail && "max-[568px]:items-end",
      )}
    >
      <div className="flex sm:gap-x-8 items-center gap-x-4">
        <CustomTooltip
          title={
            !isShuffle ? t("common.menu.shuffle") : t("common.menu.un_shuffle")
          }
        >
          <div>
            <CustomIconButton
              Icon={IconRandom}
              className={`flex items-center ${isShuffle ? "text-white" : "text-[#A3A1A2]"} ${!inLyricDetail && "max-[568px]:hidden"}`}
              disabled={!(queueList.length > 1)}
              onClick={
                isShuffle
                  ? () => {
                      dispatch(unShuffleQueue());
                      logEvent("on_off_loop_music", {
                        song_id: currentSong?.id,
                        song_name: currentSong?.name,
                        song_artist: currentSong?.artists
                          ?.map((artist) => artist?.stageName ?? artist?.name)
                          .join(", "),
                        play_position: PlayerUtil.instance.currentTime,
                      });
                    }
                  : () => {
                      dispatch(shuffleQueue());
                      logEvent("on_off_loop_music", {
                        song_id: currentSong?.id,
                        song_name: currentSong?.name,
                        song_artist: currentSong?.artists
                          ?.map((artist) => artist?.stageName ?? artist?.name)
                          .join(", "),
                        play_position: PlayerUtil.instance.currentTime,
                      });
                    }
              }
            />
          </div>
        </CustomTooltip>
        <CustomTooltip title={t("common.menu.previous")}>
          <div>
            <CustomIconButton
              Icon={IconSkipBehind}
              className="flex items-center"
              disabled={!(currentIndex > 0)}
              onClick={(e) => {
                e.stopPropagation();
                const prevSong = queueList[currentIndex - 1];
                dispatch(handlePrevSong());
                logEvent("skip_song", {
                  song_id: currentSong?.id,
                  song_name: currentSong?.name,
                  play_position: PlayerUtil.instance.currentTime,
                  new_song_id: prevSong?.id,
                  new_song_name: prevSong?.name,
                  skip_position: 0,
                  skip_direction: "backward",
                });
              }}
            />
          </div>
        </CustomTooltip>
        <CustomTooltip
          title={paused ? t("common.menu.play") : t("common.pending")}
        >
          <div>
            <IconButton
              onClick={(e) => {
                e.stopPropagation();
                if (paused && PlayerUtil.instance.ready) {
                  PlayerUtil.instance.play();
                  logEvent("play_song", {
                    song_id: currentSong?.id,
                    song_name: currentSong?.name,
                    song_artist: currentSong?.artists
                      ?.map((artist) => artist?.stageName ?? artist?.name)
                      .join(", "),
                    play_position: PlayerUtil.instance.currentTime,
                    song_duration: currentSong?.duration,
                    song_likes: currentSong?.totalLikes,
                    quality: currentSong?.audios?.[0]?.type,
                  });
                } else {
                  PlayerUtil.instance.pause();
                  logEvent("pause_song", {
                    song_id: currentSong?.id,
                    song_name: currentSong?.name,
                    play_position: PlayerUtil.instance.currentTime,
                    device_state: "paused",
                  });
                }
              }}
              className={clsx(
                "!bg-orange-500 !text-white shadow-[4px_4px_20px_0px_#B112005C,_-4px_-4px_20px_0px_#A708004A]",
                !inLyricDetail && "max-[568px]:h-[30px] max-[568px]:w-[30px]",
              )}
            >
              {paused ? <PlayArrowRounded /> : <PauseRounded />}
            </IconButton>
          </div>
        </CustomTooltip>
        <CustomTooltip title={t("playlist.next_play")}>
          <div>
            <CustomIconButton
              Icon={IconSkipForward}
              className="flex items-center"
              disabled={
                !(
                  queueList.length > 1 ||
                  (isSuggestionEnabled &&
                    suggestedSongs &&
                    suggestedSongs.length > 0)
                )
              }
              onClick={(e) => {
                e.stopPropagation();
                const nextSong = queueList[currentIndex + 1];
                dispatch(handleNextSong({isManual: true}));
                logEvent("skip_song", {
                  song_id: currentSong?.id,
                  song_name: currentSong?.name,
                  play_position: PlayerUtil.instance.currentTime,
                  new_song_id: nextSong?.id,
                  new_song_name: nextSong?.name,
                  skip_position: 0,
                  skip_direction: "forward",
                });
              }}
            />
          </div>
        </CustomTooltip>
        {(width > 568 || inLyricDetail) && (
          <CustomTooltip title={getNextTooltip()}>
            <div>
              <Badge
                badgeContent={playMode === PlayMode.REPEAT_ONE ? 1 : undefined}
                classes={{badge: "bg-red-600 !font-bold select-none"}}
              >
                <CustomIconButton
                  Icon={IconRepeat}
                  className={
                    playMode === PlayMode.REPEAT ||
                    playMode === PlayMode.REPEAT_ONE
                      ? "text-white"
                      : "text-[#A3A1A2]"
                  }
                  onClick={() => {
                    const newMode =
                      playMode === PlayMode.REPEAT_ONE ||
                      playMode === PlayMode.REPEAT
                        ? PlayMode.REPEAT_ONE
                        : PlayMode.REPEAT;
                    dispatch(updatePlayMode(newMode));
                    logEvent("on_off_loop_music", {
                      song_id: currentSong?.id,
                      song_name: currentSong?.name,
                      song_artist: currentSong?.artists
                        ?.map((artist) => artist?.stageName ?? artist?.name)
                        .join(", "),
                      play_position: PlayerUtil.instance.currentTime,
                    });
                  }}
                />
              </Badge>
            </div>
          </CustomTooltip>
        )}
      </div>
      <div
        className={clsx(
          "flex gap-x-4 items-center w-full",
          inLyricDetail && "mt-2",
        )}
      >
        <ControllerTime
          time={PlayerUtil.instance.currentTime}
          className={clsx("player-position", timeClassName)}
        />
        <PlayerSlider
          defaultValue={
            PlayerUtil.instance.duration
              ? (PlayerUtil.instance.currentTime /
                  PlayerUtil.instance.duration) *
                100
              : 0
          }
          className={sliderClassName}
          trackClassName="max-[568px]:bg-[#FF0000]"
        />
        <ControllerTime
          time={PlayerUtil.instance.duration}
          className={clsx("player-duration", timeClassName)}
        />
      </div>
    </div>
  );
}
