const fakeData = [
  {
    id: 1,
    account: "user1",
    email: "<EMAIL>",
    type: "LAPTOP",
    device: "MacBook Pro",
    status: "ACTIVE",
    report: "No issues reported",
    violation: "",
    premiumType: 3,
    extensionPeriod: "2025-02-15T16:30:00Z",
    listDevice: [
      {device: "MacBook Pro", lastLoggedIn: "2025-02-25T02:59:15Z"},
      {device: "MacBook", lastLoggedIn: "2025-02-25T02:59:15Z"},
    ],
    statusHistory: [
      {
        device: "MacBook",
        executionTime: "2025-02-25T02:59:15Z",
        status: "ACTIVE",
      },
      {
        device: "MacBook",
        executionTime: "2025-02-25T02:59:15Z",
        status: "PENDING",
      },
    ],
  },
  {
    id: 2,
    account: "user2",
    email: "<EMAIL>",
    type: "TABLET",
    device: "iPad Air",
    status: "LOCKED",
    report: "Device locked due to suspicious activity",
    violation: "Yes",
    premiumType: 1,
    extensionPeriod: "2025-02-15T16:30:00Z",
    listDevice: [
      {device: "MacBook Pro", lastLoggedIn: "2025-02-25T02:59:15Z"},
      {device: "MacBook", lastLoggedIn: "2025-02-25T02:59:15Z"},
    ],
    statusHistory: [
      {
        device: "MacBook",
        executionTime: "2025-02-25T02:59:15Z",
        status: "ACTIVE",
      },
      {
        device: "MacBook",
        executionTime: "2025-02-25T02:59:15Z",
        status: "PENDING",
      },
    ],
  },
  {
    id: 3,
    account: "user3",
    email: "<EMAIL>",
    type: "PHONE",
    device: "Samsung Galaxy",
    status: "PENDING",
    report: "Pending user verification",
    violation: "",
    premiumType: 2,
    extensionPeriod: "",
  },
  {
    id: 4,
    account: "user4",
    email: "<EMAIL>",
    type: "LAPTOP",
    device: "MacBook Pro",
    status: "ACTIVE",
    report: "",
    violation: "",
    premiumType: 3,
    extensionPeriod: "",
  },
  {
    id: 5,
    account: "user5",
    email: "<EMAIL>",
    type: "TABLET",
    device: "iPad Air",
    status: "PENDING",
    report: "User delayed in providing required documents",
    violation: "Yes",
    premiumType: 1,
    extensionPeriod: "",
  },
];

export default fakeData;
