import Header<PERSON><PERSON> from "./HeaderChart";
import {useState} from "react";
import AreaChartCustom from "./AreaChartCustom";
import {useTranslation} from "react-i18next";
import {EStatisticsDateRangeType} from "src/types";
import CommonDateRange from "@components/CommonDateRange";
import dayjs from "dayjs";
import ApiCMSStatistics, {
  ISystemVisitAndListenParams,
} from "@api/ApiCMSStatistics";
import {
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  SelectChangeEvent,
} from "@mui/material";
import {useQuery} from "@tanstack/react-query";
import QUERY_KEY from "@api/QueryKey";

function ListenOverTime() {
  const {t} = useTranslation();
  const [params, setParams] = useState<ISystemVisitAndListenParams>({
    typeStatistic: EStatisticsDateRangeType.DAY,
    fromDate: dayjs().subtract(30, "day").format("YYYY-MM-DD"),
    toDate: dayjs().format("YYYY-MM-DD"),
  });

  const {data: dataSystemVisit} = useQuery({
    queryKey: [QUERY_KEY.STATISTICS.GET_SYSTEM_LISTENS, params],
    queryFn: () => ApiCMSStatistics.getSystemListen(params),
  });

  const dataDashboard = dataSystemVisit?.data?.map((item) => {
    return {
      value: item.counts,
      name: item.date,
    };
  });

  const LINEARGRADIENT = {
    id: "colorRed",
    linear: [
      {offset: "0%", color: "#FF4319", stopOpacity: 0.8},
      {offset: "100%", color: "#FF4319", stopOpacity: 0},
    ],
  };

  const listItems = [
    {
      value: EStatisticsDateRangeType.DAY,
      label: t("cms.dashboard.day"),
    },
    {
      value: EStatisticsDateRangeType.MONTH,
      label: t("cms.dashboard.month"),
    },
    {
      value: EStatisticsDateRangeType.YEAR,
      label: t("cms.dashboard.year"),
    },
  ];

  const labels = {
    x:
      params.typeStatistic === EStatisticsDateRangeType.DAY
        ? t("cms.dashboard.day")
        : params.typeStatistic === EStatisticsDateRangeType.MONTH
          ? t("cms.dashboard.month")
          : t("cms.dashboard.year"),
    y: t("cms.dashboard.listen"),
  };

  const handleChangeType = (e: SelectChangeEvent) => {
    const type: EStatisticsDateRangeType = e.target
      .value as unknown as EStatisticsDateRangeType;
    const fromDate =
      type === EStatisticsDateRangeType.DAY
        ? dayjs().subtract(30, "day")
        : type === EStatisticsDateRangeType.MONTH
          ? dayjs().subtract(12, "month")
          : dayjs().subtract(5, "year");
    const toDate = dayjs().format("YYYY-MM-DD");
    setParams((prev) => ({
      ...prev,
      typeStatistic: parseInt(e.target.value) as EStatisticsDateRangeType,
      fromDate: fromDate.format("YYYY-MM-DD"),
      toDate,
    }));
  };

  return (
    <div className="bg-white rounded-2xl shadow-md p-5">
      <div>
        <div className="flex justify-between items-center">
          <HeaderChart title={t("cms.dashboard.listen_over_time")} />
          <div className="flex items-center gap-3">
            <CommonDateRange
              labelDateRange={[
                t("cms.dashboard.start"),
                t("cms.dashboard.end"),
              ]}
              typeView={params.typeStatistic}
              limitByView={{day: 30, month: 12, year: 5}}
              valueDateRange={[params?.fromDate, params?.toDate]}
              onChangeStartDate={(v) => {
                setParams((prev) => {
                  return {
                    ...prev,
                    fromDate: dayjs(v).format("YYYY-MM-DD"),
                  };
                });
              }}
              onChangeEndDate={(v) => {
                setParams((prev) => {
                  return {
                    ...prev,
                    toDate: dayjs(v).format("YYYY-MM-DD"),
                  };
                });
              }}
            />

            <FormControl fullWidth>
              <InputLabel id="demo-simple-select-label">
                {t("cms.dashboard.by")}
              </InputLabel>
              <Select
                labelId="demo-simple-select-label"
                id="demo-simple-select"
                value={params.typeStatistic.toString()}
                sx={{
                  "background": "#f2f2f3",
                  "borderRadius": "8px",
                  "width": "100px",
                  "& .MuiOutlinedInput-notchedOutline": {
                    border: "1px solid #dcdee0",
                  },
                  "&:hover .MuiOutlinedInput-notchedOutline": {
                    border: "1px solid #dcdee0",
                  },
                }}
                label={t("cms.dashboard.by")}
                onChange={handleChangeType}
              >
                {listItems.map((item) => (
                  <MenuItem value={item.value} key={item.label}>
                    {item.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </div>
        </div>

        <div className="p-5">
          <AreaChartCustom
            data={dataDashboard ?? []}
            linearGradientColor={LINEARGRADIENT}
            labels={labels}
          />
        </div>
      </div>
    </div>
  );
}

export default ListenOverTime;
