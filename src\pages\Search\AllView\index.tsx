import {useTranslation} from "react-i18next";
import Subtitle from "@components/Subtitle";
import {SongAllView} from "./components/SongAllView";
import {ArtistAllView} from "./components/ArtistAllView";
import {PlaylistAllView} from "./components/PlaylistAllView";
import {YoutubeAllView} from "./components/YoutubeAllView";
import {YoutubePlaylistAllView} from "./components/YoutubePlaylistAllView";
import {PlaylistType} from "src/types";
import ApiSearch, {ISearchParams} from "@api/ApiSearch";
import QUERY_KEY from "@api/QueryKey";
import {useQueries, useQuery} from "@tanstack/react-query";
import SongCardSkeleton from "@components/SongCardSkeleton";
import SubTitleSkeleton from "@components/SubTitleSkeleton";
import Slider from "@components/Slider";
import {SwiperSlide} from "swiper/react";
import ArtistCardSkeleton from "@components/ArtistCardSkeleton";
import IconNoData from "@components/Icon/IconNoData";
import AlbumCardSkeleton from "@components/AlbumCardSkeleton";
import YoutubeCardSkeleton from "@components/YoutubeCardSkeleton";

interface IAllViewClickProp {
  onSongViewClick: () => void;
  onAlbumViewClick: () => void;
  onPlaylistViewClick: () => void;
  onArtistViewClick: () => void;
  onYoutubeViewClick: () => void;
  onYoutubePlaylistViewClick: () => void;
  searchValue: string;
}

export function AllView({
  onSongViewClick,
  onAlbumViewClick,
  onPlaylistViewClick,
  onArtistViewClick,
  onYoutubeViewClick,
  onYoutubePlaylistViewClick,
  searchValue,
}: IAllViewClickProp) {
  const {t} = useTranslation();

  const params: ISearchParams = {
    keyword: searchValue || "",
    page: 0,
    pageSize: 5,
  };

  const searchParams = (type: number): ISearchParams => ({
    keyword: searchValue || "",
    page: 0,
    pageSize: 5,
    type,
  });

  const {data: dataSearchSongs, isLoading: isLoadingSong} = useQuery({
    queryKey: [QUERY_KEY.SEARCH.GET_SEARCH_ALL_VIEW_SONGS, params],
    queryFn: () => ApiSearch.searchSong(params),
    enabled: !!searchValue,
  });

  const {data: dataSearchArtist, isLoading: isLoadingArtist} = useQuery({
    queryKey: [QUERY_KEY.SEARCH.GET_SEARCH_ALL_VIEW_ARTISTS, params],
    queryFn: () => ApiSearch.searchArtists(params),
    enabled: !!searchValue,
  });

  const queryResults = useQueries({
    queries: [
      {
        queryKey: [
          QUERY_KEY.SEARCH.GET_SEARCH_ALL_VIEW_PLAYLIST,
          searchParams(PlaylistType.PLAYLIST),
        ],
        queryFn: () =>
          ApiSearch.searchPlaylists(searchParams(PlaylistType.PLAYLIST)),
        enabled: !!searchValue,
      },
      {
        queryKey: [
          QUERY_KEY.SEARCH.GET_SEARCH_ALL_VIEW_ALBUM,
          searchParams(PlaylistType.ALBUM),
        ],
        queryFn: () =>
          ApiSearch.searchPlaylists(searchParams(PlaylistType.ALBUM)),
        enabled: !!searchValue,
      },
    ],
  });

  const dataSearchPlaylists = queryResults[0]?.data;
  const isLoadingPlaylist = queryResults[0]?.isLoading;

  const dataSearchAlbum = queryResults[1]?.data;
  const isLoadingAlbum = queryResults[1]?.isLoading;

  const {data: dataSearchYoutube, isLoading: isLoadingYoutube} = useQuery({
    queryKey: [QUERY_KEY.SEARCH.GET_SEARCH_ALL_VIEW_YOUTUBE, params],
    queryFn: () => ApiSearch.searchYoutube(params),
    enabled: !!searchValue,
  });

  const {data: dataSearchYoutubePlaylist, isLoading: isLoadingYoutubePlaylist} =
    useQuery({
      queryKey: [QUERY_KEY.SEARCH.GET_SEARCH_ALL_VIEW_YOUTUBE_PLAYLIST, params],
      queryFn: () => ApiSearch.searchYoutubePlaylist(params),
      enabled: !!searchValue,
    });

  return (
    <div className="flex flex-col lg:gap-8 md:gap-7 sm:gap-6 gap-5">
      {dataSearchSongs?.data?.length === 0 &&
        !isLoadingSong &&
        dataSearchArtist?.data?.length === 0 &&
        !isLoadingArtist &&
        dataSearchPlaylists?.data?.length === 0 &&
        !isLoadingPlaylist &&
        dataSearchAlbum?.data?.length === 0 &&
        !isLoadingAlbum &&
        dataSearchYoutube?.data?.length === 0 &&
        !isLoadingAlbum &&
        dataSearchYoutubePlaylist?.data?.length === 0 &&
        !isLoadingYoutubePlaylist && (
          <div className="flex justify-center items-center flex-col lg:gap-2.5 gap-1">
            <IconNoData />
            <span className="text-[#E3E3E3] w-full text-base font-normal flex justify-center">
              {t("search.search_no_data")}
            </span>
          </div>
        )}

      {isLoadingSong && !dataSearchSongs && (
        <div className="relative flex flex-col">
          <SubTitleSkeleton />
          <div>
            {Array.from({length: 5}).map((_, index) => (
              <SongCardSkeleton key={`skeleton_song_${index}`} />
            ))}
          </div>
        </div>
      )}
      {dataSearchSongs?.data && dataSearchSongs?.data?.length > 0 && (
        <div className="flex flex-col lg:gap-4 md:gap-3 sm:gap-2 gap-1">
          <Subtitle
            subtitle={t("common.songs")}
            handleClick={onSongViewClick}
          />
          <SongAllView
            data={dataSearchSongs?.data ?? []}
            queryKey={[QUERY_KEY.SEARCH.GET_SEARCH_ALL_VIEW_SONGS, params]}
          />
        </div>
      )}

      {isLoadingArtist && !dataSearchArtist && (
        <div className="relative flex flex-col lg:gap-4 md:gap-3 sm:gap-2 gap-1">
          <SubTitleSkeleton />
          <Slider slidesPerView={5}>
            {[...Array(5)].map((_, index) => (
              <SwiperSlide key={index} virtualIndex={index}>
                <ArtistCardSkeleton />
              </SwiperSlide>
            ))}
          </Slider>
        </div>
      )}
      {dataSearchArtist?.data && dataSearchArtist?.data?.length > 0 && (
        <div className="flex flex-col lg:gap-4 md:gap-3 sm:gap-2 gap-1">
          <Subtitle
            subtitle={t("common.artist")}
            handleClick={onArtistViewClick}
          />
          <ArtistAllView data={dataSearchArtist?.data ?? []} />
        </div>
      )}

      {isLoadingAlbum && !dataSearchAlbum && (
        <div className="relative flex flex-col lg:gap-4 md:gap-3 sm:gap-2 gap-1">
          <SubTitleSkeleton />
          <Slider slidesPerView={5}>
            {[...Array(5)].map((_, index) => (
              <SwiperSlide key={index} virtualIndex={index}>
                <AlbumCardSkeleton />
              </SwiperSlide>
            ))}
          </Slider>
        </div>
      )}
      {dataSearchAlbum?.data && dataSearchAlbum?.data?.length > 0 && (
        <div className="flex flex-col lg:gap-4 md:gap-3 sm:gap-2 gap-1">
          <Subtitle
            subtitle={t("common.album")}
            handleClick={onAlbumViewClick}
          />
          <PlaylistAllView data={dataSearchAlbum?.data ?? []} />
        </div>
      )}

      {isLoadingPlaylist && !dataSearchPlaylists && (
        <div className="relative flex flex-col lg:gap-4 md:gap-3 sm:gap-2 gap-1">
          <SubTitleSkeleton />
          <Slider slidesPerView={5}>
            {[...Array(5)].map((_, index) => (
              <SwiperSlide key={index} virtualIndex={index}>
                <AlbumCardSkeleton isMultipleInfo={false} />
              </SwiperSlide>
            ))}
          </Slider>
        </div>
      )}
      {dataSearchPlaylists?.data && dataSearchPlaylists?.data?.length > 0 && (
        <div className="flex flex-col lg:gap-4 md:gap-3 sm:gap-2 gap-1">
          <Subtitle
            subtitle={t("common.playlist")}
            handleClick={onPlaylistViewClick}
          />
          <PlaylistAllView
            data={dataSearchPlaylists?.data ?? []}
            haveLayer={false}
          />
        </div>
      )}

      {isLoadingYoutube && !dataSearchYoutube && (
        <div className="relative flex flex-col lg:gap-4 md:gap-3 sm:gap-2 gap-1">
          <SubTitleSkeleton />
          <Slider slidesPerView={5}>
            {[...Array(5)].map((_, index) => (
              <SwiperSlide key={index} virtualIndex={index}>
                <YoutubeCardSkeleton />
              </SwiperSlide>
            ))}
          </Slider>
        </div>
      )}
      {dataSearchYoutube?.data && dataSearchYoutube?.data?.length > 0 && (
        <div className="flex flex-col lg:gap-4 md:gap-3 sm:gap-2 gap-1">
          <Subtitle subtitle="Youtube" handleClick={onYoutubeViewClick} />
          <YoutubeAllView data={dataSearchYoutube?.data ?? []} />
        </div>
      )}
      {isLoadingYoutubePlaylist && !dataSearchYoutubePlaylist && (
        <div className="relative flex flex-col lg:gap-4 md:gap-3 sm:gap-2 gap-1">
          <SubTitleSkeleton />
          <Slider slidesPerView={5}>
            {[...Array(5)].map((_, index) => (
              <SwiperSlide key={index} virtualIndex={index}>
                <YoutubeCardSkeleton />
              </SwiperSlide>
            ))}
          </Slider>
        </div>
      )}
      {dataSearchYoutubePlaylist?.data &&
        dataSearchYoutubePlaylist?.data?.length > 0 && (
          <div className="flex flex-col lg:gap-4 md:gap-3 sm:gap-2 gap-1">
            <Subtitle
              subtitle={t("common.youtube_playlist")}
              handleClick={onYoutubePlaylistViewClick}
            />
            <YoutubePlaylistAllView
              data={dataSearchYoutubePlaylist?.data ?? []}
            />
          </div>
        )}
    </div>
  );
}
