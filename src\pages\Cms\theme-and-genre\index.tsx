import ApiThemeAndGenre, {IGetThemeAndGenreList} from "@api/ApiThemeAndGenre";
import QUERY_KEY from "@api/QueryKey";
import GlobalButton from "@components/ButtonGlobal";
import IconAdd from "@components/Icon/IconAdd";
import ModalComfirm from "@components/ModalConfirm";
import {IconButton, Tab, Tabs} from "@mui/material";
import {GridColDef, GridRowId} from "@mui/x-data-grid";
import {useMutation, useQuery} from "@tanstack/react-query";
import {useMemo, useState} from "react";
import {toast} from "react-toastify";
import useDebounce from "src/hooks/useDebounce";
import {EThemeAndGenreType, IThemeAndGenre} from "src/types";
import CmsTable from "../components/CmsTable";
import SearchInput from "../components/SearchInput";
import AddThemeAndGenreModal from "./components/AddThemeAndGenreModal";
import {useTranslation} from "react-i18next";
import {LocalizationProvider} from "@mui/x-date-pickers/LocalizationProvider/LocalizationProvider";
import {AdapterDayjs} from "@mui/x-date-pickers/AdapterDayjs";
import dayjs from "dayjs";
import {DatePicker} from "@mui/x-date-pickers/DatePicker";
import {convertDate, convertNumber} from "src/utils/timeUtils";
import IconCmsEdit from "@components/Icon/IconCmsEdit";
import IconCmsDelete from "@components/Icon/IconCmsDelete";
import ModalDetailGenres from "./components/ModalDetailGenres/index.";

export default function CmsThemeAndGenre() {
  const {t} = useTranslation();
  const [genresId, setGenresId] = useState<GridRowId>("");
  const [openModalDetail, setOpenModalDetail] = useState(false);
  const [selectedItem, setSelectedItem] = useState<
    IThemeAndGenre | undefined
  >();
  const [searchText, setSearchText] = useState("");
  const [addModalOpen, setAddModalOpen] = useState(false);
  const [deleteConfirmModalOpen, setDeleteConfirmModalOpen] = useState(false);
  const debounceSearchText = useDebounce(searchText);

  const [params, setParams] = useState<IGetThemeAndGenreList>({
    type: EThemeAndGenreType.THEME,
    page: 0,
    pageSize: 10,
    createdAt: "",
  });

  const filterValues = useMemo(
    () => [debounceSearchText, params.createdAt],
    [debounceSearchText, params.createdAt],
  );

  const columns: GridColDef<IThemeAndGenre>[] = [
    {
      field: "images",
      headerName: t("cms.theme_and_genre.cover"),
      headerClassName: "flex item-center",
      width: 136,
      renderCell: (params) => (
        <div className="w-full h-full flex justify-center items-center">
          <img
            src={
              params.value.SMALL ||
              params.value.DEFAULT ||
              "/image/default-music.png"
            }
            className="h-12 w-12 rounded-[4px] object-cover"
          />
        </div>
      ),
      disableColumnMenu: true,
      sortable: false,
      headerAlign: "center",
    },
    {
      field: "name",
      headerName:
        params.type === EThemeAndGenreType.THEME
          ? t("common.theme_name")
          : t("common.genre_name"),
      width: 220,
      sortable: false,
    },
    {
      field: "description",
      headerName: t("common.describe"),
      width: 260,
      sortable: false,
    },
    {
      field: "totalListen",
      headerName: t("cms.theme_and_genre.total_listen_count"),
      sortable: false,
      width: 136,
      renderCell: (params) => {
        return convertNumber(params?.value);
      },
    },
    {
      field: "totalSong",
      headerName: t("cms.theme_and_genre.total_songs"),
      sortable: false,
      width: 136,
      renderCell: (params) => {
        return convertNumber(params?.value);
      },
    },
    {
      field: "totalPlaylist",
      headerName: t("cms.theme_and_genre.total_albums"),
      sortable: false,
      width: 136,
      renderCell: (params) => {
        return convertNumber(params?.value);
      },
    },
    {
      field: "createdAt",
      headerName: t("cms.theme_and_genre.created_date"),
      sortable: false,
      width: 120,
      renderCell: (params) => {
        return convertDate(params?.value);
      },
    },
    {
      field: "updatedAt",
      headerName: t("cms.theme_and_genre.last_updated"),
      sortable: false,
      width: 220,
      renderCell: (params) => {
        return convertDate(params?.value);
      },
    },
    {
      field: "action",
      headerName: t("cms.theme_and_genre.action"),
      sortable: false,
      width: 120,
      headerClassName: "sticky-header",
      cellClassName: "sticky-cell",
      renderCell: (param) => {
        return (
          <div className="flex">
            <IconButton
              onClick={() => {
                setSelectedItem(param.row);
                setAddModalOpen(true);
              }}
            >
              <IconCmsEdit />
            </IconButton>
            <IconButton
              onClick={() => {
                setSelectedItem(param.row);
                setDeleteConfirmModalOpen(true);
              }}
            >
              <IconCmsDelete />
            </IconButton>
          </div>
        );
      },
    },
  ];

  const {
    data: themeAndGenreData,
    isFetching,
    refetch,
  } = useQuery({
    queryKey: [QUERY_KEY.ALBUM.GET_LIST_ALBUM_CMS, params, debounceSearchText],
    queryFn: () =>
      ApiThemeAndGenre.getThemeAndGenreList({
        ...params,
        keyword: debounceSearchText,
      }),
  });

  const {mutateAsync: deleteThemeAndGenreMutaionAsync} = useMutation({
    mutationFn: ApiThemeAndGenre.deleteThemeAndGenre,
    onSuccess: () => {
      toast.success(t("common.delete_successfully"));
      setSelectedItem(undefined);
      refetch();
    },
  });

  return (
    <div className="p-5 bg-white space-y-4">
      <Tabs
        value={params.type}
        onChange={(_, value) => setParams((pre) => ({...pre, type: value}))}
        sx={{
          "& .MuiTab-root": {
            textTransform: "none",
            fontSize: 16,
          },
          "& .Mui-selected": {color: "#FF4319"},
        }}
        classes={{indicator: "!bg-[#FF4319]"}}
        textColor={"inherit"}
      >
        <Tab
          label={t("cms.theme_and_genre.theme")}
          value={EThemeAndGenreType.THEME}
        />
        <Tab
          label={t("cms.theme_and_genre.genre")}
          value={EThemeAndGenreType.GENRE}
        />
      </Tabs>
      <div className="flex justify-between flex-wrap gap-3 items-center">
        <div className="flex gap-3">
          <SearchInput
            searchText={searchText}
            onChange={(v) => setSearchText(v)}
          />
          <LocalizationProvider dateAdapter={AdapterDayjs}>
            <DatePicker
              disableFuture
              onChange={(v) => {
                if (v) {
                  setParams((pre) => ({
                    ...pre,
                    createdAt: dayjs(v).format("YYYY-MM-DD"),
                  }));
                }
              }}
              slotProps={{
                textField: {
                  size: "small",
                  placeholder: t("cms.theme_and_genre.created_date"),
                },
                field: {
                  clearable: true,
                  onClear: () =>
                    setParams((pre) => ({
                      ...pre,
                      createdAt: "",
                    })),
                },
              }}
              className="cms-datepicker-gray"
            />
          </LocalizationProvider>
        </div>
        <GlobalButton
          text={t("common.add")}
          onClick={() => {
            setAddModalOpen(true);
            // reset modal
            setSelectedItem(undefined);
          }}
          startIcon={<IconAdd />}
          className="h-full"
        />
      </div>
      <CmsTable
        ordinalColumn
        rows={themeAndGenreData?.data}
        columns={columns}
        loading={isFetching}
        totalItems={themeAndGenreData?.meta.totalItems || 0}
        currentPage={params.page || 0}
        onPageChange={(page) => setParams((pre) => ({...pre, page}))}
        rowsPerPage={params.pageSize || 10}
        onRowsPerPageChange={(rowsPerPage) =>
          setParams((pre) => ({...pre, pageSize: rowsPerPage}))
        }
        onRowDoubleClick={(params) => {
          setOpenModalDetail(true);
          setGenresId(params?.id);
        }}
        filterValues={filterValues}
        hideFooter
      />
      {openModalDetail && (
        <ModalDetailGenres
          open={openModalDetail}
          onClose={() => {
            setOpenModalDetail(false);
            setGenresId("" as GridRowId);
          }}
          genresId={genresId}
          type={params.type}
        />
      )}
      <AddThemeAndGenreModal
        selectedItem={selectedItem}
        open={addModalOpen}
        type={params.type}
        onClose={() => {
          setAddModalOpen(false);
        }}
        refetch={refetch}
      />
      <ModalComfirm
        title={t("common.delete_confirm")}
        open={deleteConfirmModalOpen}
        onConfirm={() => {
          setDeleteConfirmModalOpen(false);
          if (selectedItem) {
            deleteThemeAndGenreMutaionAsync(selectedItem.id);
          }
        }}
        onCancel={() => setDeleteConfirmModalOpen(false)}
      >
        {selectedItem && (
          <div>
            {params.type === EThemeAndGenreType.THEME
              ? t("cms.theme_and_genre.theme")
              : t("cms.theme_and_genre.genre")}
            : {selectedItem.name}
          </div>
        )}
      </ModalComfirm>
    </div>
  );
}
