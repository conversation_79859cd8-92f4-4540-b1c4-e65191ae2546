import HeaderTitle from "@components/HeaderTitle";
import IconArrowDown from "@components/Icon/IconArrowDown";
import IconArtist from "@components/Icon/IconArtist";
import IconGeneralCms from "@components/Icon/IconGeneralCms";
import IconLockCMS from "@components/Icon/IconLockCMS";
import IconLogoutCMS from "@components/Icon/IconLogoutCMS";
import IconMicrophone from "@components/Icon/IconMicrophone.tsx";
import IconMusicNote from "@components/Icon/IconMusicNote.tsx";
import IconMusicNote2 from "@components/Icon/IconMusicNote2";
import IconOtherCms from "@components/Icon/IconOtherCms";
import IconStatistical from "@components/Icon/IconStatistical";
import IconUserCMS from "@components/Icon/IconUserCMS";
import ModalConfirm from "@components/ModalConfirm";
import MenuIcon from "@mui/icons-material/Menu";
import {
  Avatar,
  Button,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  ListProps,
  Menu,
  MenuItem,
} from "@mui/material";
import ModalChangePassword from "@pages/Cms/accountManage/components/ModalChangePassword";
import {logoutUser} from "@redux/slices/UserSlice.ts";
import type {IRootState} from "@redux/store";
import clsx from "clsx";
import React, {useCallback, useEffect, useState} from "react";
import {useTranslation} from "react-i18next";
import {useDispatch, useSelector} from "react-redux";
import {Link, useLocation, useNavigate} from "react-router-dom";
import {Role} from "src/types";
import {useFullName} from "src/utils/global";
import {useNetworkStatus} from "src/utils/hooks";
import NoInternetSnackbar from "../components/NoInternetSnackbar";
import NoInternet from "./NoInternet";

interface CmsLayoutProps {
  children: JSX.Element;
}

interface CmsRoute extends ListProps {
  icon?: React.ReactElement;
  title: string;
  pathname?: string;
  onClick?: () => void;
}

export default function CmsLayout({children}: CmsLayoutProps) {
  const {t} = useTranslation();
  const location = useLocation();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const getFullName = useFullName();

  const {userInfo} = useSelector((state: IRootState) => state.user);
  const isAdmin = userInfo?.role === Role?.ADMIN;

  const [isModalLogout, setIsModalLogOut] = useState(false);
  const [isModalChangePassword, setIsModalChangePassword] = useState(false);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [open, setOpen] = useState(true);
  // TODO: Phase 2
  // const [userManagementMenuOpen, setUserManagementMenuOpen] = useState(false);
  const [generalManagementOpen, setGeneralManagementOpen] = useState(false);
  const [otherManagementOpen, setOtherManagementOpen] = useState(false);
  const {isOnline} = useNetworkStatus();

  const handleMenuClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => setAnchorEl(null);

  const handleLogout = () => {
    dispatch(logoutUser());
    navigate("/cms/login");
    setIsModalLogOut(false);
  };

  const routesCms: CmsRoute[] = isAdmin
    ? [
        {
          title: t("cms.statistical"),
          icon: <IconStatistical />,
          pathname: "/cms",
        },
        {
          title: t("cms.song_management"),
          icon: <IconMusicNote2 />,
          pathname: "/cms/song",
        },
        {
          title: t("cms.general_management"),
          icon: <IconGeneralCms />,
          onClick: () => setGeneralManagementOpen(!generalManagementOpen),
        },
        {
          title: t("cms.other_management"),
          icon: <IconOtherCms />,
          onClick: () => setOtherManagementOpen(!otherManagementOpen),
        },
        // TODO: Phase 2
        // {
        //   title: t("cms.user_management"),
        //   icon: <IconUser2 />,
        //   onClick: () => setUserManagementMenuOpen(!userManagementMenuOpen),
        // },
        {
          title: t("cms.artist_management"),
          icon: <IconArtist />,
          pathname: "/cms/artist",
        },
      ]
    : [
        {
          title: t("cms.song_management"),
          icon: <IconMusicNote2 />,
          pathname: "/cms/song",
        },
        {
          title: t("cms.album_management"),
          icon: <IconMusicNote />,
          pathname: "/cms/album",
        },
        {
          title: t("cms.theme_and_genre_management"),
          icon: <IconMicrophone />,
          pathname: "/cms/theme-and-genre",
        },
      ];

  const [selectedRoute, setSelectedRoute] = useState<{
    current: CmsRoute & {parentTitle?: string};
    parent?: CmsRoute;
  }>();

  const handleNavigate = useCallback(
    (route: CmsRoute, parentRoute?: CmsRoute) => {
      if (route.pathname && location.pathname !== route.pathname) {
        navigate(route.pathname);
      }
      setSelectedRoute({
        current: route,
        parent: parentRoute,
      });
      if (route.onClick) {
        route.onClick();
      }
    },
    [location.pathname, navigate],
  );

  const renderSubMenu = (
    items: {title: string; pathname: string}[],
    parentRoute: CmsRoute,
  ) => (
    <div className="flex flex-col gap-4 py-2 bg-[#FFFFFF0A]">
      {items.map((item, index) => (
        <ListItem key={index} disablePadding>
          <ListItemButton
            onClick={() =>
              handleNavigate(
                {
                  ...item,
                  title: `${parentRoute.title} / ${item.title}`,
                },
                parentRoute,
              )
            }
            selected={
              selectedRoute?.current.title ===
              `${parentRoute.title} / ${item.title}`
            }
            classes={{
              selected: "!bg-[#FF4319]",
            }}
          >
            <ListItemText
              sx={{
                color: "#C8C8C8",
                paddingLeft: "47px",
                fontSize: "16px",
                fontWeight: "400",
              }}
              primary={item.title}
            />
          </ListItemButton>
        </ListItem>
      ))}
    </div>
  );
  // TODO: Phase 2
  // const userManagementSubMenu = [
  //   {
  //     title: t("cms.user_account"),
  //     icon: <IconUser2 />,
  //     pathname: "/cms/account-user",
  //   },
  //   {
  //     title: t("cms.staff_management"),
  //     icon: <IconUser />,
  //     pathname: "/cms/staff",
  //   },
  //   {
  //     title: t("cms.premium_account_management"),
  //     icon: <IconPremium />,
  //     pathname: "/cms/account-premium",
  //   },
  // ];

  const generalManagementSubMenu = [
    {
      title: t("cms.album_management"),
      pathname: "/cms/album",
    },
    {
      title: t("cms.top100_management"),
      pathname: "/cms/top-album",
    },
    {
      title: t("cms.playlist_management"),
      pathname: "/cms/playlist",
    },
  ];

  const otherManagementSubMenu = [
    {
      title: t("cms.theme_and_genre_management"),
      pathname: "/cms/theme-and-genre",
    },
    // TODO: Phase 2
    // {
    //   title: t("cms.package_management"),
    //   pathname: "/cms/package",
    // },
  ];

  useEffect(() => {
    const currentPath = location.pathname;

    const mainRoute = routesCms.find((route) => route.pathname === currentPath);
    if (mainRoute) {
      setSelectedRoute({current: mainRoute});
      return;
    }

    const generalSubRoute = generalManagementSubMenu.find(
      (item) => item.pathname === currentPath,
    );
    if (generalSubRoute) {
      const parent = routesCms.find(
        (route) => route.title === t("cms.general_management"),
      );
      setSelectedRoute({
        current: {
          ...generalSubRoute,
          title: `${parent?.title} / ${generalSubRoute.title}`,
          parentTitle: parent?.title,
        },
        parent: parent,
      });
      return;
    }

    const otherSubRoute = otherManagementSubMenu.find(
      (item) => item.pathname === currentPath,
    );
    if (otherSubRoute) {
      const parent = routesCms.find(
        (route) => route.title === t("cms.other_management"),
      );
      setSelectedRoute({
        current: {
          ...otherSubRoute,
          title: `${parent?.title} / ${otherSubRoute.title}`,
          parentTitle: parent?.title,
        },
        parent: parent,
      });
      return;
    }

    if (currentPath === "/cms/account-personal-info") {
      setSelectedRoute({
        current: {
          title: t("cms.account_management"),
          pathname: currentPath,
        },
      });
      return;
    }

    setSelectedRoute(undefined);
  }, [location.pathname, t]);

  return (
    <div className="flex relative h-screen">
      <HeaderTitle />
      <div
        className={clsx(
          "w-[280px] bg-[#151313] overflow-x-scroll hide-scrollbar h-full overflow-y-auto",
          !open && "-translate-x-full absolute",
        )}
      >
        <Link
          to="/cms"
          className="my-16 w-[280px] flex flex-col flex-y-[18px] items-center"
          onClick={() => setSelectedRoute({current: routesCms?.[0]})}
        >
          <img src="/image/logo.png" className="w-40" alt="Logo" />
          <div className="text-white">{t("common.title_logo")}</div>
        </Link>
        <div className="bg-white opacity-[0.07] h-[1px] w-[calc(100%-40px)] mx-auto" />
        <List sx={{paddingTop: "28px"}}>
          {routesCms.map((route, index) => (
            <React.Fragment key={`route_${index}`}>
              <ListItem disablePadding>
                <ListItemButton
                  onClick={() => handleNavigate(route)}
                  selected={
                    selectedRoute?.current.title === route.title ||
                    selectedRoute?.parent?.title === route.title
                  }
                  classes={{selected: route.onClick ? "" : "!bg-[#FF4319]"}}
                >
                  <ListItemIcon
                    sx={{
                      color: "white",
                      minWidth: "25px",
                      marginRight: "12px",
                      marginLeft: "10px",
                    }}
                  >
                    {route.icon}
                  </ListItemIcon>
                  <ListItemText
                    sx={{
                      color: "white",
                      marginLeft: 0,
                      fontSize: "16px",
                      fontWeight: "400",
                    }}
                    primary={route.title}
                  />
                  {/* TODO: Phase 2 */}
                  {/* {route.title === t("cms.user_management") && (
                    <IconArrowDown
                      className={`transition-transform text-white ${userManagementMenuOpen ? "rotate-0" : "-rotate-90"}`}
                    />
                  )} */}
                  {route.title === t("cms.general_management") && (
                    <IconArrowDown
                      className={`transition-transform text-white ${generalManagementOpen ? "rotate-0" : "-rotate-90"}`}
                    />
                  )}
                  {route.title === t("cms.other_management") && (
                    <IconArrowDown
                      className={`transition-transform text-white ${otherManagementOpen ? "rotate-0" : "-rotate-90"}`}
                    />
                  )}
                </ListItemButton>
              </ListItem>

              {/* TODO: Phase 2 */}
              {/* {route.title === t("cms.user_management") &&
                userManagementMenuOpen &&
                renderSubMenu(userManagementSubMenu, route)} */}
              {route.title === t("cms.general_management") &&
                generalManagementOpen &&
                renderSubMenu(generalManagementSubMenu, route)}
              {route.title === t("cms.other_management") &&
                otherManagementOpen &&
                renderSubMenu(otherManagementSubMenu, route)}
            </React.Fragment>
          ))}
        </List>
      </div>
      <div className="bg-[#FAFBFC] flex-1 flex flex-col w-[calc(100%-280px)]">
        <div className="py-2.5 px-5 bg-white flex justify-between">
          <div className="flex items-center gap-x-3">
            <button onClick={() => setOpen(!open)}>
              <MenuIcon />
            </button>
            <div className="uppercase text-base font-semibold text-[#2D2D2D]">
              {selectedRoute?.current?.title ||
                routesCms.find((r) => r.pathname === location.pathname)
                  ?.title ||
                ""}
            </div>
          </div>
          <div>
            <Button
              id="basic-button"
              aria-controls={anchorEl ? "basic-menu" : undefined}
              aria-haspopup="true"
              aria-expanded={anchorEl ? "true" : undefined}
              onClick={handleMenuClick}
            >
              <div className="flex items-center gap-x-3">
                <Avatar
                  src={
                    userInfo?.avatar
                      ? userInfo.avatar
                      : "/image/default-avatar.png"
                  }
                />
                <div className="text-[#2D2D2D] text-sm font-bold capitalize">
                  {getFullName ?? userInfo?.username ?? "Tina music"}
                </div>
                <IconArrowDown />
              </div>
            </Button>
            <Menu
              id="basic-menu"
              anchorEl={anchorEl}
              open={Boolean(anchorEl)}
              onClose={handleMenuClose}
              MenuListProps={{"aria-labelledby": "basic-button"}}
            >
              <MenuItem
                onClick={() => {
                  handleMenuClose();
                  navigate("/cms/account-personal-info");
                }}
                sx={{
                  "color": "#232A32",
                  "paddingTop": "10px",
                  "paddingBottom": "10px",
                  "paddingLeft": "16px",
                  "paddingRight": "16px",
                  "fontWeight": 400,
                  "&:hover": {
                    bgcolor: "#FF43191A",
                    color: "#FF4319",
                    fontWeight: 500,
                  },
                }}
              >
                <div className="flex items-center gap-2.5">
                  <IconUserCMS />
                  <span className="text-sm">
                    {t("common.personal_information")}
                  </span>
                </div>
              </MenuItem>
              <MenuItem
                onClick={() => {
                  handleMenuClose();
                  setIsModalChangePassword(true);
                }}
                sx={{
                  "color": "#232A32",
                  "paddingTop": "10px",
                  "paddingBottom": "10px",
                  "paddingLeft": "16px",
                  "paddingRight": "16px",
                  "fontWeight": 400,
                  "&:hover": {
                    bgcolor: "#FF43191A",
                    color: "#FF4319",
                    fontWeight: 500,
                  },
                }}
              >
                <div className="flex items-center gap-2.5">
                  <IconLockCMS />
                  <span className="text-sm">{t("common.change_password")}</span>
                </div>
              </MenuItem>
              <MenuItem
                onClick={() => setIsModalLogOut(true)}
                sx={{
                  "color": "#232A32",
                  "paddingTop": "10px",
                  "paddingBottom": "10px",
                  "paddingLeft": "16px",
                  "paddingRight": "16px",
                  "fontWeight": 400,
                  "&:hover": {
                    bgcolor: "#FF43191A",
                    color: "#FF4319",
                    fontWeight: 500,
                  },
                }}
              >
                <div className="flex items-center gap-2.5">
                  <IconLogoutCMS />
                  <span className="text-sm">{t("auth.logout")}</span>
                </div>
              </MenuItem>
            </Menu>
          </div>
        </div>
        {isOnline ? (
          <div className="overflow-y-auto p-5">{children}</div>
        ) : (
          <NoInternet />
        )}
      </div>
      <ModalConfirm
        open={isModalLogout}
        title={t("auth.logout")}
        onConfirm={handleLogout}
        onCancel={() => setIsModalLogOut(false)}
      >
        <div className="flex items-center justify-center bg-[#FFFFFF0D] py-[30px] gap-2.5">
          <IconLogoutCMS stroke="#FF4319" />
          <span className="text-[#262626] text-xl">
            {t("auth.logout_confirm_cms")}
          </span>
        </div>
      </ModalConfirm>
      <ModalChangePassword
        open={isModalChangePassword}
        onClose={() => setIsModalChangePassword(false)}
      />
      <NoInternetSnackbar className="fixed bottom-0 h-[96px] max-[568px]:h-[68px] z-40" />
    </div>
  );
}
