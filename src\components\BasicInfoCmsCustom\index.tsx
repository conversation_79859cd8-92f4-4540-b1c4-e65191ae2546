import {useTranslation} from "react-i18next";

export interface IBasicInfoItem {
  title: string;
  value: JSX.Element;
}

interface IBasicInfoCmsCustom {
  image?: string;
  description?: string;
  biography?: string;
  basicInfoCol1?: IBasicInfoItem[];
  basicInfoCol2?: IBasicInfoItem[];
}

export default function BasicInfoCmsCustom({
  image,
  description,
  biography,
  basicInfoCol1,
  basicInfoCol2,
}: IBasicInfoCmsCustom) {
  const {t} = useTranslation();

  return (
    <div className="flex flex-col gap-5 bg-[#F6F6F6] py-2.5 px-4 border border-[#E5E5E5] rounded-xl w-full">
      {image && (
        <div className="flex flex-col gap-1 sm:flex-row sm:gap-0 md:gap-2 lg:gap-10">
          <span className="text-[#656565] text-sm min-w-40">
            {t("common.cover_img")}
          </span>
          <img
            src={image}
            className="w-20 h-20 aspect-square rounded object-cover"
          />
        </div>
      )}
      <div className="flex justify-between flex-col gap-1.5 md:flex-row">
        <div className="flex flex-col gap-2.5 w-full lg:w-[50%]">
          {basicInfoCol1?.filter(Boolean).map((item, index) => (
            <div
              key={index}
              className="flex flex-col gap-1 sm:flex-row sm:gap-0 md:gap-2 lg:gap-10 w-full"
            >
              <span className="text-[#656565] text-sm min-w-40 flex">
                {item?.title}
              </span>
              <span className="text-sm font-semibold">{item?.value}</span>
            </div>
          ))}
        </div>
        <div className="flex flex-col gap-2.5 w-full lg:w-[50%]">
          {basicInfoCol2?.filter(Boolean).map((item, index) => (
            <div
              key={index}
              className="flex flex-col gap-1 sm:flex-row sm:gap-0 md:gap-2 lg:gap-10"
            >
              <span className="text-[#656565] text-sm min-w-40 flex">
                {item?.title}
              </span>
              <span className="text-sm font-semibold">{item?.value}</span>
            </div>
          ))}
        </div>
      </div>
      {(description || biography) && (
        <>
          <hr />
          <div className="flex flex-col gap-1 sm:flex-row sm:gap-0 md:gap-2 lg:gap-10">
            <span className="text-[#656565] text-sm min-w-40">
              {t("common.describe")}
            </span>
            <span className="text-sm font-semibold">
              {description || biography}
            </span>
          </div>
        </>
      )}
    </div>
  );
}
