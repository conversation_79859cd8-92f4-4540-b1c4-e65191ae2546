import ImageCropper from "@components/ImageCropper";
import {DialogActions} from "@mui/material";
import {ErrorMessage, Field, Formik} from "formik";
import "./index.scss";
import ApiAutofill from "@api/ApiAutofill";
import AutoCompleteAutofill from "@components/AutoCompleteAutofill";
import {useTranslation} from "react-i18next";
import {LocalizationProvider} from "@mui/x-date-pickers/LocalizationProvider/LocalizationProvider";
import {AdapterDayjs} from "@mui/x-date-pickers/AdapterDayjs";
import {DatePicker} from "@mui/x-date-pickers/DatePicker";
import dayjs from "dayjs";
import UploadAudio from "@components/UploadAudio";
import UploadHighQualityAudio from "@components/UploadHighQualityAudio";
import * as Yup from "yup";
import UploadFile from "@components/UploadFile";
import {ISong} from "src/types";
import {useMutation} from "@tanstack/react-query";
import ApiCMSSong, {ICreateSong} from "@api/ApiCMSSong";
import {toast} from "react-toastify";
import GlobalButton from "@components/ButtonGlobal";
import CUModal from "@components/CUModal";
import MetaLanguageSelect from "@pages/Cms/components/MetaLanguageSelect";

interface IModalAddEditSong {
  open: boolean;
  onClose: () => void;
  initValue?: ISong;
  refetch: () => void;
}

export default function ModalAddEditSong({
  open,
  onClose,
  initValue,
  refetch,
}: IModalAddEditSong) {
  const {t} = useTranslation();
  const isEdit = initValue !== undefined;

  const validationSchema = Yup.object({
    image: Yup.mixed()
      .test(
        "required",
        t("validation.field_is_require", {
          field: t("cms.song.image_song"),
        }),
        (file) => {
          if (!initValue?.images?.DEFAULT) {
            return !!file;
          }
          return true;
        },
      )
      .test(
        "size",
        t("validation.file_size_limit_exceed", {size: "10MB"}),
        (file) => {
          if (initValue?.images?.DEFAULT && !file) return true;
          return file && (file as File).size < 10 * 1024 * 1024;
        },
      ),
    name: Yup.string().required(
      t("validation.field_is_require", {
        field: t("cms.song.song_name"),
      }),
    ),
    artistIds: Yup.array().min(
      1,
      t("validation.field_is_require", {
        field: t("cms.artist_song"),
      }),
    ),
    audio: Yup.mixed()
      .test(
        "required",
        t("validation.field_is_require", {
          field: t("cms.song.song_file"),
        }),
        (file) => {
          if (initValue && !file) return true;
          return !!file;
        },
      )
      .test(
        "type",
        t("validation.file_upload_must_be_type", {
          type: "MP3",
        }),
        (file) => {
          if (initValue && !file) return true;
          return file instanceof File && file.type === "audio/mpeg";
        },
      )
      .test(
        "size",
        t("validation.file_size_limit_exceed", {
          size: "15MB",
        }),
        (file) => {
          if (initValue && !file) return true;
          return file && (file as File).size < 15 * 1024 * 1024;
        },
      ),
  });

  const initialValues = {
    image: undefined,
    name: initValue?.name,
    artistIds: initValue?.artists?.map((artist) => ({
      id: artist.id,
      label: artist?.stageName ?? artist?.name,
    })),
    albumId: initValue?.album?.length
      ? {
          label: initValue.album[0].name ?? "",
          id: initValue.album[0].id,
        }
      : undefined,
    genreIds: initValue?.genres?.map((genre) => ({
      id: genre.id,
      label: genre.name,
    })),
    themeIds: initValue?.themes?.map((theme) => ({
      id: theme.id,
      label: theme.name,
    })),
    lrcLyrics: undefined,
    audio: undefined,
    audioHq: undefined,
    textLyrics: initValue?.textLyrics,
    releaseDate: initValue?.releaseDate,
    language: initValue?.language || "en",
  };

  const {mutateAsync: createSongMutationAsync, isPending: isCreating} =
    useMutation({
      mutationFn: ApiCMSSong.createSong,
      onSuccess: () => {
        toast.success(t("common.add_successfully"));
        refetch();
        onClose();
      },
    });

  const {mutateAsync: updateSongMuatationAsync, isPending: isUpdating} =
    useMutation({
      mutationFn: ApiCMSSong.updateSong,
      onSuccess: () => {
        toast.success(t("common.update_successfully"));
        refetch();
        onClose();
      },
    });

  const handleSubmit = async (values: ICreateSong) => {
    const formattedValues = {
      ...values,
      genreIds: values.genreIds?.map((genre) => genre.id),
      themeIds: values.themeIds?.map((theme) => theme.id),
      artistIds: values.artistIds?.map((artist) => artist.id),
      albumId: values.albumId ? values.albumId.id : undefined,
    };

    if (values.audio && typeof values.audio === "string") {
      delete formattedValues.audio;
    }

    if (isEdit) {
      await updateSongMuatationAsync({
        id: initValue.id || "",
        ...(formattedValues as ICreateSong),
      });
    } else {
      await createSongMutationAsync(formattedValues as ICreateSong);
    }
  };

  return (
    <CUModal
      onClose={onClose}
      open={open}
      title={isEdit ? t("cms.edit_song") : t("cms.new_song")}
    >
      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
      >
        {({setFieldValue, values, handleSubmit, handleChange}) => {
          return (
            <div className="flex flex-col">
              <div className="flex flex-col gap-4 p-6 max-h-[600px] overflow-y-auto">
                {/* Ảnh */}
                <div className="flex flex-col gap-2.5">
                  <span className="text-[#000000D9] text-sm font-semibold">
                    {t("common.cover_img")}{" "}
                    <span className="text-red-600"> *</span>
                  </span>
                  <div className="relative">
                    <ImageCropper
                      onChange={(file) => {
                        setFieldValue("image", file);
                      }}
                      initialImageUrl={initValue?.images?.DEFAULT}
                      className="w-[120px] aspect-square object-cover"
                    />
                    <ErrorMessage
                      name="image"
                      component="div"
                      className="text-red-500 text-sm mt-1"
                    />
                  </div>
                </div>
                {/* Tên bài hát */}
                <div className="flex flex-col gap-2.5">
                  <span className="text-[#000000D9] text-sm font-semibold">
                    {t("cms.song.song_name")}
                    <span className="text-red-600"> *</span>
                  </span>
                  <div className="relative">
                    <Field
                      name="name"
                      value={values.name}
                      placeholder={t("cms.song_name")}
                      className="custom-input-info w-full"
                      onChange={handleChange("name")}
                    />
                    <ErrorMessage
                      name="name"
                      component="div"
                      className="text-red-500 text-sm mt-1"
                    />
                  </div>
                </div>
                {/* Nghệ sĩ thể hiện */}
                <div className="flex flex-col gap-2.5">
                  <span className="text-[#000000D9] text-sm font-semibold">
                    {t("cms.artist_song")}
                    <span className="text-red-600"> *</span>
                  </span>
                  <div className="relative">
                    <AutoCompleteAutofill
                      placeHolder={t("cms.artist_song")}
                      name="autofillArtist"
                      suggestionAPI={ApiAutofill.autoSinger}
                      multiple
                      value={values.artistIds}
                      onChange={(val) => setFieldValue("artistIds", val)}
                    />
                    <ErrorMessage
                      name="artistIds"
                      component="div"
                      className="text-red-500 text-sm mt-1"
                    />
                  </div>
                </div>
                {/* Thuộc về album */}
                <div className="flex flex-col gap-2.5">
                  <span className="text-[#000000D9] text-sm font-semibold">
                    {t("cms.song.songs_album")}
                  </span>
                  <AutoCompleteAutofill
                    placeHolder={t("cms.song.songs_album")}
                    name="autofillAlbum"
                    suggestionAPI={ApiAutofill.autoAlbum}
                    extraParams={{
                      artistIds: values?.artistIds?.map((item) => {
                        return item?.id;
                      }),
                    }}
                    value={values.albumId}
                    onChange={(val) => {
                      setFieldValue("albumId", val);
                    }}
                  />
                </div>
                <div className="flex gap-4 justify-between">
                  {/* Chủ đề */}
                  <div className="flex flex-col gap-2.5 w-full">
                    <span className="text-[#000000D9] text-sm font-semibold">
                      {t("cms.song.theme")}
                    </span>
                    <div className="relative">
                      <AutoCompleteAutofill
                        placeHolder={t("cms.song.theme")}
                        name="autofillTheme"
                        suggestionAPI={ApiAutofill.autoTheme}
                        multiple
                        value={values.themeIds}
                        onChange={(val) => setFieldValue("themeIds", val)}
                      />
                    </div>
                  </div>
                  {/* Thể loại */}
                  <div className="flex flex-col gap-2.5 w-full">
                    <span className="text-[#000000D9] text-sm font-semibold">
                      {t("cms.song.genres")}
                    </span>
                    <div className="relative">
                      <AutoCompleteAutofill
                        placeHolder={t("cms.song.genres")}
                        name="autofillGenre"
                        suggestionAPI={ApiAutofill.autoGenre}
                        multiple
                        value={values.genreIds}
                        onChange={(val) => setFieldValue("genreIds", val)}
                      />
                    </div>
                  </div>
                </div>
                <div className="flex gap-4 justify-between">
                  <div className="flex flex-col gap-2.5 w-full">
                    <span className="text-[#000000D9] text-sm font-semibold">
                      {t("cms.language")}
                    </span>
                    <MetaLanguageSelect
                      onChange={(val) => setFieldValue("language", val)}
                      initValue={initValue?.language}
                    />
                  </div>
                  <div className="flex flex-col gap-2.5 w-full">
                    <span className="text-[#000000D9] text-sm font-semibold">
                      {t("cms.song.release_time")}
                    </span>
                    <LocalizationProvider dateAdapter={AdapterDayjs}>
                      <DatePicker
                        name="releaseDate"
                        disableFuture
                        value={
                          values.releaseDate ? dayjs(values.releaseDate) : null
                        }
                        onChange={(value) => {
                          if (value) {
                            setFieldValue(
                              "releaseDate",
                              value.format("YYYY-MM-DD"),
                            );
                          } else {
                            setFieldValue("releaseDate", null);
                          }
                        }}
                        slotProps={{
                          textField: {
                            size: "small",
                            placeholder: t("common.release_date"),
                          },
                          field: {
                            clearable: true,
                          },
                        }}
                      />
                    </LocalizationProvider>
                  </div>
                </div>
                {/* Lời bài hát (text) */}
                <div className="flex flex-col gap-2.5">
                  <span className="text-[#000000D9] text-sm font-semibold">
                    {t("cms.song.lyrics")}
                  </span>
                  <Field
                    as="textarea"
                    name="textLyrics"
                    value={values.textLyrics}
                    onChange={handleChange}
                    placeholder={t("cms.song.lyrics")}
                    className="custom-input-info w-full"
                  />
                </div>
                {/* Lời bài hát (lrc) */}
                <div className="flex flex-col gap-2.5">
                  <span className="text-[#000000D9] text-sm font-semibold">
                    {t("cms.song.lrc_lyrics")}
                  </span>
                  <UploadFile
                    acceptFile=".lrc"
                    value={initValue?.lrcLyrics}
                    onFileUpload={(val) => setFieldValue("lrcLyrics", val)}
                  />
                </div>
                {/* File chất lượng cao, TODO: Add value */}
                <div className="flex flex-col gap-2.5">
                  <span className="text-[#000000D9] text-sm font-semibold">
                    {t("cms.song.song_file_high_quality")}
                  </span>
                  <UploadHighQualityAudio
                    helperText={
                      <span className="text-sm text-[#BFBFBF]">
                        {t("common.you_can_upload_file_type", {
                          file: "MP3",
                        })}
                      </span>
                    }
                    value={initValue?.audios?.[1]?.url}
                    onFileUpload={(val) => setFieldValue("audioHq", val)}
                  />
                </div>
                {/* File âm thanh */}
                <div className="flex flex-col gap-2.5">
                  <span className="text-[#000000D9] text-sm font-semibold">
                    {t("cms.song.song_file")}
                    <span className="text-red-600"> *</span>
                  </span>
                  <div className="relative">
                    <UploadAudio
                      helperText={
                        <span className="text-sm text-[#BFBFBF]">
                          {t("common.you_can_upload_file_type", {
                            file: "MP3",
                          })}
                        </span>
                      }
                      value={initValue?.audios?.[0]?.url}
                      onFileUpload={(val) => setFieldValue("audio", val)}
                    />
                    <ErrorMessage
                      name="audio"
                      component="div"
                      className="text-red-500 text-sm mt-1"
                    />
                  </div>
                </div>
              </div>
              <hr className="bg-[#F0F0F0]" />
              {/* Action */}
              <DialogActions>
                <GlobalButton
                  onClick={onClose}
                  text={t("common.cancel")}
                  color="white"
                  className="w-20"
                  textClassName="text-[#000000D9]"
                  disabled={isCreating || isUpdating}
                />
                <GlobalButton
                  text={t("common.confirm")}
                  className="w-30"
                  onClick={handleSubmit}
                  isLoading={isCreating || isUpdating}
                />
              </DialogActions>
            </div>
          );
        }}
      </Formik>
    </CUModal>
  );
}
