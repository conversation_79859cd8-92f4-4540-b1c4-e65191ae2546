import {useTranslation} from "react-i18next";
import {<PERSON>} from "react-router-dom";
import {useState} from "react";
import IconArrowRight2 from "@components/Icon/IconArrowRight2";
import {Skeleton} from "@mui/material";
import {useQuery} from "@tanstack/react-query";
import QUERY_KEY from "@api/QueryKey";
import ApiTopic from "@api/ApiTopic";

interface IThemesListProps {
  className?: string;
  genreSlug: string;
}

export default function ThemesList({className, genreSlug}: IThemesListProps) {
  const {t} = useTranslation();
  const [isExpanded, setIsExpanded] = useState<boolean>(false);
  const isCountry = genreSlug === "Country" ? true : false;
  const {
    data: themesData,
    isLoading,
    isError,
  } = useQuery({
    queryKey: [QUERY_KEY.GENRE.GET_GENRE, genreSlug],
    queryFn: () => ApiTopic.getGenre(genreSlug),
    // staleTime: 5 * 60 * 1000,
  });
  const displayedData = isExpanded
    ? themesData?.subGenresDto?.slice(0, 8)
    : themesData?.subGenresDto?.slice(0, 4);

  return (
    <section className={`${className} @container`}>
      <h2 className="text-white text-lg md:text-2xl font-semibold mb-4">
        {themesData?.name}
      </h2>
      {isLoading ? (
        <div className="grid grid-cols-2 @lg:grid-cols-4 lg:gap-4 gap-2">
          {Array.from({length: 4}).map((_, index) => (
            <div
              key={index}
              className="relative w-full aspect-[2/1] group rounded-lg hover:bg-[#FFFFFF0F]"
            >
              <div className="relative rounded-lg flex w-full">
                <Skeleton
                  variant="rounded"
                  sx={{
                    aspectRatio: "2 / 1",
                    maxWidth: "100%",
                    width: "100%",
                    bgcolor: "#752121CC",
                    borderRadius: "9px",
                  }}
                >
                  <div className="w-full bg-white aspect-[2/1] rounded-lg" />
                </Skeleton>
              </div>
            </div>
          ))}
        </div>
      ) : !isError && displayedData?.length ? (
        <div className="flex flex-col lg:gap-4 gap-2">
          <div className="grid grid-cols-2 @lg:grid-cols-4 lg:gap-4 gap-2">
            {displayedData?.map((item, index) => (
              <Link
                key={index}
                to={`/topic/${item.urlSlug}`}
                className={`relative overflow-hidden w-full h-full cursor-pointer group ${isCountry ? "rounded-xl" : "rounded-t-xl"}`}
              >
                <img
                  src={
                    item?.images?.DEFAULT ||
                    item?.images?.SMALL ||
                    "/image/default-music.png"
                  }
                  alt={item?.name}
                  className={`object-cover w-full transition-transform duration-700 ease-in-out group-hover:scale-110 ${isCountry ? "aspect-[2]" : "aspect-[1.5]"} `}
                />
                <div
                  className={`absolute inset-0 flex ${isCountry ? "items-center justify-center" : "items-end justify-start bg-gradient-to-t from-[#1a0606] via-transparent to-transparent"} bg-black/50 text-white`}
                >
                  <span
                    className={`text-center mx-5 my-9 block font-black text-base md:text-lg xl:text-2xl truncate`}
                  >
                    {item?.name}
                  </span>
                </div>
              </Link>
            ))}
          </div>
          {themesData?.subGenresDto?.length &&
            themesData?.subGenresDto?.length > 4 && (
              <button
                className="mt-2 inline-flex items-center gap-2 text-[#FF4319] text-xs md:text-sm lg:text-base font-normal transition-all duration-300 mx-auto"
                onClick={() => setIsExpanded((prev) => !prev)}
              >
                {isExpanded ? (
                  <>
                    {t("common.see_less")}
                    <IconArrowRight2 className="-rotate-90" />
                  </>
                ) : (
                  <>
                    {t("common.see_more")}
                    <IconArrowRight2 className="rotate-90" />
                  </>
                )}
              </button>
            )}
        </div>
      ) : (
        <div className="text-white w-full h-[10vh] flex justify-center items-center text-[22px]">
          {t("common.list_is_empty")}
        </div>
      )}
    </section>
  );
}
