import {SVGProps} from "react";

function IconPlaylistDuotone({
  width = "24",
  height = "24",
  ...props
}: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox={`0 0 ${width} ${height}`}
      fill="none"
      {...props}
    >
      <path
        d="M3.75 15.75C3.5375 15.75 3.35941 15.6777 3.21575 15.533C3.07191 15.3885 3 15.2093 3 14.9955C3 14.7818 3.07191 14.6041 3.21575 14.4625C3.35941 14.3208 3.5375 14.25 3.75 14.25H9.9C10.1125 14.25 10.2906 14.3223 10.4345 14.467C10.5781 14.6115 10.65 14.7907 10.65 15.0045C10.65 15.2182 10.5781 15.3959 10.4345 15.5375C10.2906 15.6792 10.1125 15.75 9.9 15.75H3.75ZM3.75 11.625C3.5375 11.625 3.35941 11.5526 3.21575 11.408C3.07191 11.2635 3 11.0843 3 10.8705C3 10.6568 3.07191 10.4791 3.21575 10.3375C3.35941 10.1958 3.5375 10.125 3.75 10.125H14.075C14.2875 10.125 14.4656 10.1974 14.6095 10.342C14.7531 10.4865 14.825 10.6657 14.825 10.8795C14.825 11.0932 14.7531 11.2709 14.6095 11.4125C14.4656 11.5542 14.2875 11.625 14.075 11.625H3.75ZM3.75 7.5C3.5375 7.5 3.35941 7.42765 3.21575 7.283C3.07191 7.1385 3 6.95935 3 6.7455C3 6.53185 3.07191 6.35415 3.21575 6.2125C3.35941 6.07085 3.5375 6 3.75 6H14.075C14.2875 6 14.4656 6.07235 14.6095 6.217C14.7531 6.3615 14.825 6.54065 14.825 6.7545C14.825 6.96815 14.7531 7.14585 14.6095 7.2875C14.4656 7.42915 14.2875 7.5 14.075 7.5H3.75ZM17.15 20.55C17.0833 20.6 17.0167 20.6292 16.95 20.6375C16.8834 20.6459 16.8167 20.6334 16.75 20.6C16.6833 20.5667 16.6334 20.52 16.6 20.46C16.5667 20.4 16.55 20.33 16.55 20.25V13.7C16.55 13.62 16.5667 13.55 16.6 13.49C16.6334 13.43 16.6833 13.3834 16.75 13.35C16.8167 13.3167 16.8834 13.3042 16.95 13.3125C17.0167 13.3208 17.0833 13.35 17.15 13.4L21.6 16.675C21.6555 16.7103 21.6944 16.7544 21.7167 16.8072C21.7389 16.8602 21.75 16.9162 21.75 16.975C21.75 17.0339 21.7389 17.0897 21.7167 17.1427C21.6944 17.1956 21.6555 17.2396 21.6 17.275L17.15 20.55Z"
        fill="white"
      />
    </svg>
  );
}

export default IconPlaylistDuotone;
