# SongItem Component

SongItem component đã được cập nhật để hỗ trợ hai layout khác nhau:
1. **Flex Layout** (mặc định) - Layout hiện tại với flexbox
2. **Table Layout** - Layout mới sử dụng display: table-row và table-cell

## Props mới

- `tableLayout?: boolean` - Bật table layout (mặc định: false)
- `showNumber?: boolean` - Hiển thị số thứ tự (chỉ cho table layout)
- `index?: number` - Chỉ số của item (cho số thứ tự)

## Cách sử dụng

### Flex Layout (mặc định)
```tsx
<SongItem
  song={song}
  handlePlayMusic={() => handlePlay(song)}
  showDuration={true}
  showAlbumInfo={true}
  showReleaseDate={true}
/>
```

### Table Layout
```tsx
<div className="table w-full">
  <div className="table-row-group">
    {songs.map((song, index) => (
      <SongItem
        key={song.id}
        song={song}
        tableLayout={true}
        showNumber={true}
        index={index}
        handlePlayMusic={() => handlePlay(song)}
        showDuration={true}
        showAlbumInfo={true}
        showReleaseDate={true}
      />
    ))}
  </div>
</div>
```

### Sử dụng với SongTableWrapper
```tsx
<SongTableWrapper
  songs={songs}
  onPlaySong={handlePlaySong}
  showNumber={true}
  showDuration={true}
  showAlbumInfo={true}
  showReleaseDate={true}
/>
```

## Table Layout Structure

Khi `tableLayout={true}`, component sẽ render với cấu trúc:

1. **Number Column** (nếu `showNumber={true}`)
   - `table-cell align-middle text-center`
   - Hiển thị số thứ tự

2. **Song Info Column**
   - `table-cell align-middle`
   - Chứa avatar, tên bài hát, nghệ sĩ

3. **Album Column** (nếu `showAlbumInfo={true}`)
   - `hidden lg:table-cell align-middle`
   - Hiển thị thông tin album/playlist

4. **Release Date Column** (nếu `showReleaseDate={true}`)
   - `hidden md:table-cell align-middle`
   - Hiển thị ngày phát hành

5. **Actions Column**
   - `table-cell align-middle text-right`
   - Chứa nút like, menu, duration

## Responsive Behavior

- **Album column**: Ẩn trên màn hình < lg (1024px)
- **Release Date column**: Ẩn trên màn hình < md (768px)
- **Number column**: Luôn hiển thị khi được bật

## CSS Classes

Component sử dụng các CSS classes sau cho table layout:
- `.table-row` - Cho container chính
- `.table-cell` - Cho các column
- `.align-middle` - Căn giữa theo chiều dọc
- Responsive classes: `hidden md:table-cell`, `hidden lg:table-cell`

## Tương thích

- Table layout hoàn toàn tương thích với flex layout
- Tất cả props và functionality hiện tại được giữ nguyên
- Hover effects và interactions hoạt động bình thường
- Responsive design được duy trì
