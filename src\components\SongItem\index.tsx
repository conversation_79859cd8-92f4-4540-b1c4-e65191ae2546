import ApiSong from "@api/ApiSong";
import LikeButton from "@components/AuthButton/LikeButton";
import IconMoreHorizontal from "@components/Icon/IconMoreHorizontal";
import IconPlay from "@components/Icon/IconPlay";
import {IconButton} from "@mui/material";
import {addSongsToQueue} from "@redux/slices/PlayerSlice";
import {IRootState} from "@redux/store";
import {useMutation} from "@tanstack/react-query";
import clsx from "clsx";
import {useMemo, useState} from "react";
import {useDispatch, useSelector} from "react-redux";
import {useNavigate} from "react-router-dom";
import {ESongType, ISong} from "src/types";
import {convertDateSong, convertPlayerTime} from "src/utils/timeUtils";
import "./index.scss";
import PopupMenu from "@components/PopupMenu";
import IconAdd24px from "@components/Icon/IconAdd24px";
import {useTranslation} from "react-i18next";
import ModalAddToPlaylist from "@components/ModalAddToPlaylist";
import {useWindowWidth} from "src/utils/hooks";
import {toast} from "react-toastify";
import IconShare from "@components/Icon/IconShare";
import ModalShare from "@components/ModalShare";
import {generateShareLink} from "src/utils/global";
import PlayerUtil from "src/core/PlayerUtil";
import {handleLikeSong} from "src/utils/like";

interface SongItemProps {
  song: ISong;
  className?: string;
  left?: JSX.Element;
  songLabel?: JSX.Element;
  handlePlayMusic: () => void;
  songClassName?: string;
  showDuration?: boolean;
  showAlbumInfo?: boolean;
  showReleaseDate?: boolean;
  extraMenuActions?: {
    icon?: React.ReactElement;
    label: React.ReactElement | string;
    action?: () => void;
    isAuth?: boolean;
  }[];
}

export default function SongItem({
  song,
  className,
  left,
  songClassName,
  songLabel,
  handlePlayMusic,
  showDuration = true,
  showAlbumInfo = true,
  showReleaseDate = true,
  extraMenuActions,
}: SongItemProps) {
  const {t} = useTranslation();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [openModalShare, setOpenModalShare] = useState<boolean>(false);
  const {currentSong, paused, queueList} = useSelector(
    (state: IRootState) => state?.player,
  );
  const isPlay = currentSong?.id === song?.id;
  const [addToPlaylistOpen, setAddToPlaylistOpen] = useState(false);
  const [menuAnchor, setMenuAnchor] = useState<null | HTMLElement>(null);
  const [isFocused, setIsFocused] = useState(false);
  const width = useWindowWidth();
  const hasPlaylist = (song?.playlists?.length ?? 0) > 0;
  const shareMutate = useMutation({
    mutationFn: ApiSong.shareSong,
    onSuccess: () => {
      const link = generateShareLink({type: "song", data: song});
      navigator.clipboard.writeText(link).then(() => {
        toast.success(t("common.copy_link_success"));
      });
    },
    onError: () => {
      toast.error(t("common.copy_link_failed"));
    },
  });

  const isMobile = useMemo(() => width <= 834, [width]);

  const handleOpenMenu = (event: React.MouseEvent<HTMLButtonElement>) => {
    event.stopPropagation();
    setIsFocused(true);
    setMenuAnchor(event.currentTarget);
  };

  const handleCloseMenu = () => {
    setIsFocused(false);
    setMenuAnchor(null);
  };

  const openAddToPlaylist = () => {
    setAddToPlaylistOpen(true);
  };

  const handleOpenModalShare = () => {
    setOpenModalShare(true);
  };

  const handleCloseModalShare = () => {
    setOpenModalShare(false);
  };

  const handleCopyLink = () => {
    shareMutate.mutateAsync(song?.id ?? "");
  };

  const handleAddSingleSongToQueue = (song: ISong) => {
    if (!song) {
      toast.info(t("common.song_not_found"));
      return;
    }

    const isExisted = queueList?.some((qSong) => qSong?.id === song?.id);

    if (isExisted) {
      toast.info(t("common.song_exist_queue"));
    } else {
      dispatch(addSongsToQueue([song]));
      toast.success(`${t("common.add_to_successfully")}`);
    }
  };

  return (
    <>
      <div
        className={clsx(
          "@container song-item group w-full flex items-center px-[2px] sm:px-2.5 py-2.5 rounded border-b border-[rgba(255,255,255,0.1)] cursor-pointer select-none",
          isFocused && "focused",
          className,
        )}
        onClick={(e) => {
          if (isPlay) {
            if (paused) {
              PlayerUtil.instance.play();
            } else {
              PlayerUtil.instance.pause();
            }
          } else {
            handlePlayMusic();
          }
          if (isMobile) {
            e.stopPropagation();
          }
        }}
        key={song?.id}
      >
        {left}
        <div
          className={clsx(
            "flex items-center gap-4 mr-2.5 w-full max-w-xs sm:max-w-sm md:max-w-md",
            songClassName,
          )}
        >
          <div className="relative avatar-wrapper min-w-10 min-h-10 w-10 h-10">
            <img
              src={
                song?.images?.SMALL ||
                song?.images?.DEFAULT ||
                "/image/default-music.png"
              }
              className="w-full h-full rounded object-cover"
            />
            {isPlay ? (
              <div className="flex items-center justify-center absolute  top-0 left-0 w-10 h-10 rounded bg-[rgba(0,0,0,0.5)]">
                {paused ? (
                  <IconPlay />
                ) : (
                  <img
                    src="/image/animation_play.gif"
                    style={{filter: "grayscale(100%) brightness(0) invert(1)"}}
                  />
                )}
              </div>
            ) : (
              <div className="avatar-mask absolute top-0 left-0 w-10 h-10 rounded bg-[rgba(0,0,0,0.5)]">
                <IconPlay />
              </div>
            )}
          </div>
          <div className="flex flex-col gap-y-1 justify-center w-full max-w-xs sm:max-w-sm xl:max-w-md overflow-hidden">
            <div className="flex items-center gap-x-2.5 text-white">
              <div
                className={`text-sm font-semibold line-clamp-1 ${!isMobile && song.type === ESongType.SONG ? "hover:text-sky-500" : ""}`}
                onClick={(e) => {
                  if (
                    !isMobile &&
                    hasPlaylist &&
                    song.type === ESongType.SONG
                  ) {
                    e.stopPropagation();
                    navigate(`/playlist/${song?.playlists?.[0]?.urlSlug}`);
                  } else {
                    PlayerUtil.instance?.play?.();
                  }
                }}
              >
                {song?.name}
              </div>
              {songLabel}
            </div>
            <div className="text-xs text-[rgba(255,255,255,0.5)] overflow-hidden text-ellipsis whitespace-nowrap w-40 lg:w-44 group-hover:w-36 group-hover:max-md:w-28 max-md:w-32 max-[412px]:w-28">
              {song?.artists && song?.artists?.length > 0 ? (
                song?.artists?.map((artist, index) => (
                  <span key={`song_item_artist_${artist?.id}`}>
                    <span
                      className={clsx(
                        !isMobile &&
                          song.type === ESongType.SONG &&
                          "hover:text-sky-500 hover:underline",
                      )}
                      onClick={(e) => {
                        if (!isMobile && song.type === ESongType.SONG) {
                          e.stopPropagation();
                          navigate(`/artist/${artist?.urlSlug}`);
                        } else {
                          PlayerUtil.instance?.play?.();
                        }
                      }}
                    >
                      {artist?.stageName ?? artist?.name}
                    </span>
                    {index < (song.artists?.length || 0) - 1 ? ", " : ""}
                  </span>
                ))
              ) : (
                <span />
              )}
            </div>
          </div>
        </div>
        <div className="hidden @lg:flex items-center flex-1 justify-end md:justify-between text-[#FFFFFF80] text-sm">
          {showAlbumInfo && (
            <div
              className={clsx(
                "hidden md:block flex-1 w-16 text-ellipsis overflow-hidden whitespace-nowrap",
                !showReleaseDate && "justify-center flex",
              )}
            >
              {song?.playlists && song?.playlists?.length > 0 ? (
                song?.playlists?.map((playlist, index) => (
                  <span key={`song_item_album_${playlist?.urlSlug}`}>
                    <span
                      className={clsx(
                        !isMobile && "hover:text-sky-500 hover:underline",
                      )}
                      onClick={(e) => {
                        if (!isMobile) {
                          e.stopPropagation();
                          navigate(`/playlist/${playlist?.urlSlug}`);
                        } else {
                          PlayerUtil.instance?.play?.();
                        }
                      }}
                    >
                      {playlist?.name}
                    </span>
                    {index < (song.playlists?.length || 0) - 1 && (
                      <span>,&nbsp;</span>
                    )}
                  </span>
                ))
              ) : (
                <span />
              )}
            </div>
          )}
          {showReleaseDate && (
            <div className={clsx("lg:block flex-1")}>
              <span className="line-clamp-1">
                {convertDateSong(song?.releaseDate)}
              </span>
            </div>
          )}
        </div>
        <div className="flex items-center justify-end sm:w-[84px] ml-auto">
          <div
            className="flex justify-between items-center w-fit box-border sm:gap-4"
            onClick={(e) => {
              e.stopPropagation();
            }}
          >
            <LikeButton
              isLiked={song?.isLiked}
              className="song-action hover:rounded-full"
              action={() => handleLikeSong(song)}
              songId={song?.id}
            />
            <IconButton className="song-action" onClick={handleOpenMenu}>
              <IconMoreHorizontal />
            </IconButton>
          </div>
          {showDuration && (
            <div className="song-duration text-[#FFFFFF80] w-[80px] flex items-center justify-end line-clamp-1">
              {convertPlayerTime(song?.duration)}
            </div>
          )}
        </div>
      </div>

      <PopupMenu
        data={song}
        menuArray={[
          {
            icon: <IconAdd24px />,
            label: t("common.menu.add_to_playlist"),
            action: openAddToPlaylist,
            isAuth: true,
          },
          {
            icon: <IconAdd24px />,
            label: t("common.add_to_queue"),
            action: () => handleAddSingleSongToQueue(song),
          },

          ...(extraMenuActions || []),
        ].concat(
          song.type === ESongType.SONG
            ? {
                icon: <IconShare height={22} width={22} />,
                label: t("common.share"),
                action: handleOpenModalShare,
              }
            : [],
        )}
        anchorEl={menuAnchor}
        open={Boolean(menuAnchor)}
        onClose={handleCloseMenu}
      ></PopupMenu>
      <ModalAddToPlaylist
        open={addToPlaylistOpen}
        onClose={() => setAddToPlaylistOpen(false)}
        songData={song}
      />
      <ModalShare
        open={openModalShare}
        onCancel={handleCloseModalShare}
        handleCopyLink={handleCopyLink}
        image={song?.images?.SMALL || song?.images?.DEFAULT}
        name={song?.name}
        artists={song?.artists}
        shareUrl={generateShareLink({type: "song", data: song})}
      />
    </>
  );
}
