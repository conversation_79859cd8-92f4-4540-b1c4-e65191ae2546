import ApiPlaylistDetail from "@api/ApiPlaylistDetail";
import QUERY_KEY from "@api/QueryKey";
import IconCancel from "@components/Icon/IconCancel";
import IOSSwitch from "@components/IOSSwitch";
import {
  <PERSON><PERSON>,
  Dialog,
  DialogContent,
  IconButton,
  TextField,
} from "@mui/material";
import {useMutation, useQueryClient} from "@tanstack/react-query";
import React, {useRef, useState} from "react";
import {useTranslation} from "react-i18next";
import {toast} from "react-toastify";
import {IPlaylist} from "src/types";

interface IPlaylistModalProps {
  data: IPlaylist;
  open: boolean;
  onClose: () => void;
  refetchAlbumData: () => void;
}

export default function ModalUpdateMyPlaylist({
  data,
  open,
  onClose,
  refetchAlbumData,
}: IPlaylistModalProps): JSX.Element {
  const {t} = useTranslation();
  const inputRef = useRef<HTMLInputElement | null>(null);
  const queryClient = useQueryClient();
  const [playlistData, setPlaylistData] = useState({
    name: data?.name || "",
    isPublic: data?.isPublic ?? false,
  });
  const [errorMessage, setErrorMessage] = useState("");

  const updateMyPlaylistMutation = useMutation({
    mutationFn: ApiPlaylistDetail.updateMyPlaylist,
  });

  const handleUpdatePLaylist = async (): Promise<void> => {
    if (!playlistData.name.trim()) {
      setErrorMessage(
        t("validation.field_is_require", {
          field: t("common.name_playlist"),
        }).trim(),
      );
      return;
    }
    updateMyPlaylistMutation.mutate(
      {
        urlSlug: data?.urlSlug || "",
        name: playlistData.name.trim(),
        isPublic: playlistData.isPublic,
      },
      {
        onSuccess: () => {
          queryClient.invalidateQueries({
            queryKey: [QUERY_KEY.PLAYLIST.UPDATE_MY_PLAYLIST],
          });
          toast.success(t("common.update_successfully"));
          refetchAlbumData();
          onClose();
        },
      },
    );
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      sx={{
        "& .MuiDialog-paper": {
          backgroundColor: "#1C1717",
          color: "#E3E3E3",
          width: "444px",
          display: "flex",
          flexDirection: "col",
          justifyContent: "center",
          gap: "4px",
          borderRadius: "12px",
          padding: "24px",
          paddingBottom: "38px",
        },
      }}
    >
      <div className="flex items-center justify-between">
        <span className="p-0 text-base !font-normal text-[#FFFFFF]">
          {t("playlist.rename_playlist")}
        </span>
        <IconButton aria-label="close" onClick={onClose} size="small">
          <IconCancel className="text-white w-5 h-5" />
        </IconButton>
      </div>
      <div className="border-b border-[#FFFFFF0D] my-4" />
      <DialogContent sx={{p: 0}}>
        <div className="flex flex-col gap-10">
          <div className="text-center flex flex-col justify-center gap-[37px] w-full items-center">
            <span className="text-[#E3E3E3] text-xl">
              {t("playlist.rename_your_playlist")}
            </span>

            <TextField
              inputRef={inputRef}
              sx={{
                "width": "100%",
                "fontSize": "28px",
                "fontWeight": "600",

                "& .MuiOutlinedInput-notchedOutline": {
                  border: "none",
                },
                "& input.MuiInputBase-input": {
                  "color": "#FFFFFF82",
                  "fontSize": "28px",
                  "textAlign": "center",
                  "borderBottom": "1px solid #808080",
                  "p": "0px 10px 5px 10px",
                  "::selection": {
                    backgroundColor: "#FF431933",
                  },
                },
              }}
              autoFocus
              onFocus={(e) => {
                e.target.setSelectionRange(0, e.target.value.length);
              }}
              value={playlistData.name}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                setPlaylistData({
                  ...playlistData,
                  name: e.target.value,
                });
                setErrorMessage("");
              }}
              variant="outlined"
            />
            {errorMessage && (
              <div className="text-red-500 text-sm mt-1">{errorMessage}</div>
            )}
          </div>
          <div className="flex flex-col gap-1.5">
            <span className="text-lg text-[#E3E3E3]">
              {t("playlist.public")}
            </span>
            <div className="flex justify-between items-center">
              <span className="text-sm text-[#E3E3E3] opacity-70">
                {t("playlist.des_public_playlist")}
              </span>
              <IOSSwitch
                checked={playlistData.isPublic}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                  setPlaylistData({
                    ...playlistData,
                    isPublic: e.target.checked,
                  });
                }}
              />
            </div>
          </div>
        </div>
        <div className="w-full flex justify-center items-center mt-16">
          <Button
            className="!bg-orange-500 !py-2.5 !px-8 !rounded-[20px] !text-[#FBFDFF]"
            sx={{textTransform: "none"}}
            onClick={handleUpdatePLaylist}
            disabled={updateMyPlaylistMutation.isPending}
            loading={updateMyPlaylistMutation.isPending}
            loadingPosition="end"
          >
            {t("common.confirm")}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
