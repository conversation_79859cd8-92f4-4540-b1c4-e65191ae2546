import {useEffect, useState} from "react";

export const fetchData = <T>(
  data: T[],
  page = 1,
  pageSize = 10,
  delay = 3000,
): Promise<T[]> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const start = (page - 1) * pageSize;
      const end = start + pageSize;

      resolve(data.slice(start, end));
    }, delay);
  });
};

export const useFetchData = <T>(
  fetchFunction: (page: number) => Promise<T[]>,
  pageSize = 10,
  timeout = 5000,
) => {
  const [data, setData] = useState<T[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isError, setIsError] = useState<boolean>(false);
  const [page, setPage] = useState<number>(1);
  const [hasMore, setHasMore] = useState<boolean>(true);

  useEffect(() => {
    let isMounted = true;
    setIsLoading(true);
    setIsError(false);

    const timeoutId = setTimeout(() => {
      if (isMounted) {
        setIsError(true);
        setIsLoading(false);
      }
    }, timeout);

    fetchFunction(page)
      .then((result) => {
        if (isMounted) {
          clearTimeout(timeoutId);
          setData((prev) => [...prev, ...result]);
          setIsLoading(false);
          if (result.length < pageSize) setHasMore(false);
        }
      })
      .catch(() => {
        if (isMounted) {
          clearTimeout(timeoutId);
          setIsError(true);
          setIsLoading(false);
        }
      });

    return () => {
      isMounted = false;
      clearTimeout(timeoutId);
    };
  }, [page]);

  return {data, isLoading, isError, setPage, hasMore};
};

export function useWindowWidth() {
  const [width, setWidth] = useState(window.innerWidth);

  useEffect(() => {
    const handleResize = () => setWidth(window.innerWidth);

    window.addEventListener("resize", handleResize);

    return () => window.removeEventListener("resize", handleResize);
  }, []);

  return width;
}

export function useLayout() {
  const width = useWindowWidth();
  const isMobile = width <= 834;

  return {isMobile};
}

export const useNetworkStatus = () => {
  const [isOnline, setOnline] = useState<boolean>(true);

  const updateNetworkStatus = () => {
    setOnline(navigator.onLine);
  };

  useEffect(() => {
    window.addEventListener("load", updateNetworkStatus);
    window.addEventListener("online", updateNetworkStatus);
    window.addEventListener("offline", updateNetworkStatus);

    return () => {
      window.removeEventListener("load", updateNetworkStatus);
      window.removeEventListener("online", updateNetworkStatus);
      window.removeEventListener("offline", updateNetworkStatus);
    };
  }, [navigator.onLine]);

  return {isOnline};
};
