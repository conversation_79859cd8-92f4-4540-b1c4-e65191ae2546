import {IAccountInfo, IUserInfo} from "src/types";
import {fetcher} from "./Fetcher";

export interface ILoginBody {
  email: string;
  password: string;
  otp?: string;
}

export interface IForgotBody {
  email?: string;
  password?: string;
  otp?: string;
  token?: string;
}

interface ILoginLaoIdBody {
  authorizationCode: string;
}

const path = {
  login: "auth/login",
  meToken: "auth/me-token",
  getMe: "auth/me",
  register: "auth/register",
  confirmRegister: "auth/confirm-register",
  requestOTPForgot: "auth/request-otp-forgot-password",
  verifyOTPForgot: "auth/verify-otp-forgot",
  resetPassword: "auth/forgot-password",
};

function getMe(): Promise<IUserInfo> {
  return fetcher({url: path.getMe, method: "get"}, {displayError: false});
}

function register(body: ILoginBody): Promise<null> {
  return fetcher(
    {url: path.register, method: "post", data: body},
    {
      displayError: false,
    },
  );
}

function confirmRegister(body: ILoginBody): Promise<IAccountInfo> {
  return fetcher(
    {url: path.confirmRegister, method: "post", data: body},
    {
      displayError: false,
    },
  );
}

function requestOTPForgotPassword(body: IForgotBody): Promise<void> {
  return fetcher(
    {url: path.requestOTPForgot, method: "POST", data: body},
    {
      displayError: false,
    },
  );
}

function verifyOTPForgot(body: IForgotBody): Promise<IForgotBody> {
  return fetcher(
    {url: path.verifyOTPForgot, method: "POST", data: body},
    {displayError: false},
  );
}

function resetPassword(body: IForgotBody): Promise<void> {
  return fetcher(
    {url: path.resetPassword, method: "POST", data: body},
    {displayError: false},
  );
}

function login(data: ILoginLaoIdBody) {
  return fetcher<IAccountInfo>({url: path.login, data, method: "post"});
}

function getMeToken(accessToken: string) {
  return fetcher(
    {
      url: "/auth/me-token",
      method: "POST",
      data: {accessToken},
    },
    {
      displayError: true,
    },
  );
}

export default {
  login,
  getMe,
  register,
  confirmRegister,
  requestOTPForgotPassword,
  verifyOTPForgot,
  resetPassword,
  getMeToken,
};
