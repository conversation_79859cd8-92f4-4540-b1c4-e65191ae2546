import {
  MenuItem,
  OutlinedInput,
  Select,
  SelectChangeEvent,
  SelectProps,
} from "@mui/material";
import clsx from "clsx";
import {useState} from "react";

interface ISelectNotFillProps {
  selectProps?: SelectProps<string>;
  onChange: (selected: string) => void;
  values: Array<{name: string; id: string}>;
  initValue?: string;
  placeholder?: string;
}

const CommonSelect = (props: ISelectNotFillProps) => {
  const [selected, setSelected] = useState<string>(props.initValue || "");
  return (
    <Select
      {...props.selectProps}
      displayEmpty
      value={selected}
      onChange={(event: SelectChangeEvent<string>) => {
        setSelected(event.target.value);
        props.onChange(event.target.value);
      }}
      input={<OutlinedInput />}
      inputProps={{"aria-label": "Without label"}}
      renderValue={(selected) => {
        if (selected !== "") {
          return props.values.find((item) => item.id === selected)?.name;
        } else {
          return (
            <span className={clsx("text-[#c3c3c3] text-sm")}>
              {props.placeholder || "-"}
            </span>
          );
        }
      }}
      MenuProps={{
        PaperProps: {
          style: {
            maxHeight: 250,
          },
          sx: {
            "marginTop": "4px",
            "borderRadius": "8px",
            "& .MuiMenuItem-root": {
              fontSize: "14px",
            },
            "& .MuiMenuItem-root.Mui-selected:not(:first-child)": {
              backgroundColor: "#FFF4F2",
              fontSize: "14px",
              fontWeight: "bold",
            },
            "& .MuiMenuItem-root.Mui-selected:first-child": {
              backgroundColor: "#FFF",
              fontSize: "14px",
            },
          },
        },
      }}
    >
      {props.values.map((item) => (
        <MenuItem key={item.id} value={item.id}>
          {item.name}
        </MenuItem>
      ))}
    </Select>
  );
};

export default CommonSelect;
