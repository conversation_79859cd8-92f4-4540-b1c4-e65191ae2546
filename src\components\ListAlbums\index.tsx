import AlbumCardSkeleton from "@components/AlbumCardSkeleton";
import CommonAlbumCard from "@components/CommonAlbumCard";
import IconArrowRight2 from "@components/Icon/IconArrowRight2";
import {useState, useMemo} from "react";
import {useTranslation} from "react-i18next";
import {IPlaylist} from "src/types";
import {Grid} from "@mui/material";
import {useWindowWidth} from "src/utils/hooks";

interface IListAlbumCardProps {
  className?: string;
  data?: IPlaylist[];
  isLoading?: boolean;
  title?: string;
}

export default function ListAlbums({
  className,
  data,
  isLoading,
  title,
}: IListAlbumCardProps) {
  const {t} = useTranslation();
  const [isExpanded, setIsExpanded] = useState<boolean>(false);
  const windowWidth = useWindowWidth();

  const itemsPerRow = useMemo(() => {
    if (windowWidth < 600) return 2;
    if (windowWidth < 900) return 3;
    if (windowWidth < 1200) return 4;
    return 5;
  }, [windowWidth]);

  const displayedData = useMemo(() => {
    return isExpanded ? data : data?.slice(0, itemsPerRow);
  }, [isExpanded, data, itemsPerRow, windowWidth]);

  return (
    <div className={`${className} @container`}>
      <div className="flex justify-between items-start flex-col gap-2 sm:flex-row sm:items-center mb-6 mt-3">
        <h2 className="text-xl md:text-2xl font-semibold text-white">
          {title}
        </h2>
      </div>
      {isLoading ? (
        <Grid
          container
          spacing={{xs: 2, md: 3}}
          columns={{xs: 2, sm: 6, md: 12, lg: 15}}
        >
          {[...Array(itemsPerRow)].map((_, index) => (
            <Grid item xs={1} sm={2} md={3} lg={3} key={index}>
              <AlbumCardSkeleton />
            </Grid>
          ))}
        </Grid>
      ) : displayedData?.length ? (
        <>
          <Grid
            container
            spacing={{xs: 2, md: 3}}
            columns={{xs: 2, sm: 6, md: 12, lg: 15}}
          >
            {displayedData?.map((item, index) => (
              <Grid item xs={1} sm={2} md={3} lg={3} key={index}>
                <CommonAlbumCard data={item} />
              </Grid>
            ))}
          </Grid>
          {data?.length && data?.length > itemsPerRow && (
            <button
              className="w-full justify-center mt-2 inline-flex items-center gap-2 text-[#FF4319] text-xs md:text-sm lg:text-base font-normal transition-all duration-300 mx-auto"
              onClick={() => setIsExpanded((prev) => !prev)}
            >
              {isExpanded ? (
                <>
                  {t("common.see_less")}
                  <IconArrowRight2 className="-rotate-90" />
                </>
              ) : (
                <>
                  {t("common.see_more")}
                  <IconArrowRight2 className="rotate-90" />
                </>
              )}
            </button>
          )}
        </>
      ) : (
        <div className="text-white w-full h-[10vh] flex justify-center items-center text-xl">
          {t("common.list_is_empty")}
        </div>
      )}
    </div>
  );
}
