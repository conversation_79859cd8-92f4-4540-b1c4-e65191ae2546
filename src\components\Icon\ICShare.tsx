import {SVGProps} from "react";

export default function ICShare({
  width = 24,
  height = 24,
  ...props
}: SVGProps<SVGSVGElement>): JSX.Element {
  return (
    <svg
      width={width}
      height={width}
      viewBox={`0 0 ${width} ${height}`}
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M10.501 13.5721C10.9304 14.1462 11.4783 14.6213 12.1075 14.965C12.7367 15.3088 13.4325 15.5132 14.1476 15.5644C14.8628 15.6157 15.5806 15.5125 16.2523 15.2619C16.9241 15.0113 17.5341 14.6192 18.041 14.1121L21.041 11.1121C21.9518 10.1691 22.4557 8.90609 22.4443 7.5951C22.433 6.28412 21.9071 5.03006 20.9801 4.10302C20.053 3.17598 18.799 2.65013 17.488 2.63874C16.177 2.62735 14.914 3.13132 13.971 4.04211L12.251 5.75211"
        stroke="white"
        strokeWidth="1.8"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M14.4992 11.5718C14.0698 10.9976 13.5219 10.5226 12.8927 10.1788C12.2635 9.83506 11.5677 9.63064 10.8526 9.57942C10.1374 9.52821 9.41964 9.63139 8.74788 9.88198C8.07612 10.1326 7.46611 10.5247 6.95922 11.0318L3.95922 14.0318C3.04843 14.9748 2.54446 16.2378 2.55585 17.5488C2.56725 18.8597 3.09309 20.1138 4.02013 21.0409C4.94717 21.9679 6.20123 22.4937 7.51222 22.5051C8.8232 22.5165 10.0862 22.0125 11.0292 21.1018L12.7392 19.3918"
        stroke="white"
        strokeWidth="1.8"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}
