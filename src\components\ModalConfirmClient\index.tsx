import IconCancel from "@components/Icon/IconCancel";
import {Dialog, DialogContent, DialogTitle, IconButton} from "@mui/material";
import React from "react";
import {useTranslation} from "react-i18next";

interface ConfirmModalProps {
  open: boolean;
  title?: string;
  children?: React.ReactNode;
  onConfirm: () => void;
  onClose: () => void;
  loading?: boolean;
}

export default function ModalConfirmClient({
  open,
  title,
  children,
  onConfirm,
  onClose,
  loading,
}: ConfirmModalProps) {
  const {t} = useTranslation();
  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      classes={{paper: "rounded-lg"}}
      sx={{
        "& .MuiDialog-paper": {
          backgroundColor: "#1C1717",
          borderRadius: "12px",
          padding: "24px",
          paddingBottom: "38px",
        },
      }}
    >
      <div className="flex items-center justify-between pr-4">
        <DialogTitle className="!p-0 text-[#FFFFFF]">
          {title ?? t("common.confirm")}
        </DialogTitle>
        <IconButton aria-label="close" onClick={onClose} size="small">
          <IconCancel className="text-[#FFFFFF]" />
        </IconButton>
      </div>
      <div className="border-b border-[#FFFFFF0D] my-4" />

      <DialogContent className="!p-0 text-[#FFFFFF] !pb-16">
        {children}
      </DialogContent>

      <div className="flex justify-end space-x-2 px-4">
        <div
          onClick={onClose}
          className="rounded-lg cursor-pointer select-none px-4 py-2 text-base text-[#FBFDFF]"
        >
          {t("common.cancel")}
        </div>
        <IconButton
          onClick={onConfirm}
          loading={loading}
          disabled={loading}
          className={`!rounded-[20px] !px-4 !text-base !text-white w-[102px]
            ${loading ? "!bg-gray-400 cursor-not-allowed" : "!bg-orange-500 hover:bg-red-600"}
          `}
        >
          {!loading && t("common.confirm")}
        </IconButton>
      </div>
    </Dialog>
  );
}
