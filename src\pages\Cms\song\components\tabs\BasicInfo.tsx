import BasicInfoCmsCustom from "@components/BasicInfoCmsCustom";
import {Avatar, AvatarGroup} from "@mui/material";
import {useTranslation} from "react-i18next";
import {
  EArtistType,
  EThemeAndGenreType,
  IArtist,
  ISong,
  PlaylistType,
} from "src/types";
import {convertDate, convertNumber} from "src/utils/timeUtils";

export default function BasicInfo({songData}: {songData: ISong | undefined}) {
  const {t} = useTranslation();

  const singerData = songData?.artists?.filter((item) =>
    [EArtistType.SINGER, EArtistType.SINGER_MUSICIAN].includes(item.type),
  );

  const musicianData = songData?.artists?.filter(
    (item) => item.type === EArtistType.MUSICIAN,
  );

  const albumData = songData?.playlists?.filter(
    (item) => item.type === PlaylistType.ALBUM,
  );

  const themeData = songData?.genres?.filter(
    (item) => item?.type === EThemeAndGenreType.THEME,
  );

  const genresData = songData?.genres?.filter(
    (item) => item?.type === EThemeAndGenreType.GENRE,
  );

  const basicInfoCol1 = [
    {
      title: t("cms.song.song_name"),
      value: (
        <span className="text-sm font-semibold text-gray-default">
          {songData?.name}
        </span>
      ),
    },
    {
      title: t("cms.song.song_singer"),
      value: (
        <div className="w-full h-full flex justify-start items-center space-x-1">
          <AvatarGroup
            max={3}
            sx={{
              "& .MuiAvatar-root": {
                width: 24,
                height: 24,
                fontSize: 12,
              },
            }}
          >
            {singerData?.map((artist: IArtist, index: number) => (
              <Avatar
                key={`avatar_${index}`}
                src={
                  artist?.images?.SMALL ||
                  artist.images?.DEFAULT ||
                  "/image/default-avatar.png"
                }
              />
            ))}
          </AvatarGroup>
          <span className="font-semibold text-sm text-[#242728]">
            {singerData
              ?.slice(0, 2)
              .map((artist: IArtist) => artist?.stageName ?? artist?.name)
              .join(", ")}
            {(singerData?.length ?? 0) > 2 && ` & ${t("common.lots_artist")}`}
          </span>
        </div>
      ),
    },
    {
      title: t("cms.song.song_musician"),
      value: (
        <div className="w-full h-full flex justify-start items-center space-x-1">
          <AvatarGroup
            max={3}
            sx={{
              "& .MuiAvatar-root": {
                width: 24,
                height: 24,
                fontSize: 12,
              },
            }}
          >
            {musicianData?.map((artist: IArtist, index: number) => (
              <Avatar
                key={`avatar_${index}`}
                src={
                  artist?.images?.SMALL ||
                  artist.images?.DEFAULT ||
                  "/image/default-avatar.png"
                }
              />
            ))}
          </AvatarGroup>
          <span className="font-semibold text-sm text-[#242728]">
            {musicianData
              ?.slice(0, 2)
              .map((artist: IArtist) => artist?.stageName ?? artist?.name)
              .join(", ")}
            {(musicianData?.length ?? 0) > 2 && ` & ${t("common.lots_artist")}`}
          </span>
        </div>
      ),
    },
    {
      title: t("cms.song.songs_album"),
      value: (
        <span className="text-sm font-semibold text-gray-default">
          {albumData?.map((item) => (
            <span key={item?.id}>
              {item?.name}
              {albumData?.length > 1 &&
              albumData?.indexOf(item) < albumData?.length - 1
                ? ", "
                : ""}
            </span>
          ))}
        </span>
      ),
    },
    {
      title: t("cms.song.theme"),
      value: (
        <span className="text-sm font-semibold text-gray-default">
          {themeData?.map((item) => (
            <span key={item?.id}>
              {item?.name}
              {themeData?.length > 1 &&
              themeData?.indexOf(item) < themeData?.length - 1
                ? ", "
                : ""}
            </span>
          ))}
        </span>
      ),
    },
    {
      title: t("cms.song.genres"),
      value: (
        <span className="text-sm font-semibold text-gray-default">
          {genresData?.map((item) => (
            <span key={item?.id}>
              {item?.name}
              {genresData?.length > 1 &&
              genresData?.indexOf(item) < genresData?.length - 1
                ? ", "
                : ""}
            </span>
          ))}
        </span>
      ),
    },
  ];

  const basicInfoCol2 = [
    {
      title: t("cms.song.release_time"),
      value: (
        <span className="text-sm whitespace-nowrap text-left font-semibold text-gray-default">
          {convertDate(songData?.releaseDate)}
        </span>
      ),
    },
    {
      title: t("cms.song.update_time"),
      value: (
        <span className="text-sm whitespace-nowrap text-left font-semibold text-gray-default">
          {convertDate(songData?.updatedAt)}
        </span>
      ),
    },
    {
      title: t("cms.song.favourite_count"),
      value: (
        <span className="text-sm whitespace-nowrap text-left font-semibold text-gray-default">
          {convertNumber(songData?.totalLikes)}
        </span>
      ),
    },
    {
      title: t("cms.song.share_count"),
      value: (
        <span className="text-sm whitespace-nowrap text-left font-semibold text-gray-default">
          {convertNumber(songData?.totalShares)}
        </span>
      ),
    },
    {
      title: t("cms.song.add_to_playlist_collection"),
      value: (
        <span className="text-sm whitespace-nowrap text-left font-semibold text-gray-default">
          {convertNumber(songData?.totalAddedToPlaylists)}
        </span>
      ),
    },
  ];

  return (
    <div className="max-h-[50vh] overflow-y-auto">
      <BasicInfoCmsCustom
        basicInfoCol1={basicInfoCol1}
        basicInfoCol2={basicInfoCol2}
      />
    </div>
  );
}
