import {PayloadAction, createSlice} from "@reduxjs/toolkit";
import Config from "@config";

interface ILanguageState {
  language: string;
}

const initialState: ILanguageState = {
  language: Config.LANGUAGE.DEFAULT,
};

const LanguageSlice = createSlice({
  name: "language",
  initialState,
  reducers: {
    setLanguage: (state, action: PayloadAction<string>) => {
      state.language = action.payload;
    },
  },
});

export const {setLanguage} = LanguageSlice.actions;

export default LanguageSlice.reducer;
