import {
  I<PERSON>rt<PERSON>,
  IParamsDefault,
  IPlaylist,
  ISong,
  IThemeAndGenre,
  PlaylistType,
} from "src/types";
import {fetcher, fetcherWithMetadata, IDataWithMeta} from "./Fetcher";

const path = {
  getTopGenres: "/genres/top-genres-playlists",
  getGenres: "/genres",
};

export interface IGetTopGenresParam {
  page: number;
  pageSize: number;
  playlistType: PlaylistType;
}

function getTopGenres(
  params: IGetTopGenresParam,
): Promise<IDataWithMeta<IThemeAndGenre[]>> {
  return fetcherWithMetadata(
    {
      url: path.getTopGenres,
      method: "get",
      params,
    },
    {
      displayError: false,
      withMetadata: true,
    },
  );
}

function getThemePlaylists(
  urlSlug: string,
  params: IParamsDefault,
): Promise<IDataWithMeta<IPlaylist[]>> {
  return fetcherWithMetadata<IPlaylist[]>(
    {
      url: `${path.getGenres}/${urlSlug}/playlists`,
      method: "get",
      params,
    },
    {
      displayError: false,
      withMetadata: true,
    },
  );
}

function getThemeSongs(
  urlSlug: string,
  params: IParamsDefault,
): Promise<IDataWithMeta<ISong[]>> {
  return fetcherWithMetadata<ISong[]>(
    {
      url: `${path.getGenres}/${urlSlug}/songs`,
      method: "get",
      params,
    },
    {
      displayError: false,
      withMetadata: true,
    },
  );
}
function getThemeArtists(
  urlSlug: string,
  params: IParamsDefault,
): Promise<IDataWithMeta<IArtist[]>> {
  return fetcherWithMetadata<IArtist[]>({
    url: `${path.getGenres}/${urlSlug}/artists`,
    method: "get",
    params,
  });
}

function getGenre(urlSlug: string): Promise<IThemeAndGenre> {
  return fetcher({
    url: `${path.getGenres}/${urlSlug}`,
    method: "get",
  });
}

export default {
  getTopGenres,
  getGenre,
  getThemePlaylists,
  getThemeArtists,
  getThemeSongs,
};
