import BasicInfoCmsCustom, {
  IBasicInfoItem,
} from "@components/BasicInfoCmsCustom";
import {useTranslation} from "react-i18next";
import {EThemeAndGenreType, IThemeAndGenre} from "src/types";
import {convertDate, convertNumber} from "src/utils/timeUtils";

interface BasicInfoGenresProps {
  genresData?: IThemeAndGenre;
}

export default function BasicInfo({genresData}: BasicInfoGenresProps) {
  const {t} = useTranslation();

  const basicInfoCol1 = [
    {
      title:
        genresData?.type === EThemeAndGenreType.THEME
          ? t("cms.theme_and_genre.theme_name", {
              language: t("common.language.vietnamese"),
            })
          : t("cms.theme_and_genre.genre_name", {
              language: t("common.language.vietnamese"),
            }),
      value:
        genresData?.type === EThemeAndGenreType.THEME ? (
          <span className="text-sm font-semibold text-gray-default">
            {genresData?.name}
          </span>
        ) : (
          <span className="text-sm font-normal text-[#393939] border border-solid border-[#DCDCDC] rounded-lg px-2 py-0.5 bg-[#F2F2F3]">
            {genresData?.name}
          </span>
        ),
    },
    genresData?.type === EThemeAndGenreType.THEME
      ? {
          title: t("cms.theme_and_genre.topic_parent"),
          value: (
            <span className="text-sm line-clamp-3 text-left font-semibold text-gray-default">
              {genresData?.parentGenreDto?.name || "-"}
            </span>
          ),
        }
      : undefined,
    {
      title: t("cms.theme_and_genre.total_songs"),
      value: (
        <span className="text-sm whitespace-nowrap text-left font-semibold text-gray-default">
          {convertNumber(genresData?.totalSong)}
        </span>
      ),
    },
    {
      title: t("cms.theme_and_genre.total_albums"),
      value: (
        <span className="text-sm whitespace-nowrap text-left font-semibold text-gray-default">
          {convertNumber(genresData?.totalPlaylist)}
        </span>
      ),
    },
  ].filter(Boolean) as IBasicInfoItem[];

  const basicInfoCol2 = [
    {
      title: t("common.listen_count"),
      value: (
        <span className="text-sm whitespace-nowrap text-left font-semibold text-gray-default">
          {convertNumber(genresData?.totalListen)}
        </span>
      ),
    },
    {
      title: t("cms.song.update_time"),
      value: (
        <span className="text-sm whitespace-nowrap text-left font-semibold text-gray-default">
          {convertDate(genresData?.updatedAt)}
        </span>
      ),
    },
    {
      title: t("cms.theme_and_genre.created_date"),
      value: (
        <span className="text-sm whitespace-nowrap text-left font-semibold text-gray-default">
          {convertDate(genresData?.createdAt)}
        </span>
      ),
    },
  ];

  return (
    <div className="max-h-[50vh] overflow-y-auto">
      <BasicInfoCmsCustom
        image={
          genresData?.images?.SMALL ||
          genresData?.images?.DEFAULT ||
          "/image/default-music.png"
        }
        basicInfoCol1={basicInfoCol1}
        basicInfoCol2={basicInfoCol2}
        description={genresData?.description || "-"}
      />
    </div>
  );
}
