import {IMusicPackage} from "src/types";
import {fetcher} from "./Fetcher";
import config from "src/config";

const path = {
  urlRegister: "/premium/webview",
  musicPackages: "/music-packages",
  checkRegister: "/premium/access",
};

const getUrlRegister = (): Promise<{data: {url?: string}}> => {
  return fetcher(
    {
      url: `${path.urlRegister}?subServiceCode=${config.SERVICE_CODE.subserviceCode.laoMusicPremium}`,
      method: "post",
    },
    {
      displayError: true,
    },
  );
};

const getMusicPackages = (): Promise<IMusicPackage[]> => {
  return fetcher(
    {
      url: path.musicPackages,
      method: "get",
    },
    {
      displayError: false,
    },
  );
};

const checkRegister = (): Promise<{allow: boolean; webviewUrl: string}> => {
  return fetcher(
    {
      url: `${path.checkRegister}?subServiceCode=${config.SERVICE_CODE.subserviceCode.laoMusicPremium}`,
      method: "get",
    },
    {
      displayError: false,
    },
  );
};

export default {
  getUrlRegister,
  getMusicPackages,
  checkRegister,
};
