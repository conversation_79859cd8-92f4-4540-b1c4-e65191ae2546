import {useState} from "react";
import {useMutation} from "@tanstack/react-query";
import {
  TextField,
  IconButton,
  InputAdornment,
  Dialog,
  DialogTitle,
  DialogContent,
} from "@mui/material";
import {VisibilityOffOutlined, VisibilityOutlined} from "@mui/icons-material";
import {useFormik} from "formik";
import * as Yup from "yup";
import ApiUser from "@api/ApiUser";
import {toast} from "react-toastify";
import {useTranslation} from "react-i18next";
import CloseIcon from "@mui/icons-material/Close";

interface ChangePasswordModalProps {
  open: boolean;
  onClose: () => void;
}

export default function ModalChangePassword({
  open,
  onClose,
}: ChangePasswordModalProps) {
  const {t} = useTranslation();
  const [showOldPassword, setShowOldPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const validationSchema = Yup.object({
    oldPassword: Yup.string().required(
      t("validation.field_is_require", {field: t("common.old_password")}),
    ),
    newPassword: Yup.string()
      .required(
        t("validation.field_is_require", {field: t("common.new_password")}),
      )
      .min(8, t("validation.password_min_length"))
      .max(20, t("validation.password_max_length"))
      .matches(/[a-z]/, t("validation.password_lowercase"))
      .matches(/[A-Z]/, t("validation.password_uppercase"))
      .matches(/\d/, t("validation.password_digit"))
      .matches(/[^a-zA-Z0-9\s]/, t("validation.password_special_character")),
    confirmPassword: Yup.string()
      .required(
        t("validation.field_is_require", {field: t("common.retype_password")}),
      )
      .oneOf([Yup.ref("newPassword")], t("validation.password_mismatch")),
  });

  const formik = useFormik({
    initialValues: {
      oldPassword: "",
      newPassword: "",
      confirmPassword: "",
    },
    validationSchema,
    onSubmit: (values) => {
      mutation.mutate(values);
    },
  });

  const mutation = useMutation({
    mutationFn: ApiUser.changePasswordUser,
    onSuccess: () => {
      toast.success(t("common.password_changed_successful"));
      formik.resetForm();
      onClose();
    },
    onError: () => {
      toast.error(t("common.password_change_fail"));
    },
  });

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      classes={{paper: "rounded-lg"}}
    >
      <div className="flex items-center justify-between border-b border-gray-300 pr-4">
        <DialogTitle className="p-0 text-base font-bold text-[#000000D9]">
          {t("common.change_password")}
        </DialogTitle>
        <IconButton
          aria-label="close"
          onClick={onClose}
          className="text-gray-500 hover:text-[#000000D9]"
          size="small"
        >
          <CloseIcon />
        </IconButton>
      </div>

      <DialogContent>
        <form onSubmit={formik.handleSubmit}>
          <div className="flex flex-col gap-2 pb-4">
            <span className="text-[#000000D9] text-sm font-semibold">
              {t("common.old_password")}
            </span>
            <TextField
              type={showOldPassword ? "text" : "password"}
              placeholder={t("common.old_password")}
              fullWidth
              name="oldPassword"
              value={formik.values.oldPassword}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              error={
                formik.touched.oldPassword && Boolean(formik.errors.oldPassword)
              }
              helperText={
                formik.touched.oldPassword && formik.errors.oldPassword
              }
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      onClick={() => setShowOldPassword(!showOldPassword)}
                    >
                      {showOldPassword ? (
                        <VisibilityOffOutlined />
                      ) : (
                        <VisibilityOutlined />
                      )}
                    </IconButton>
                  </InputAdornment>
                ),
              }}
            />
          </div>

          <div className="flex flex-col gap-2 pb-4">
            <span className="text-[#000000D9] text-sm font-semibold">
              {t("common.new_password")}
            </span>
            <TextField
              type={showNewPassword ? "text" : "password"}
              placeholder={t("common.new_password")}
              fullWidth
              name="newPassword"
              value={formik.values.newPassword}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              error={
                formik.touched.newPassword && Boolean(formik.errors.newPassword)
              }
              helperText={
                formik.touched.newPassword && formik.errors.newPassword
              }
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      onClick={() => setShowNewPassword(!showNewPassword)}
                    >
                      {showNewPassword ? (
                        <VisibilityOffOutlined />
                      ) : (
                        <VisibilityOutlined />
                      )}
                    </IconButton>
                  </InputAdornment>
                ),
              }}
            />
          </div>

          <div className="flex flex-col gap-2 pb-4">
            <span className="text-[#000000D9] text-sm font-semibold">
              {t("common.retype_password")}
            </span>
            <TextField
              type={showConfirmPassword ? "text" : "password"}
              placeholder={t("common.retype_password")}
              fullWidth
              name="confirmPassword"
              value={formik.values.confirmPassword}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              error={
                formik.touched.confirmPassword &&
                Boolean(formik.errors.confirmPassword)
              }
              helperText={
                formik.touched.confirmPassword && formik.errors.confirmPassword
              }
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      onClick={() =>
                        setShowConfirmPassword(!showConfirmPassword)
                      }
                    >
                      {showConfirmPassword ? (
                        <VisibilityOffOutlined />
                      ) : (
                        <VisibilityOutlined />
                      )}
                    </IconButton>
                  </InputAdornment>
                ),
              }}
            />
          </div>
          <div className="flex justify-end space-x-2 border-t border-gray-300 py-2">
            <div
              onClick={onClose}
              className="rounded-lg cursor-pointer select-none px-4 py-2 text-base text-gray-600 hover:bg-gray-300"
            >
              {t("common.cancel")}
            </div>
            <IconButton
              type="submit"
              loading={mutation.isPending}
              disabled={mutation.isPending}
              className={`!rounded-lg !px-4 !text-base !text-white w-[102px]
                        ${mutation.isPending ? "!bg-gray-400 cursor-not-allowed" : "!bg-orange-500 hover:bg-red-600"}
                      `}
            >
              {t("common.confirm")}
            </IconButton>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
