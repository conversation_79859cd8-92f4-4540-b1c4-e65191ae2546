import IconGridFour from "@components/Icon/IconGridFour.tsx";
import IconHomeBold from "@components/Icon/IconHomeBold.tsx";
import IconStreamline from "@components/Icon/IconStreamline.tsx";
import IconViewList from "@components/Icon/IconViewList.tsx";
import {Box, Collapse, Drawer, List} from "@mui/material";
import {IRootState} from "@redux/store.ts";
import clsx from "clsx";
import {useEffect, useState} from "react";
import {useTranslation} from "react-i18next";
import {useDispatch, useSelector} from "react-redux";
import {useNavigate} from "react-router-dom";
import {useNetworkStatus, useWindowWidth} from "src/utils/hooks.ts";
import BoxSuggestLogin from "./BoxSuggestLogin.tsx";
import HomeRouteItem from "./HomeRouteItem.tsx";
import "./index.scss";
import {IHomeRoute} from "./types.tsx";
import ModalConfirm from "@components/ModalConfirm/index.tsx";
import {logoutUser} from "@redux/slices/UserSlice.ts";
import IconSetting from "@components/Icon/IconSetting.tsx";
import ModalSettingGeneral from "../ModalSettingGeneral/index.tsx";
import AccountCircleIcon from "@mui/icons-material/AccountCircle";
import {useFullName} from "src/utils/global.ts";
import IconUpgrade from "@components/Icon/IconUpgrade.tsx";
import ICRingbackTone from "@components/Icon/ICRingTone.tsx";
import ModalRingbackTone from "@components/ModalRingbackTone/index.tsx";
import AuthButtonWrapper from "@components/AuthButton/AuthButtonWrapper.tsx";

interface HomeSidebarProps {
  className?: string;
}

interface HomeSidebarProps {
  isDrawerOpen: boolean;
  toggleDrawer: (value: boolean) => void;
  className?: string;
}

export default function HomeSidebar({
  className,
  isDrawerOpen,
  toggleDrawer,
}: HomeSidebarProps) {
  const {t} = useTranslation();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const getFullName = useFullName();
  const {isOnline} = useNetworkStatus();
  const width = useWindowWidth();
  const {currentSong, isOpenLyrics} = useSelector(
    (state: IRootState) => state.player,
  );
  const {accessToken, userInfo} = useSelector(
    (state: IRootState) => state.user,
  );
  const [isOpenLibrary, setIsOpenLibrary] = useState(false);
  const [isOpenProfile, setIsOpenProfile] = useState(false);
  const [isModalLogout, setIsModalLogOut] = useState(false);
  const [isModalSettingOpen, setIsModalSettingOpen] = useState(false);
  const [isModalRegisterRingbackTone, setIsModalRegisterRingbackTone] =
    useState(false);

  const firstRoutes: IHomeRoute[] = [
    {
      title: t("common.home"),
      pathname: "/",
      icon: <IconHomeBold />,
      isPrivate: false,
    },
    {
      title: t("common.home_sidebar.rankings"),
      icon: <IconViewList />,
      pathname: "/ranking",
      isPrivate: false,
    },

    {
      title: t("common.home_sidebar.topic"),
      icon: <IconGridFour />,
      pathname: "/topic",
    },
  ];
  const secondRoutes: IHomeRoute[] = [
    {
      title: t("common.home_sidebar.library"),
      icon: <IconStreamline />,
      pathname: "/library",
      onPress: () => {
        setIsOpenLibrary(!isOpenLibrary);
      },
      open: isOpenLibrary,
      children: [
        {
          title: t("common.home_sidebar.favorite"),
          pathname: "/library/favorite",
          isPrivate: true,
        },
        {
          title: t("common.home_sidebar.recent"),
          pathname: "/library/recent",
          isPrivate: true,
        },
        {
          title: t("common.library.my_playlists"),
          pathname: "/library/my-playlists",
          isPrivate: true,
        },
      ],
    },
    {
      title: t("common.upgrade"),
      icon: <IconUpgrade />,
      pathname: "/upgrade",
      isMobile: true,
      isPrivate: true,
      forcedisplay: true,
    },
    {
      title: t("services.ringback_tone.register_title"),
      icon: <ICRingbackTone />,
      pathname: "",
      onPress: () => {
        setIsModalRegisterRingbackTone(true);
      },
      isMobile: true,
      isPrivate: true,
      forcedisplay: true,
    },
    {
      title: t("common.settings"),
      icon: <IconSetting />,
      pathname: "",
      onPress: () => {
        setIsModalSettingOpen(true);
      },
      isMobile: true,
    },
    {
      title: getFullName ?? userInfo?.username ?? t("auth.profile"),
      icon: userInfo?.avatar ? (
        <img
          src={userInfo.avatar}
          alt="avatar"
          className="h-6 w-6 rounded-full"
        />
      ) : (
        <AccountCircleIcon />
      ),
      pathname: "",
      isMobile: true,
      isPrivate: true,
      onPress: () => {
        setIsOpenProfile(!isOpenProfile);
      },
      open: isOpenProfile,
      children: [
        {
          title: t("auth.information"),
          pathname: "/profile",
          isPrivate: true,
          isMobile: true,
        },
        {
          title: t("auth.logout"),
          pathname: "",
          onPress: () => {
            setIsModalLogOut(true);
          },
          isPrivate: true,
          isMobile: true,
        },
      ],
    },
  ];

  const handleCloseModalLogout = () => {
    setIsModalLogOut(false);
  };

  const handleConfirmLogout = () => {
    dispatch(logoutUser());
    handleCloseModalLogout();
    navigate("/");
  };

  const DrawerList = (
    <Box
      sx={{width: 260}}
      className={clsx(
        "bg-[linear-gradient(#3F1414,#161110_40%)] h-screen overflow-y-scroll sidebar",
      )}
      role="presentation"
    >
      <div
        className="flex justify-center py-7"
        onClick={() => toggleDrawer(false)}
      >
        <img
          src="/image/logo.png"
          className="cursor-pointer max-sm:w-32 max-lg:w-40"
          alt="logo"
          onClick={() => navigate("/")}
        />
      </div>
      <hr className="border-t border-t-[#FFFFFF12]" />
      <List
        sx={{paddingBottom: "32px", paddingTop: "12px", overflow: "hidden"}}
        onClick={() => {
          toggleDrawer(false);
        }}
      >
        {firstRoutes.map((route, index) => (
          <div key={`sidebar_route_${index}`}>
            {!route?.children ? (
              <HomeRouteItem item={route} key={`sidebar_route_${index}`} />
            ) : (
              <>
                <div
                  onClick={(e) => {
                    e.stopPropagation();
                  }}
                >
                  <HomeRouteItem
                    item={route}
                    key={`sidebar_route_${index}`}
                    onPress={route?.onPress}
                  />
                </div>
              </>
            )}
          </div>
        ))}
      </List>
      <hr className="border-t border-t-[#FFFFFF12]" />
      <List
        sx={{paddingTop: "20px", paddingBottom: 0, overflow: "hidden"}}
        onClick={() => toggleDrawer(false)}
      >
        {secondRoutes.map((route, index) => {
          if (!route.isPrivate || accessToken || route.forcedisplay) {
            return (
              <div key={`sidebar_route_${index}`}>
                {!route?.children ? (
                  route.isPrivate ? (
                    <AuthButtonWrapper action={route?.onPress}>
                      <HomeRouteItem
                        item={route}
                        key={`sidebar_route_${index}`}
                      />
                    </AuthButtonWrapper>
                  ) : (
                    <HomeRouteItem
                      item={route}
                      key={`sidebar_route_${index}`}
                      onPress={route?.onPress}
                    />
                  )
                ) : (
                  <>
                    <div
                      onClick={(e) => {
                        e.stopPropagation();
                      }}
                    >
                      <HomeRouteItem
                        item={route}
                        key={`sidebar_route_${index}`}
                        onPress={route?.onPress}
                      />
                    </div>
                    {route?.children && (
                      <Collapse
                        in={
                          route.title === t("common.home_sidebar.library")
                            ? isOpenLibrary
                            : isOpenProfile
                        }
                      >
                        {route?.children?.map((route, index) => (
                          <HomeRouteItem
                            item={route}
                            key={`sidebar_route_${index}`}
                            onPress={route?.onPress}
                          />
                        ))}
                      </Collapse>
                    )}
                  </>
                )}
              </div>
            );
          }
        })}
      </List>
      <BoxSuggestLogin toggleDrawer={toggleDrawer} />
    </Box>
  );

  useEffect(() => {
    if (width > 1024) {
      toggleDrawer(false);
    }
  }, [width]);

  return (
    <>
      <div
        className={clsx(
          "sidebar hidden-scrollbar hidden lg:block max-w-[300px] min-w-[250px] bg-[linear-gradient(#3F1414,#161110_40%)] z-20 transition-width duration-300",
          className,
          currentSong && !isOpenLyrics && isOnline
            ? "h-[calc(100vh-96px)] max-sm:h-[calc(100vh-140px)]"
            : "h-[100vh]",
        )}
      >
        <div className="flex justify-center py-7">
          <img
            src="/image/logo.png"
            className="w-40 cursor-pointer max-sm:w-32"
            draggable="false"
            onDragStart={(e) => e.preventDefault()}
            onClick={() => navigate("/")}
          />
        </div>
        <hr className="border-t border-t-[#FFFFFF12]" />
        <List
          sx={{paddingBottom: "32px", paddingTop: "12px", overflow: "hidden"}}
        >
          {firstRoutes.map((route, index) => (
            <div key={`sidebar_route_${index}`}>
              {!route?.children ? (
                <HomeRouteItem item={route} key={`sidebar_route_${index}`} />
              ) : (
                <>
                  <HomeRouteItem
                    item={route}
                    key={`sidebar_route_${index}`}
                    onPress={() => setIsOpenLibrary(!isOpenLibrary)}
                  />
                  {route?.children && (
                    <Collapse in={isOpenLibrary}>
                      {route?.children?.map((route, index) => {
                        return (
                          <HomeRouteItem
                            item={route}
                            key={`sidebar_route_${index}`}
                          />
                        );
                      })}
                    </Collapse>
                  )}
                </>
              )}
            </div>
          ))}
        </List>
        <hr className="border-t border-t-[#FFFFFF12]" />
        <List sx={{paddingTop: "20px", paddingBottom: 0, overflow: "hidden"}}>
          {secondRoutes.map((route, index) => (
            <div key={`sidebar_route_${index}`}>
              {!route.isMobile && (
                <>
                  <HomeRouteItem
                    item={route}
                    key={`sidebar_route_${index}`}
                    onPress={route?.onPress}
                  />
                  {route?.children && (
                    <Collapse in={isOpenLibrary}>
                      {route.children.map((childRoute, childIndex) => (
                        <HomeRouteItem
                          item={childRoute}
                          key={`sidebar_child_route_${childIndex}`}
                          onPress={childRoute?.onPress}
                        />
                      ))}
                    </Collapse>
                  )}
                </>
              )}
            </div>
          ))}
        </List>
      </div>
      <Drawer open={isDrawerOpen} onClose={() => toggleDrawer(false)}>
        {DrawerList}
      </Drawer>
      <ModalSettingGeneral
        open={isModalSettingOpen}
        onCancel={() => setIsModalSettingOpen(false)}
      />
      <ModalConfirm
        open={isModalLogout}
        title={t("auth.logout")}
        onConfirm={handleConfirmLogout}
        onCancel={handleCloseModalLogout}
      >
        <span>{t("auth.logout_confirm")}</span>
      </ModalConfirm>
      <ModalRingbackTone
        open={isModalRegisterRingbackTone}
        onCancel={() => setIsModalRegisterRingbackTone(false)}
      />
    </>
  );
}
