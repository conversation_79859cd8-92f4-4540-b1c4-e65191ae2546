import {SVGProps} from "react";

function IconMusicNote(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={20}
      height={20}
      fill="none"
      {...props}
    >
      <g
        stroke={props.fill || "currentColor"}
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={1.5}
        clipPath="url(#a)"
      >
        <path d="M10 18.929c5.714 0 8.929-3.215 8.929-8.929S15.714 1.071 10 1.071 1.071 4.286 1.071 10 4.286 18.929 10 18.929ZM10.177 12.13V5.01" />
        <path d="M14.516 9.138c0-2.211-1.921-4.093-4.132-4.126a14.016 14.016 0 0 0-.206-.001M8.118 14.19c1.318 0 2.06-.741 2.06-2.06 0-1.317-.742-2.059-2.06-2.059s-2.06.742-2.06 2.06.742 2.06 2.06 2.06Z" />
      </g>
      <defs>
        <clipPath id="a">
          <path fill={props.fill || "currentColor"} d="M0 0h20v20H0z" />
        </clipPath>
      </defs>
    </svg>
  );
}

export default IconMusicNote;
