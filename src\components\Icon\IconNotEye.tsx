import {SVGProps} from "react";

function IconNotEye(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={24}
      height={24}
      fill="none"
      {...props}
    >
      <path
        stroke="#fff"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeOpacity=".8"
        d="M10.73 5.073A11.01 11.01 0 0 1 12 5c4.664 0 8.4 2.903 10 7-.387.997-.91 1.935-1.555 2.788M6.52 6.519C4.48 7.764 2.9 9.693 2 12c1.6 4.097 5.336 7 10 7a10.44 10.44 0 0 0 5.48-1.52m-7.6-7.6a3 3 0 1 0 4.243 4.243"
      />
      <path
        stroke="#fff"
        strokeLinecap="round"
        strokeOpacity=".8"
        d="m4 4 16 16"
      />
    </svg>
  );
}

export default IconNotEye;
