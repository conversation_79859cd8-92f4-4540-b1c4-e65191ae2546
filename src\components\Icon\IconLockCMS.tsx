import {SVGProps} from "react";

function IconLockCMS({
  width = "20",
  height = "20",
  ...props
}: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox={`0 0 ${width} ${height}`}
      className={props.className}
      fill="none"
      {...props}
    >
      <g clipPath="url(#clip0_3643_21612)">
        <path
          d="M15 9.16412C14.6734 8.49745 13.79 7.51745 11.975 7.55912C11.975 7.55912 10.5359 7.49662 8.90835 7.49662C7.28168 7.49662 6.52002 7.53495 5.21669 7.55912C4.38252 7.53829 2.79669 7.72579 2.06669 9.45579C1.58669 10.9141 1.56669 13.9783 1.85835 15.52C1.92085 16.3116 2.33835 17.4166 3.63168 18C4.42335 18.4166 5.69669 18.25 6.65669 18.3333M4.98669 6.82995C4.94502 4.85079 4.86169 3.28829 7.15585 1.99579C7.92752 1.68329 9.07502 1.41245 10.4934 2.07912C11.9742 2.97495 12.1592 3.92329 12.2875 4.16245C12.6417 5.10079 12.4542 6.43412 12.4959 6.97579"
          stroke={props.stroke || "currentColor"}
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M12.6875 14.8248L14.3459 13.2065M14.3459 13.2065L15.5217 12.0748C15.7917 11.8448 16.3334 11.3065 16.9775 11.9232L18.3334 13.2065M14.3459 13.2065L15.6875 14.494M12.9167 16.4457C12.9167 16.6908 12.8682 16.9334 12.774 17.1597C12.6799 17.386 12.5419 17.5914 12.368 17.7641C12.1941 17.9369 11.9878 18.0735 11.7608 18.1661C11.5339 18.2588 11.2909 18.3057 11.0459 18.304C10.0159 18.304 9.16669 17.4823 9.16669 16.4457C9.16867 15.9487 9.36758 15.4729 9.71983 15.1223C10.0721 14.7718 10.5489 14.5752 11.0459 14.5757C12.0759 14.5757 12.9167 15.409 12.9167 16.4457Z"
          stroke={props.stroke || "currentColor"}
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_3643_21612">
          <rect width="20" height="20" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

export default IconLockCMS;
