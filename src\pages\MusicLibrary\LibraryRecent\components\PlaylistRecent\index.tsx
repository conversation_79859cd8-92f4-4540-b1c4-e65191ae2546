import ApiRecent from "@api/ApiRecent";
import {IDataWithMeta} from "@api/Fetcher";
import QUERY_KEY from "@api/QueryKey";
import AlbumCardSkeleton from "@components/AlbumCardSkeleton";
import CommonAlbumCard from "@components/CommonAlbumCard";
import IconNoData from "@components/Icon/IconNoData";
import Subtitle from "@components/Subtitle";
import {Grid} from "@mui/material";
import {useInfiniteQuery} from "@tanstack/react-query";
import {useEffect, useRef} from "react";
import {useTranslation} from "react-i18next";
import {IPlaylist, PlaylistType} from "src/types";

interface IPlaylistRecentProps {
  type: PlaylistType;
}

export default function PlaylistRecent({
  type,
}: IPlaylistRecentProps): JSX.Element {
  const {t} = useTranslation();

  const observer = useRef<IntersectionObserver | null>(null);
  const lastElementRef = useRef<HTMLDivElement | null>(null);
  const {
    data: dataPlaylist,
    fetchNextPage,
    isLoading,
    isError,
    hasNextPage,
  } = useInfiniteQuery<IDataWithMeta<IPlaylist[]>, Error>({
    queryKey: [
      type === PlaylistType.ALBUM
        ? QUERY_KEY.RECENT.GET_RECENT_ALBUMS
        : QUERY_KEY.RECENT.GET_RECENT_PLAYLISTS,
    ],
    queryFn: ({pageParam = 0}) =>
      ApiRecent.recentPlaylists({
        pageSize: 15,
        page: pageParam as number,
        type,
      }),
    getNextPageParam: (lastPage) =>
      lastPage?.meta?.totalPage - 1 > lastPage?.meta?.currentPage
        ? (lastPage?.meta?.currentPage || 0) + 1
        : undefined,
    initialPageParam: 0,
  });

  useEffect(() => {
    if (isLoading || isError) return;

    if (observer.current) observer.current.disconnect();

    observer.current = new IntersectionObserver((entries) => {
      if (entries[0].isIntersecting && hasNextPage) {
        fetchNextPage();
      }
    });

    if (lastElementRef?.current) {
      observer.current.observe(lastElementRef.current);
    }

    return () => {
      if (observer.current) {
        observer.current.disconnect();
      }
    };
  }, [isLoading, isError, dataPlaylist, hasNextPage]);

  const isEmpty =
    !dataPlaylist ||
    !dataPlaylist.pages ||
    dataPlaylist.pages.every((page) => page.data.length === 0);

  return (
    <div className="w-full flex flex-col gap-1 sm:gap-2 md:gap-3 lg:gap-4">
      <Subtitle
        subtitle={
          type === PlaylistType.ALBUM ? t("common.album") : t("common.playlist")
        }
        seeMore={false}
      />
      {isLoading && !dataPlaylist && (
        <Grid
          container
          spacing={{xs: 2, md: 3}}
          columns={{xs: 2, sm: 6, md: 12, lg: 15}}
        >
          {[...Array(5)].map((_, index) => (
            <Grid item xs={1} sm={2} md={3} lg={3} key={index}>
              <AlbumCardSkeleton
                isMultipleInfo={type === PlaylistType.ALBUM ? true : false}
              />
            </Grid>
          ))}
        </Grid>
      )}
      {!isLoading && (isError || isEmpty) && (
        <div className=" flex justify-center items-center flex-col gap-2.5">
          <IconNoData />
          <span className="text-[#E3E3E3] w-full text-base font-normal flex justify-center">
            {type === PlaylistType.ALBUM
              ? t("common.album_list_not_found")
              : t("common.playlist_list_not_found")}
          </span>
        </div>
      )}
      <div>
        <Grid
          container
          spacing={{xs: 2, md: 3}}
          columns={{xs: 2, sm: 6, md: 12, lg: 15}}
        >
          {!isLoading &&
            !(isError || isEmpty) &&
            dataPlaylist?.pages?.map((page) =>
              page?.data?.map((playlist, index) => (
                <Grid item xs={1} sm={2} md={3} lg={3} key={index}>
                  <CommonAlbumCard
                    data={playlist}
                    haveLayer={type === PlaylistType.ALBUM ? true : false}
                  />
                </Grid>
              )),
            )}
        </Grid>
        {hasNextPage && (
          <Grid
            container
            spacing={{xs: 2, md: 3}}
            columns={{xs: 2, sm: 6, md: 12, lg: 15}}
          >
            {[...Array(5)].map((_, index) => (
              <Grid item xs={1} sm={2} md={3} lg={3} key={index}>
                <AlbumCardSkeleton
                  isMultipleInfo={type === PlaylistType.ALBUM ? true : false}
                />
              </Grid>
            ))}
          </Grid>
        )}
      </div>
      {!isLoading && <div ref={lastElementRef} className="h-1"></div>}
    </div>
  );
}
