import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "recharts";

interface IBarChartProps {
  data: {
    name: string;
    value: number;
  }[];
  linearGradientColor?: {
    id: string;
    linear: {offset: string; color: string; stopOpacity?: number}[];
  };
  labels?: {
    x?: string;
    y?: string;
  };
}

function BarChartCustom({data, linearGradientColor, labels}: IBarChartProps) {
  const customTickOfXAxis = ({
    x = 0,
    y = 0,
    payload = {} as {value?: string},
    index = 0,
  }) => {
    const value = payload.value || "";
    const length = data.length;
    const maxLength = length <= 10 ? 10 : length <= 20 ? 5 : 2;
    const name =
      value.length > maxLength ? value.slice(0, maxLength) + "…" : value;

    return (
      <g
        key={`tick-${value}-${index}`}
        transform={`translate(${x}, ${y + 10})`}
      >
        <title>{value}</title>
        <text textAnchor="middle" fill="#707070" fontSize={12}>
          {name}
        </text>
      </g>
    );
  };

  return (
    <div style={{width: "100%", height: 300}}>
      <ResponsiveContainer>
        <BarChart
          data={data}
          margin={{top: 20, right: 20, left: 20, bottom: 30}}
        >
          <defs>
            <linearGradient
              id={linearGradientColor?.id}
              x1="0"
              y1="0"
              x2="0"
              y2="1"
            >
              {linearGradientColor?.linear?.map((item) => (
                <stop
                  offset={item.offset}
                  stopColor={item.color}
                  stopOpacity={item.stopOpacity}
                  key={`bar_chart_${item.offset}-${item.color}`}
                />
              ))}
            </linearGradient>
          </defs>
          <CartesianGrid strokeDasharray="3 3" vertical={false} />

          {labels?.x ? (
            <XAxis
              dataKey="name"
              interval={0}
              scale="point"
              padding={{left: 20, right: 20}}
              tick={customTickOfXAxis}
              tickMargin={10}
              label={{
                value: labels.x,
                position: "insideBottom",
                offset: -25,
                style: {fill: "#707070"},
              }}
            />
          ) : (
            <XAxis
              dataKey="name"
              interval={0}
              tick={customTickOfXAxis}
              scale="point"
              padding={{left: 20, right: 20}}
              tickMargin={10}
            />
          )}
          {labels?.y ? (
            <YAxis
              label={{
                value: labels.y,
                angle: -90,
                position: "insideLeft",
                style: {fill: "#707070"},
              }}
            />
          ) : (
            <YAxis />
          )}

          <Tooltip />
          <Bar
            dataKey="value"
            fill={`url(#${linearGradientColor?.id})`}
            barSize={35}
            radius={[10, 10, 0, 0]}
          />
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
}

export default BarChartCustom;
