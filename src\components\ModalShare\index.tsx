import {Dialog} from "@mui/material";
import {useTranslation} from "react-i18next";
import ICShare from "@components/Icon/ICShare";
import CloseIcon from "@mui/icons-material/Close";
import {IArtist} from "src/types";

interface ModalShareProps {
  open: boolean;
  onCancel: () => void;
  handleCopyLink: () => void;
  name?: string;
  image?: string;
  artists?: IArtist[];
  shareUrl: string;
}

export default function ModalShare({
  open,
  onCancel,
  handleCopyLink,
  name,
  image,
  artists,
  shareUrl,
}: ModalShareProps) {
  const {t} = useTranslation();

  const handleShareFacebook = () => {
    const encodedUrl = encodeURIComponent(shareUrl);
    window.open(
      `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
      "_blank",
    );
  };

  return (
    <Dialog
      open={open}
      onClose={onCancel}
      maxWidth="md"
      fullWidth
      classes={{paper: "rounded-lg"}}
      PaperProps={{
        style: {
          background: "#1C1717",
          color: "white",
          boxSizing: "border-box",
          gap: "4px",
          padding: "20px 28px",
          borderRadius: "12px",
          width: "444px",
          boxShadow: "0px_10px_15px_0px_#1B28361A",
        },
      }}
    >
      <div>
        <div className="flex justify-between items-start">
          <div className="infoSong grid w-3/4 items-center justify-start gap-x-3">
            <div className="relative col-start-1 col-span-1 row-start-1 row-span-2">
              <img
                src={image || "/image/default-music.png"}
                alt={name}
                className="w-14 h-14 rounded object-cover"
              />
            </div>
            <div className="nameSong col-start-2 col-span-1 row-start-1 row-span-1 text-white whitespace-nowrap overflow-hidden text-ellipsis text-base">
              {name}
            </div>
            {artists && (
              <div className="nameArtist truncate min-w-0 col-start-2 col-span-1 row-start-2 row-span-1 mt-3 whitespace-nowrap w-[95%] text-[#615A5C]">
                {artists
                  ?.map((artist: IArtist) => artist?.stageName ?? artist?.name)
                  .join(", ") ?? t("common.not_info_artist")}
              </div>
            )}
          </div>
          <div className="close cursor-pointer" onClick={onCancel}>
            <CloseIcon />
          </div>
        </div>
        <div className="w-full h-0 border-solid border-[1px] my-5 border-[#FFFFFF0D]"></div>
        <div className="actionShare flex flex-col gap-4">
          <div className="title text-base font-semibold leading-[14px]">
            {t("common.share")}
          </div>
          <div className="flex justify-center items-center">
            <div className="grid grid-cols-2 w-full box-border flex-1 rounded-md">
              <div className="flex items-center flex-col justify-center gap-3">
                <div
                  className="flex rounded-full items-center w-[64.8px] aspect-square justify-center bg-[#FFFFFF0D] cursor-pointer"
                  onClick={handleShareFacebook}
                >
                  <img
                    src="/image/iconFacebook.png"
                    className="w-[38.4px] aspect-square object-cover"
                  />
                </div>
                <div className="text-sm text-[#FFFFFFCC]">Facebook</div>
              </div>
              <div className="flex items-center flex-col justify-center gap-3">
                <div
                  className="flex rounded-full items-center w-[64.8px] aspect-square justify-center bg-[#FFFFFF0D] cursor-pointer"
                  onClick={() => {
                    onCancel();
                    handleCopyLink();
                  }}
                >
                  <ICShare />
                </div>
                <div className="text-sm text-[#FFFFFFCC]">
                  {t("common.copy_link")}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Dialog>
  );
}
