import {SVGProps} from "react";

function IconTop100({
  width = "20",
  height = "20",
  ...props
}: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox={`0 0 ${width} ${height}`}
      className={props.className}
      fill="none"
      {...props}
    >
      <path
        d="M10.1718 10.553L12.4592 9.17107L14.7465 10.553L14.1433 7.93989L16.1542 6.20618L13.5149 5.98004L12.4592 3.51767L11.4034 5.98004L8.76414 6.20618L10.775 7.93989L10.1718 10.553ZM10.6493 17.0858H16.2798C16.1625 17.5214 15.9615 17.8731 15.6766 18.1411C15.3917 18.4091 15.023 18.5767 14.5706 18.6437L3.61123 19.9753C3.05823 20.0591 2.55953 19.9295 2.11512 19.5864C1.67072 19.2433 1.41533 18.7951 1.34897 18.2416L0.0167574 7.26148C-0.0502723 6.7087 0.0837872 6.21455 0.418936 5.77903C0.754085 5.34351 1.19816 5.09225 1.75115 5.02524L2.90741 4.87449V6.88459L2.00251 7.01022L3.35987 17.9904L10.6493 17.0858ZM6.9292 15.0757C6.3762 15.0757 5.90297 14.8791 5.50951 14.4858C5.11604 14.0925 4.91898 13.6191 4.91831 13.0656V2.0101C4.91831 1.45732 5.11537 0.984278 5.50951 0.590969C5.90364 0.19766 6.37687 0.000670033 6.9292 0H17.9891C18.5421 0 19.0157 0.19699 19.4098 0.590969C19.8039 0.984948 20.0007 1.45799 20 2.0101V13.0656C20 13.6184 19.8033 14.0918 19.4098 14.4858C19.0163 14.8798 18.5428 15.0764 17.9891 15.0757H6.9292ZM6.9292 13.0656H17.9891V2.0101H6.9292V13.0656Z"
        fill={props.fill || "#ffffff"}
      />
    </svg>
  );
}

export default IconTop100;
