import LikeButton from "@components/AuthButton/LikeButton";
import IconAdd24px from "@components/Icon/IconAdd24px";
import {useWindowWidth} from "src/utils/hooks";
import ModalAddToPlaylist from "@components/ModalAddToPlaylist";
import {IconButton} from "@mui/material";
import {toggleLyrics} from "@redux/slices/PlayerSlice";
import {IRootState} from "@redux/store";
import clsx from "clsx";
import {useMemo, useState} from "react";
import {useTranslation} from "react-i18next";
import {useDispatch, useSelector} from "react-redux";
import {useNavigate} from "react-router-dom";
import {ESongType, IPlaylist} from "src/types";
import AuthButtonWrapper from "@components/AuthButton/AuthButtonWrapper";
import CustomTooltip from "@components/CustomTooltip";
import IconYoutube from "@components/Icon/IconYoutube";
import {handleLikeSong} from "src/utils/like";
import PopupMenu from "@components/PopupMenu";
import IconHeart from "@components/Icon/IconHeart";
import IconUser from "@components/Icon/IconUser";
import ICInformation from "@components/Icon/ICInformation";
import IconShare from "@components/Icon/IconShare";
import ModalShare from "@components/ModalShare";
import {generateShareLink} from "src/utils/global";
import {useMutation} from "@tanstack/react-query";
import ApiSong from "@api/ApiSong";
import {toast} from "react-toastify";
import IconMoreHorizontal from "@components/Icon/IconMoreHorizontal";

export default function SongInfo() {
  const {t} = useTranslation();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [addToPlaylistOpen, setAddToPlaylistOpen] = useState(false);
  const {currentSong, currentPlaylistId} = useSelector(
    (state: IRootState) => state?.player,
  );
  const [openModalShare, setOpenModalShare] = useState<boolean>(false);
  const [menuAnchor, setMenuAnchor] = useState<null | HTMLElement>(null);
  const playlistUrl = useMemo(() => {
    return currentPlaylistId
      ? currentSong?.playlists?.find(
          (playlist: IPlaylist) => playlist?.id === currentPlaylistId,
        )?.urlSlug || currentSong?.playlists?.[0]?.urlSlug
      : currentSong?.playlists?.[0]?.urlSlug || "unknown-playlist";
  }, [currentSong, currentPlaylistId]);
  const hasPlaylist =
    Array.isArray(currentSong?.playlists) && currentSong.playlists.length > 0;
  const width = useWindowWidth();
  const isMobile = useMemo(() => width <= 834, [width]);

  const shareMutate = useMutation({
    mutationFn: ApiSong.shareSong,
    onSuccess: () => {
      const link = generateShareLink({type: "song", data: currentSong});
      navigator.clipboard.writeText(link).then(() => {
        toast.success(t("common.copy_link_success"));
      });
    },
    onError: () => {
      toast.error(t("common.copy_link_failed"));
    },
  });

  const handleOpenMenu = (event: React.MouseEvent<HTMLButtonElement>) => {
    event.stopPropagation();
    setMenuAnchor(event.currentTarget);
  };

  const handleCloseMenu = () => {
    setMenuAnchor(null);
  };

  const openAddToPlaylist = () => {
    setAddToPlaylistOpen(true);
  };

  const handleWatchDetailArtist = () => {
    if (currentSong?.artists?.[0]?.urlSlug) {
      navigate(`/artist/${currentSong?.artists[0]?.urlSlug}`);
    }
  };

  const handleWatchDetailAlbum = () => {
    if (currentSong?.playlists?.[0]?.urlSlug) {
      navigate(`/playlist/${currentSong?.playlists[0]?.urlSlug}`);
    }
  };

  const handleOpenModalShare = () => {
    setOpenModalShare(true);
  };

  const handleCloseModalShare = () => {
    setOpenModalShare(false);
  };

  const handleCopyLink = () => {
    shareMutate.mutateAsync(currentSong?.id ?? "");
  };

  if (!currentSong) {
    return null;
  }

  return (
    <div className="flex gap-x-4 mr-4 select-none">
      <img
        src={
          currentSong?.images?.SMALL ||
          currentSong?.images?.DEFAULT ||
          "/image/default-music.png"
        }
        className="h-[60px] w-[60px] max-[568px]:h-[48px] max-[568px]:w-[48px] rounded-lg object-cover cursor-pointer"
        onClick={(e) => {
          if (isMobile) {
            dispatch(toggleLyrics());
          } else if (hasPlaylist) {
            navigate(`/playlist/${playlistUrl}`);
          }
          e.stopPropagation();
        }}
      />
      <div className="flex flex-col justify-around max-[924px]:mr-4 max-sm:mr-0">
        <div className="flex max-w-72 sm:min-w-64 gap-x-2">
          <div
            className="line-clamp-1 leading-6 cursor-pointer max-[568px]:text-sm text-[14px]"
            onClick={(e) => {
              if (isMobile) {
                dispatch(toggleLyrics());
              } else if (hasPlaylist) {
                navigate(`/playlist/${playlistUrl}`);
              }
              e.stopPropagation();
            }}
          >
            {currentSong?.name}
          </div>
          <div
            className="flex flex-row justify-center gap-2 max-[568px]:hidden"
            onClick={(e) => e.stopPropagation()}
          >
            <CustomTooltip title={t("common.like")}>
              <div>
                <LikeButton
                  isLiked={currentSong?.isLiked}
                  songId={currentSong?.id}
                  className={clsx(
                    "song-action",
                    !isMobile && "hover:rounded-full",
                  )}
                  classNameIcon="h-[18px] w-[18px]"
                  action={() => handleLikeSong(currentSong)}
                />
              </div>
            </CustomTooltip>
            <CustomTooltip title={t("common.menu.add_to_playlist")}>
              <div>
                <AuthButtonWrapper action={openAddToPlaylist}>
                  <IconButton style={{padding: "0"}}>
                    <IconAdd24px />
                  </IconButton>
                </AuthButtonWrapper>
              </div>
            </CustomTooltip>
            <CustomTooltip title={t("common.see_more")}>
              <div>
                <IconButton style={{padding: "0"}} onClick={handleOpenMenu}>
                  <IconMoreHorizontal />
                </IconButton>
              </div>
            </CustomTooltip>

            <ModalAddToPlaylist
              open={addToPlaylistOpen}
              onClose={() => setAddToPlaylistOpen(false)}
              songData={currentSong}
            />
            {width > 568 && (
              <>
                <PopupMenu
                  data={currentSong}
                  menuArray={[
                    {
                      icon: (
                        <IconHeart
                          width={24}
                          height={24}
                          className={clsx(
                            "cursor-pointer like-button",
                            currentSong?.id && `song-${currentSong.id}-liking`,
                            currentSong?.isLiked && "liked",
                          )}
                        />
                      ),
                      label: (
                        <span
                          className={clsx(
                            "text-like-button",
                            currentSong?.id && `song-${currentSong.id}-liking`,
                            currentSong?.isLiked && "liked",
                          )}
                        >
                          {currentSong?.isLiked
                            ? t("common.menu.remove_from_playlist_favorite")
                            : t("common.menu.add_to_playlist_favorite")}
                        </span>
                      ),
                      action: () => handleLikeSong(currentSong),
                      isAuth: true,
                    },
                    {
                      icon: <IconAdd24px />,
                      label: t("common.menu.add_to_playlist"),
                      action: openAddToPlaylist,
                      isAuth: true,
                    },
                    {
                      icon: <IconUser />,
                      label: t("common.menu.watch_detail_artist"),
                      action: handleWatchDetailArtist,
                      isAuth: false,
                    },
                    {
                      icon: <ICInformation />,
                      label: t("common.menu.watch_detail_album"),
                      action: handleWatchDetailAlbum,
                      isAuth: false,
                    },
                  ].concat(
                    currentSong.type === ESongType.SONG
                      ? {
                          icon: <IconShare height={24} width={24} />,
                          label: t("common.menu.share_link_song"),
                          action: handleOpenModalShare,
                          isAuth: false,
                        }
                      : [],
                  )}
                  anchorEl={menuAnchor}
                  open={Boolean(menuAnchor)}
                  onClose={handleCloseMenu}
                  anchorOrigin={{
                    vertical: "top",
                    horizontal: "right",
                  }}
                  transformOrigin={{
                    vertical: "bottom",
                    horizontal: "left",
                  }}
                  slotProps={{
                    paper: {
                      style: {
                        minWidth: "200px",
                        width: "375px",
                        background: "#1C1717",
                        color: "#ffffffcc",
                        boxShadow: "0px 10px 15px 0px #1B28361A",
                        borderRadius: "12px",
                        padding: "20px 16px",
                      },
                    },
                    list: {
                      style: {
                        padding: 0,
                        display: "flex",
                        flexDirection: "column",
                        gap: "8px",
                      },
                    },
                  }}
                ></PopupMenu>
                <ModalShare
                  open={openModalShare}
                  onCancel={handleCloseModalShare}
                  handleCopyLink={handleCopyLink}
                  image={
                    currentSong?.images?.SMALL || currentSong?.images?.DEFAULT
                  }
                  name={currentSong?.name}
                  artists={currentSong?.artists}
                  shareUrl={generateShareLink({
                    type: "song",
                    data: currentSong,
                  })}
                />
              </>
            )}
          </div>
        </div>

        {currentSong.type === ESongType.YOUTUBE ? (
          <div className="flex flex-row gap-1 items-center bg-[#FFE7E117] p-1 rounded w-fit">
            <IconYoutube />
            <span className="text-[10px] text-[#FFCC57] leading-3">
              {t("common.video_mv")}
            </span>
          </div>
        ) : (
          <div className="text-sm leading-5 text-[#e8dbdf] max-[568px]:text-xs align-text-bottom whitespace-nowrap max-w-40 text-ellipsis overflow-hidden">
            {Array.isArray(currentSong?.artists) &&
            currentSong?.artists?.length > 0
              ? currentSong?.artists?.map((artist, index) => (
                  <span key={artist?.id}>
                    <span
                      onClick={(e) => {
                        if (isMobile) {
                          dispatch(toggleLyrics());
                        } else {
                          navigate(`/artist/${artist?.urlSlug || "unknown"}`);
                        }
                        e.stopPropagation();
                      }}
                      className={clsx(
                        "cursor-pointer",
                        !isMobile && "hover:text-sky-500 hover:underline",
                      )}
                    >
                      {artist?.stageName ?? artist?.name}
                    </span>
                    {index < (currentSong?.artists?.length || 0) - 1 && ", "}
                  </span>
                ))
              : t("common.not_info_artist")}
          </div>
        )}
      </div>
    </div>
  );
}
