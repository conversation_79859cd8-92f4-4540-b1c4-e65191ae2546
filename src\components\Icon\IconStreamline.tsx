import {SVGProps} from "react";

function IconStreamline(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={24}
      height={24}
      fill="none"
      {...props}
    >
      <path
        fill={props.fill || "currentColor"}
        d="M17.254 2.557h4.197c.58 0 1.05.47 1.05 1.05v16.787c0 .58-.47 1.05-1.05 1.05h-4.197V2.556ZM2.416 2.428 15.606.544a.525.525 0 0 1 .6.52v21.873a.525.525 0 0 1-.6.52l-13.19-1.885a1.05 1.05 0 0 1-.9-1.038V3.466c0-.522.384-.965.9-1.039Zm8.543 5.375v5.235L8.86 10.95l-2.087 2.098-.011-5.246H4.663v8.394h2.099L8.86 14.1l2.099 2.098h2.098V7.803H10.96Z"
      />
      <path
        fill={props.fill || "currentColor"}
        d="M1.936 3.606h13.431v16.788H1.936z"
      />
      <g clipPath="url(#a)">
        <path
          fill="#000"
          d="M9.984 6.415a1.583 1.583 0 0 0-1.671 1.033 1.729 1.729 0 0 0-.086.5 9.929 9.929 0 0 0-.009.478v4.913a2.734 2.734 0 1 0 .863 1.994v-5.059c.**************.198.1l1.558.78c.24.12.437.218.597.288.162.07.321.128.486.147a1.583 1.583 0 0 0 1.671-1.033c.057-.155.077-.323.086-.5.01-.175.01-.394.01-.663v-.048c0-.2 0-.356-.03-.505a1.583 1.583 0 0 0-.592-.96 2.716 2.716 0 0 0-.44-.25l-1.558-.78c-.24-.12-.437-.218-.597-.288a1.729 1.729 0 0 0-.486-.147Z"
        />
      </g>
      <defs>
        <clipPath id="a">
          <path fill="#fff" d="M1.375 3.046h15.669v15.67H1.375z" />
        </clipPath>
      </defs>
    </svg>
  );
}

export default IconStreamline;
