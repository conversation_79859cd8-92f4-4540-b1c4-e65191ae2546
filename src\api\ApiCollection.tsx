import {fetcher, fetcherWithMetadata, IDataWithMeta} from "./Fetcher";

const path = {
  collection: "cms/music-collection",
};

export interface ICollection {
  id?: string;
  name?: string;
  avatar?: string;
  image?: string;
  urlSlug?: string;
  createdAt?: string;
  updatedTime?: string;
  description?: string;
  releaseDate?: string;
  hashtag?: string[];
  favoriteCount?: number;
  genres?: string[];
  themes?: string[];
  artists?: [id: string, name: string, avatar: string];
  songList?: [songName: string, artistNames: string[]];
  hashtagCount?: number;
  shareCount?: number;
  playlistCount?: number;
  duration?: number;
  downloadCount?: number;
  listenCount?: number;
  numberOfSongs?: number;
}

interface IListCollectionParams {
  search?: string;
  themeId?: string;
  genreId?: string;
  albumId?: string;
  updatedAt?: string;
  sortField?: string;
  sortDirection?: string;
  page?: number;
  pageSize?: number;
}

export interface ICreateCollection {
  name?: string;
  image?: string | File;
  songIds?: string[];
  themeId?: string;
  genreId?: string[];
  releaseDate?: string;
  description?: string;
}

function getListCollection(
  params: IListCollectionParams,
): Promise<IDataWithMeta<[]>> {
  return fetcherWithMetadata(
    {
      url: path.collection,
      method: "get",
      params,
    },
    {
      displayError: false,
    },
  );
}

function detailCollection(id: string): Promise<ICollection> {
  return fetcher(
    {
      url: `${path.collection}/${id}`,
      method: "get",
    },
    {
      displayError: false,
    },
  );
}

function createCollection(body: ICreateCollection): Promise<void> {
  return fetcher(
    {
      url: path.collection,
      method: "post",
      data: body,
    },
    {
      displayError: false,
      isFormData: true,
    },
  );
}

export default {
  getListCollection,
  detailCollection,
  createCollection,
};
