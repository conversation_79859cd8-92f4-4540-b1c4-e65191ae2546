import {SVGProps} from "react";

function IconArtist({
  width = "20",
  height = "20",
  ...props
}: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox={`0 0 ${width} ${height}`}
      className={props.className}
      fill="none"
      {...props}
    >
      <path
        d="M9.25911 8.4445C10.7626 8.4445 11.9814 7.22571 11.9814 5.72225C11.9814 4.21879 10.7626 3 9.25911 3C7.75566 3 6.53687 4.21879 6.53687 5.72225C6.53687 7.22571 7.75566 8.4445 9.25911 8.4445Z"
        stroke={props.stroke || "#ffffff"}
        strokeWidth="1.5"
      />
      <path
        d="M14.7037 17.5186C15.7061 17.5186 16.5186 16.7061 16.5186 15.7037C16.5186 14.7014 15.7061 13.8889 14.7037 13.8889C13.7014 13.8889 12.8889 14.7014 12.8889 15.7037C12.8889 16.7061 13.7014 17.5186 14.7037 17.5186Z"
        stroke={props.stroke || "#ffffff"}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M9.56876 17.5183H3.81483C3.33351 17.5183 2.8719 17.3271 2.53155 16.9868C2.1912 16.6464 2 16.1848 2 15.7035C2 14.7408 2.38241 13.8176 3.0631 13.1369C3.7438 12.4562 4.66702 12.0738 5.62967 12.0738H10.6459M16.5187 15.7035V9.35156L18.3335 11.1664"
        stroke={props.stroke || "#ffffff"}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}

export default IconArtist;
