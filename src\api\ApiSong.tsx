import {IPlaylist, ISong} from "src/types";
import {fetcher} from "./Fetcher";

const path = {
  interactionSong: "/interaction-song",
  song: "/songs",
  addSongToPlaylist: "add-to-playlist",
  getLikedById: "songs/likes/by-ids",
  getInteractionsSong: "songs/interactions-song",
};

function likeSong(id: string): Promise<ISong> {
  return fetcher(
    {
      url: `${path.interactionSong}/like`,
      method: "post",
      data: {
        songId: id,
      },
    },
    {
      displayError: false,
    },
  );
}

function listenSong(id: string): Promise<ISong> {
  return fetcher(
    {
      url: `${path.interactionSong}/listen`,
      method: "post",
      data: {songId: id},
    },
    {
      displayError: false,
    },
  );
}

function shareSong(id: string): Promise<ISong> {
  return fetcher(
    {
      url: `${path.interactionSong}/share`,
      method: "post",
      data: {songId: id},
    },
    {
      displayError: false,
    },
  );
}

function getSongIncludedInPlaylist(id: string): Promise<IPlaylist[]> {
  return fetcher(
    {
      url: `${path.song}/${id}/included-in-playlists`,
      method: "get",
    },
    {
      displayError: false,
    },
  );
}

function addAndRemoveSongToPlaylist({
  songId,
  playlistId,
}: {
  songId: string;
  playlistId: string;
}): Promise<{song: ISong; playlist: IPlaylist}> {
  return fetcher(
    {
      url: `${path.interactionSong}/${path.addSongToPlaylist}`,
      method: "post",
      data: {
        songId,
        playlistId,
      },
    },
    {
      displayError: false,
    },
  );
}

function getSongById(id: string): Promise<ISong> {
  return fetcher(
    {
      url: `${path.song}/${id}/detail`,
      method: "get",
    },
    {
      displayError: false,
    },
  );
}

function getLikedById(songIds: string[]) {
  return fetcher<{id: string; isLiked: boolean}[]>({
    url: path.getLikedById,
    method: "post",
    data: {
      songIds,
    },
  });
}

function getInteractionsSongById(songId?: string): Promise<ISong> {
  return fetcher({
    url: path.getInteractionsSong + `/${songId}`,
    method: "get",
  });
}

export default {
  likeSong,
  listenSong,
  shareSong,
  addAndRemoveSongToPlaylist,
  getSongIncludedInPlaylist,
  getSongById,
  getLikedById,
  getInteractionsSongById,
};
