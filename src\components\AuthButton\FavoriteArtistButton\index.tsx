import {useTranslation} from "react-i18next";
import AuthButtonWrapper from "../AuthButtonWrapper";
import IconAdd from "@components/Icon/IconAdd";
import {IconButton} from "@mui/material";

interface IFavoriteArtistButtonAuth {
  action?: () => void;
  className?: string;
  loading?: boolean;
}

export default function FavoriteArtistButtonAuth({
  action,
  className,
  loading = false,
}: IFavoriteArtistButtonAuth): JSX.Element {
  const {t} = useTranslation();
  return (
    <AuthButtonWrapper action={action} className={className}>
      <IconButton
        loading={loading}
        disabled={loading}
        className={`!bg-[#FF4319] !border !border-solid !border-transparent !rounded-2xl !text-white !py-2 !px-[14px] !text-sm !gap-[5px] !flex !items-center !justify-center ${loading ? "opacity-50" : ""}`}
      >
        <IconAdd />
        <div className="truncate">{t("common.favorite")}</div>
      </IconButton>
    </AuthButtonWrapper>
  );
}
