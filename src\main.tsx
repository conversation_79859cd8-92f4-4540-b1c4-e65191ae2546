import "@fontsource/roboto";
import {createTheme, ThemeProvider} from "@mui/material";
import store from "@redux/store.ts";
import {QueryClient, QueryClientProvider} from "@tanstack/react-query";
import {StrictMode} from "react";
import {createRoot} from "react-dom/client";
import {Provider} from "react-redux";
import App from "./App.tsx";
import "./i18n";
import "./styles/main.scss";
import {initializeFirebase} from "./utils/firebase.ts";
import PlayerUtil from "./core/PlayerUtil.ts";
import CorePlayer from "./core/CorePlayer.ts";
import YoutubePlayer from "./core/YoutubePlayer.ts";

const youtubePlayer = new YoutubePlayer();
const audioElement = document.querySelector("audio")!;
const corePlayer = new CorePlayer(audioElement);
const players = [corePlayer, youtubePlayer];

PlayerUtil.initialize(players);

initializeFirebase();

const theme = createTheme({
  components: {},
});

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      retry: false,
    },
  },
});

createRoot(document.getElementById("root")!).render(
  <StrictMode>
    <QueryClientProvider client={queryClient}>
      <Provider store={store}>
        <ThemeProvider theme={theme}>
          <App />
        </ThemeProvider>
      </Provider>
    </QueryClientProvider>
  </StrictMode>,
);
