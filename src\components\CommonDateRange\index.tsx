import {ClickAwayListener} from "@mui/material";
import {AdapterDayjs} from "@mui/x-date-pickers/AdapterDayjs";
import {DatePickerProps} from "@mui/x-date-pickers/DatePicker";
import {DesktopDatePicker} from "@mui/x-date-pickers/DesktopDatePicker";
import {LocalizationProvider} from "@mui/x-date-pickers/LocalizationProvider/LocalizationProvider";
import dayjs, {Dayjs} from "dayjs";
import React, {useState} from "react";
import {EStatisticsDateRangeType} from "src/types";

export interface ICommonDateRangeProps extends DatePickerProps<Dayjs> {
  onChangeStartDate?: (v: Dayjs | null) => void;
  onChangeEndDate?: (v: Dayjs | null) => void;
  valueDateRange?: [string | undefined, string | undefined];
  labelDateRange?: [string | React.ReactNode, string | React.ReactNode];
  typeView?: EStatisticsDateRangeType;
  limitByView?: {day?: number; month?: number; year?: number};
}

const styleDatePicker = {
  "width": "180px",
  "backgroundColor": "#f2f2f3",
  "borderRadius": "8px",
  "& .MuiInputBase-root": {
    borderRadius: "8px",
  },
  "& input": {
    fontSize: "14px",
    fontWeight: 400,
    minHeight: "23px",
  },
  "& .MuiOutlinedInput-notchedOutline": {
    border: "1px solid #dcdee0",
  },
  "&:hover .MuiOutlinedInput-notchedOutline": {
    border: "1px solid #dcdee0",
  },
};

export default function CommonDateRange({
  valueDateRange,
  onChangeStartDate,
  onChangeEndDate,
  labelDateRange,
  typeView = EStatisticsDateRangeType.DAY,
  limitByView,
  ...props
}: ICommonDateRangeProps): JSX.Element {
  const [activeKey, setActiveKey] = useState<number | null>(null);

  const handleAcceptDatePicker = () => {
    if (activeKey === 0) {
      setActiveKey(1);
      return;
    }
    if (!valueDateRange?.[0]) {
      setActiveKey(0);
      return;
    }
    setActiveKey(null);
  };

  const getMinDate = () => {
    switch (typeView) {
      case EStatisticsDateRangeType.DAY:
        return valueDateRange?.[1]
          ? dayjs(valueDateRange?.[1]).subtract(limitByView?.day ?? 0, "day")
          : dayjs().subtract(limitByView?.day ?? 0, "day");
      case EStatisticsDateRangeType.MONTH:
        return valueDateRange?.[1]
          ? dayjs(valueDateRange?.[1]).subtract(
              limitByView?.month ?? 0,
              "month",
            )
          : dayjs().subtract(limitByView?.month ?? 0, "month");
      case EStatisticsDateRangeType.YEAR:
        return valueDateRange?.[1]
          ? dayjs(valueDateRange?.[1]).subtract(limitByView?.year ?? 0, "year")
          : dayjs().subtract(limitByView?.year ?? 0, "year");
      default:
        return undefined;
    }
  };

  const getMaxDate = () => {
    switch (typeView) {
      case EStatisticsDateRangeType.DAY:
        return valueDateRange?.[0]
          ? dayjs(valueDateRange?.[0]).add(limitByView?.day ?? 0, "day")
          : dayjs().add(limitByView?.day ?? 0, "day");
      case EStatisticsDateRangeType.MONTH:
        return valueDateRange?.[1]
          ? dayjs(valueDateRange?.[0]).add(limitByView?.month ?? 0, "month")
          : dayjs().add(limitByView?.month ?? 0, "month");
      case EStatisticsDateRangeType.YEAR:
        return valueDateRange?.[0]
          ? dayjs(valueDateRange?.[0]).add(limitByView?.year ?? 0, "year")
          : dayjs().add(limitByView?.year ?? 0, "year");
      default:
        return undefined;
    }
  };

  return (
    <ClickAwayListener onClickAway={() => setActiveKey(null)}>
      <div className="flex flex-row gap-3 justify-start">
        <LocalizationProvider dateAdapter={AdapterDayjs}>
          <DesktopDatePicker
            {...props}
            disableFuture
            open={activeKey === 0}
            label={labelDateRange?.[0] ?? ""}
            sx={{...styleDatePicker, ...props?.sx}}
            slotProps={{
              openPickerButton: {
                onClick: (e) => {
                  setActiveKey(0);
                  e.stopPropagation();
                },
              },
              textField: {
                onBlur: () => setActiveKey(null),
                onClick: () => setActiveKey(0),
              },
            }}
            value={valueDateRange?.[0] ? dayjs(valueDateRange?.[0]) : null}
            views={
              typeView === EStatisticsDateRangeType.DAY
                ? ["day", "month", "year"]
                : typeView === EStatisticsDateRangeType.MONTH
                  ? ["month", "year"]
                  : ["year"]
            }
            onChange={(v) => {
              if (dayjs(v).isValid()) {
                onChangeStartDate?.(v);
              }
            }}
            maxDate={valueDateRange?.[1] ? dayjs(valueDateRange[1]) : dayjs()}
            minDate={limitByView ? getMinDate() : undefined}
            onAccept={handleAcceptDatePicker}
            format={
              typeView === EStatisticsDateRangeType.DAY
                ? "DD/MM/YYYY"
                : typeView === EStatisticsDateRangeType.MONTH
                  ? "MM/YYYY"
                  : "YYYY"
            }
          />
          <DesktopDatePicker
            {...props}
            open={activeKey === 1}
            disableFuture
            label={labelDateRange?.[1] ?? ""}
            sx={{...styleDatePicker, ...props?.sx}}
            value={valueDateRange?.[1] ? dayjs(valueDateRange?.[1]) : null}
            onChange={(v) => {
              if (dayjs(v).isValid()) {
                onChangeEndDate?.(v);
              }
            }}
            minDate={valueDateRange?.[0] ? dayjs(valueDateRange[0]) : dayjs()}
            views={
              typeView === EStatisticsDateRangeType.DAY
                ? ["day", "month", "year"]
                : typeView === EStatisticsDateRangeType.MONTH
                  ? ["month", "year"]
                  : ["year"]
            }
            maxDate={limitByView ? getMaxDate() : undefined}
            slotProps={{
              openPickerButton: {
                onClick: (e) => {
                  setActiveKey(1);
                  e.stopPropagation();
                },
              },
              textField: {
                onBlur: () => setActiveKey(null),
                onClick: () => setActiveKey(1),
              },
            }}
            onAccept={handleAcceptDatePicker}
            format={
              typeView === EStatisticsDateRangeType.DAY
                ? "DD/MM/YYYY"
                : typeView === EStatisticsDateRangeType.MONTH
                  ? "MM/YYYY"
                  : "YYYY"
            }
          />
        </LocalizationProvider>
      </div>
    </ClickAwayListener>
  );
}
