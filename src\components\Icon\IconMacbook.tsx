import {SVGProps} from "react";

function IconMacbook(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={25}
      height={24}
      viewBox="0 0 25 24"
      fill="none"
      {...props}
    >
      <g clipPath="url(#clip0_542_22098)">
        <path
          d="M2.11563 19.6667C1.74012 19.6667 1.4186 19.5259 1.15106 19.2443C0.883687 18.9629 0.75 18.6245 0.75 18.2292H4.12812C3.74479 18.2292 3.40937 18.0854 3.12188 17.7979C2.83438 17.5104 2.69062 17.175 2.69062 16.7917V4.8125C2.69062 4.42917 2.83438 4.09375 3.12188 3.80625C3.40937 3.51875 3.74479 3.375 4.12812 3.375H20.3719C20.7552 3.375 21.0906 3.51875 21.3781 3.80625C21.6656 4.09375 21.8094 4.42917 21.8094 4.8125V16.7917C21.8094 17.175 21.6656 17.5104 21.3781 17.7979C21.0906 18.0854 20.7552 18.2292 20.3719 18.2292H23.75C23.75 18.6285 23.6093 18.9679 23.3279 19.2474C23.0462 19.5269 22.7078 19.6667 22.3125 19.6667H2.11563ZM12.25 19.1396C12.4808 19.1396 12.6743 19.0616 12.8305 18.9055C12.9866 18.7493 13.0646 18.5558 13.0646 18.325C13.0646 18.0942 12.9866 17.9007 12.8305 17.7445C12.6743 17.5884 12.4808 17.5104 12.25 17.5104C12.0192 17.5104 11.8257 17.5884 11.6695 17.7445C11.5134 17.9007 11.4354 18.0942 11.4354 18.325C11.4354 18.5558 11.5134 18.7493 11.6695 18.9055C11.8257 19.0616 12.0192 19.1396 12.25 19.1396ZM4.12812 16.7917H20.3719V4.8125H4.12812V16.7917Z"
          fill="black"
        />
      </g>
      <defs>
        <clipPath id="clip0_542_22098">
          <rect
            width="24"
            height="24"
            fill="white"
            transform="translate(0.25)"
          />
        </clipPath>
      </defs>
    </svg>
  );
}

export default IconMacbook;
