import {IAutoFill, IParamsAutofill} from "@api/ApiAutofill";
import {MenuItem, OutlinedInput} from "@mui/material";
import Select, {SelectChangeEvent, SelectProps} from "@mui/material/Select";
import {keepPreviousData, useQuery} from "@tanstack/react-query";
import clsx from "clsx";
import {useEffect, useState} from "react";

interface ISelect {
  placeholder?: string | JSX.Element;
  selectProps?: SelectProps<string>;
  suggestionAPI: (data: IParamsAutofill) => Promise<IAutoFill[]>;
  onChange?: (selected: string) => void;
  name: string; // Make query key unique
  noValueStyle?: string; // Style text when nothing selected
}

export default function SelectAutofill(props: ISelect) {
  const [selected, setSelected] = useState<string>("");
  const [hasMore, setHasMore] = useState(true);
  const [params, setParams] = useState({
    name: "",
    page: 0,
    pageSize: 10,
  });
  const [suggestions, setSuggestions] = useState<
    Array<{name: string; id: string}>
  >([]);

  const getDataSuggestions = async (): Promise<IAutoFill[]> => {
    const data = await props.suggestionAPI(params);
    if (data.length < params.pageSize) {
      setHasMore(false);
    }
    return data;
  };

  const {data: newData} = useQuery({
    queryKey: ["listSuggestions", params, props.name],
    queryFn: getDataSuggestions,
    enabled: !!params.name || params.name === "",
    placeholderData: keepPreviousData,
  });

  const handleChange = (event: SelectChangeEvent) => {
    setSelected(event.target.value as string);
    if (props.onChange) {
      props.onChange(event.target.value as string);
    }
  };

  const getNameById = (id: string) => {
    const item = suggestions.find((s) => s.id === id);
    return item?.name || id;
  };

  const handleScroll = (event: React.UIEvent<HTMLDivElement, UIEvent>) => {
    const listboxNode = event.currentTarget;
    const scrollTop = listboxNode.scrollTop;
    const scrollHeight = listboxNode.scrollHeight;
    const clientHeight = listboxNode.clientHeight;

    if (scrollHeight - scrollTop === clientHeight && hasMore) {
      setParams((prev) => ({
        ...prev,
        page: prev.page + 1,
      }));
    }
  };

  useEffect(() => {
    if (newData) {
      setSuggestions((prevSuggestions) => {
        // When page = 0 (search or init value), reset suggestions
        if (params.page === 0) {
          return newData.map((item) => ({
            name: item?.name ?? "",
            id: item?.id ?? "",
          }));
        }

        // When load more page, append new data to previous data and remove duplicate
        const uniqueSuggestions = new Map(
          prevSuggestions.map((item) => [item.id, item]),
        );

        newData.forEach((item) => {
          const suggestion = {
            name: item?.name ?? "",
            id: item?.id ?? "",
          };
          uniqueSuggestions.set(item?.id || "", suggestion); // Set will remove duplicate items based on id
        });

        return Array.from(uniqueSuggestions.values());
      });
    }
  }, [newData, params.page]);

  return (
    <Select
      {...props.selectProps}
      displayEmpty
      value={selected}
      onChange={handleChange}
      input={<OutlinedInput />}
      renderValue={(selected) => {
        if (selected !== "") {
          return <span className="text-sm">{getNameById(selected)}</span>;
        } else {
          return (
            <span
              className={clsx(
                "text-[#242728] font-semibold text-sm",
                props.noValueStyle,
              )}
            >
              {props.placeholder || "-"}
            </span>
          );
        }
      }}
      inputProps={{"aria-label": "Without label"}}
      MenuProps={{
        PaperProps: {
          style: {
            maxHeight: 250,
          },
          sx: {
            "marginTop": "4px",
            "borderRadius": "8px",
            "& .MuiMenuItem-root": {
              fontSize: "14px",
            },
            "& .MuiMenuItem-root.Mui-selected:not(:first-child)": {
              backgroundColor: "#FFF4F2",
              fontSize: "14px",
              fontWeight: "bold",
            },
            "& .MuiMenuItem-root.Mui-selected:first-child": {
              backgroundColor: "#FFF",
              fontSize: "14px",
            },
          },
          onScroll: handleScroll,
        },
      }}
    >
      <MenuItem value="">
        <span>Xóa đã chọn</span>
      </MenuItem>
      {suggestions?.map((item) => (
        <MenuItem key={item?.id} value={item?.id}>
          {item?.name}
        </MenuItem>
      ))}
    </Select>
  );
}
