import {
  <PERSON>I<PERSON>,
  ListItemButton,
  ListItemIcon,
  ListItemText,
} from "@mui/material";
import {EGlobalModal, showModal} from "@redux/slices/GlobalModalSlice.ts";
import {IRootState} from "@redux/store.ts";
import clsx from "clsx";
import {useDispatch, useSelector} from "react-redux";
import {useLocation, useNavigate} from "react-router-dom";
import {IHomeRoute} from "./types.tsx";
import ChevronRightIcon from "@mui/icons-material/ChevronRight";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";

interface HomeRouteItemProps {
  item: IHomeRoute;
  onPress?: () => void;
}

export default function HomeRouteItem({item, onPress}: HomeRouteItemProps) {
  const {accessToken} = useSelector((state: IRootState) => state?.user);
  const location = useLocation();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const isFavoriteSelected = location.pathname.includes("/library/");
  const isSelected =
    location.pathname === item.pathname ||
    (item.pathname === "/library" && isFavoriteSelected);
  const isItemFavorite = item.pathname.includes("/library/");
  const shouldHighlight = isSelected && !isItemFavorite;
  const handleNavigate = (route: IHomeRoute) => {
    if (location.pathname !== route.pathname) {
      if (route?.isPrivate) {
        if (accessToken) {
          navigate(route.pathname);
        } else {
          dispatch(showModal(EGlobalModal.AUTH_MODAL));
        }
      } else {
        navigate(route.pathname);
      }
    }
  };

  return (
    <ListItem disablePadding>
      <div
        className={clsx(
          "absolute w-1 h-6 bg-orange-500 rounded-md shadow-[4px_4px_20px_0_#FF4319,-4px_-4px_20px_0_#FF43199E]",
          shouldHighlight ? "block" : "hidden",
        )}
      />
      <ListItemButton
        onClick={
          onPress
            ? onPress
            : () => {
                handleNavigate(item);
              }
        }
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          paddingY: "12px",
          marginLeft: "12px",
          borderTopLeftRadius: "10px",
          borderBottomLeftRadius: "10px",
        }}
        selected={shouldHighlight}
        classes={{selected: "!bg-[#FFFFFF0A]", root: "!pl-2 !pr-5"}}
      >
        <ListItemIcon
          sx={{
            color: shouldHighlight ? "white" : "#8C8989",
            width: "24px",
            height: "24px",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
          }}
          classes={{root: "!min-w-fit"}}
        >
          {item?.icon ?? (
            <div
              className={clsx(
                "p-[3px] mx-2.5 rounded-full",
                isSelected ? "bg-orange-500" : "bg-[#342F2F]",
              )}
            />
          )}
        </ListItemIcon>
        <ListItemText
          sx={{
            "color":
              shouldHighlight || (isItemFavorite && isSelected)
                ? "white"
                : "#8C8989",
            "marginBottom": 0,
            "whiteSpace": "nowrap",
            "marginLeft": 2,
            "& .MuiListItemText-primary": {
              overflow: "hidden",
              textOverflow: "ellipsis",
            },
          }}
          classes={{
            primary: `${
              shouldHighlight || (isItemFavorite && isSelected)
                ? "!font-semibold"
                : "font-normal"
            } !text-sm`,
          }}
          primary={<>{item.title}</>}
        />
        {item?.children && item?.children.length > 0 && (
          <ListItemIcon
            sx={{
              color: shouldHighlight ? "white" : "#8C8989",
              width: "24px",
              height: "24px",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
            }}
            classes={{root: "!min-w-fit"}}
          >
            {item?.open ? (
              <ExpandMoreIcon style={{color: "white"}} />
            ) : (
              <ChevronRightIcon />
            )}
          </ListItemIcon>
        )}
      </ListItemButton>
    </ListItem>
  );
}
