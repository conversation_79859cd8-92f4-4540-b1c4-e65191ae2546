import {SVGProps} from "react";

function IconHidden({
  width = "16",
  height = "16",
  ...props
}: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox={`0 0 ${width} ${height}`}
      className={props.className}
      fill="none"
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M3.99998 4.66732C3.99998 3.78326 4.35117 2.93542 4.97629 2.3103C5.60141 1.68517 6.44926 1.33398 7.33331 1.33398C8.21737 1.33398 9.06521 1.68517 9.69034 2.3103C10.3155 2.93542 10.6666 3.78326 10.6666 4.66732C10.6666 5.55137 10.3155 6.39922 9.69034 7.02434C9.06521 7.64946 8.21737 8.00065 7.33331 8.00065C6.44926 8.00065 5.60141 7.64946 4.97629 7.02434C4.35117 6.39922 3.99998 5.55137 3.99998 4.66732ZM7.33331 2.66732C7.07067 2.66732 6.8106 2.71905 6.56795 2.81956C6.32529 2.92007 6.10482 3.06739 5.9191 3.2531C5.73338 3.43882 5.58606 3.6593 5.48555 3.90195C5.38504 4.1446 5.33331 4.40467 5.33331 4.66732C5.33331 4.92996 5.38504 5.19003 5.48555 5.43268C5.58606 5.67534 5.73338 5.89581 5.9191 6.08153C6.10482 6.26725 6.32529 6.41457 6.56795 6.51508C6.8106 6.61559 7.07067 6.66732 7.33331 6.66732C7.86375 6.66732 8.37245 6.4566 8.74753 6.08153C9.1226 5.70646 9.33331 5.19775 9.33331 4.66732C9.33331 4.13688 9.1226 3.62818 8.74753 3.2531C8.37245 2.87803 7.86375 2.66732 7.33331 2.66732ZM7.51398 9.30732C7.52512 9.48367 7.4658 9.65722 7.34904 9.78986C7.23229 9.92249 7.06765 10.0033 6.89131 10.0147C5.64465 10.0947 4.54465 10.5 3.77065 11.008C3.38398 11.2613 3.09465 11.53 2.90865 11.778C2.71931 12.0313 2.66665 12.222 2.66665 12.334L2.66998 12.3753C2.68931 12.492 2.80798 12.702 3.42931 12.9153C4.09265 13.142 5.07398 13.27 6.26798 13.3153C6.35689 13.3165 6.44466 13.3354 6.52614 13.371C6.60761 13.4066 6.68114 13.4582 6.7424 13.5226C6.80366 13.5871 6.85141 13.6631 6.88283 13.7463C6.91426 13.8295 6.92873 13.9181 6.9254 14.0069C6.92206 14.0958 6.90099 14.1831 6.86341 14.2637C6.82584 14.3442 6.77252 14.4165 6.70661 14.4762C6.64069 14.5358 6.5635 14.5817 6.47959 14.6111C6.39567 14.6405 6.30672 14.6528 6.21798 14.6473C4.98998 14.6013 3.84998 14.4687 2.99665 14.176C2.18531 13.898 1.33331 13.3567 1.33331 12.334C1.33331 11.838 1.54665 11.372 1.84198 10.9787C2.14065 10.58 2.55398 10.2113 3.03931 9.89265C4.00998 9.25598 5.33398 8.77798 6.80598 8.68398C6.89341 8.67838 6.98108 8.69006 7.06399 8.71836C7.1469 8.74666 7.22342 8.79102 7.28917 8.84891C7.35492 8.9068 7.40862 8.97708 7.4472 9.05574C7.48577 9.1344 7.50846 9.21989 7.51398 9.30732ZM11.6073 13.8553C11.4249 13.8692 11.2417 13.8692 11.0593 13.8553L11.0013 14.072C10.9555 14.2428 10.8438 14.3884 10.6906 14.4768C10.5374 14.5652 10.3554 14.5891 10.1846 14.5433C10.0138 14.4975 9.86823 14.3858 9.77984 14.2326C9.69145 14.0794 9.66752 13.8975 9.71331 13.7267L9.77331 13.502C9.61242 13.422 9.45842 13.3287 9.31131 13.222L9.13798 13.3947C9.01225 13.5161 8.84384 13.5833 8.66905 13.5818C8.49425 13.5802 8.32704 13.5101 8.20343 13.3865C8.07983 13.2629 8.00972 13.0957 8.0082 12.9209C8.00668 12.7461 8.07387 12.5777 8.19531 12.452L8.41531 12.2313C8.26837 11.9792 8.15263 11.7101 8.07065 11.43C8.02043 11.2604 8.03963 11.0779 8.12402 10.9225C8.20842 10.767 8.35108 10.6515 8.52065 10.6013C8.69021 10.5511 8.87277 10.5703 9.02818 10.6547C9.18358 10.7391 9.2991 10.8818 9.34931 11.0513C9.93465 13.0267 12.732 13.0267 13.3173 11.0513C13.3422 10.9674 13.3833 10.8891 13.4384 10.8211C13.4935 10.753 13.5615 10.6965 13.6384 10.6547C13.7154 10.6129 13.7998 10.5867 13.8869 10.5775C13.974 10.5684 14.062 10.5765 14.146 10.6013C14.2299 10.6262 14.3082 10.6673 14.3762 10.7224C14.4443 10.7775 14.5008 10.8455 14.5426 10.9225C14.5844 10.9994 14.6106 11.0838 14.6198 11.1709C14.6289 11.258 14.6208 11.346 14.596 11.43C14.5093 11.7233 14.392 11.9907 14.2506 12.2313L14.4713 12.4513C14.5964 12.5763 14.6667 12.7459 14.6668 12.9227C14.6668 13.0996 14.5967 13.2692 14.4716 13.3943C14.3466 13.5194 14.1771 13.5897 14.0002 13.5898C13.8234 13.5898 13.6537 13.5197 13.5286 13.3947L13.3553 13.2213C13.2082 13.328 13.0542 13.4215 12.8933 13.502L12.9533 13.7267C12.9991 13.8975 12.9752 14.0794 12.8868 14.2326C12.7984 14.3858 12.6528 14.4975 12.482 14.5433C12.3112 14.5891 12.1292 14.5652 11.976 14.4768C11.8229 14.3884 11.7111 14.2428 11.6653 14.072L11.6073 13.8553Z"
        fill={props.stroke || "#ffffff"}
        stroke={props.stroke || "#ffffff"}
        strokeWidth="0.3"
      />
    </svg>
  );
}

export default IconHidden;
