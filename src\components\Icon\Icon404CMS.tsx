import {SVGProps} from "react";

function Icon404CMS({
  width = "853",
  height = "728",
  ...props
}: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox={`0 0 ${width} ${height}`}
      className={props.className}
      fill="none"
      {...props}
    >
      <g filter="url(#filter0_f_1_35271)">
        <circle cx="149.5" cy="593.5" r="2.5" fill="white" />
      </g>
      <g filter="url(#filter1_f_1_35271)">
        <circle cx="347.5" cy="8.5" r="2.5" fill="white" />
      </g>
      <g filter="url(#filter2_f_1_35271)">
        <circle cx="632.5" cy="719.5" r="2.5" fill="white" />
      </g>
      <path
        d="M836.763 496.201L836.765 496.204L839.984 501.75C839.984 501.75 839.985 501.75 839.985 501.751C840.219 502.157 840.548 502.5 840.944 502.751C841.341 503.001 841.793 503.151 842.261 503.187L842.263 503.187L848.662 503.656C848.662 503.656 848.662 503.656 848.663 503.656C849.017 503.683 849.356 503.808 849.643 504.018C849.929 504.228 850.151 504.514 850.283 504.844C850.415 505.174 850.452 505.534 850.39 505.884C850.328 506.233 850.169 506.559 849.931 506.822L849.929 506.824L845.651 511.615C845.651 511.615 845.651 511.616 845.65 511.616C845.336 511.965 845.112 512.385 844.996 512.841C844.881 513.296 844.878 513.773 844.989 514.23C844.989 514.23 844.989 514.23 844.989 514.23L846.488 520.434L846.488 520.435C846.572 520.779 846.558 521.14 846.447 521.477C846.337 521.814 846.134 522.113 845.862 522.34C845.59 522.568 845.26 522.715 844.909 522.765C844.559 522.814 844.201 522.765 843.877 522.622C843.877 522.622 843.876 522.622 843.876 522.622L838.002 520.029C838.001 520.028 838.001 520.028 838 520.028C837.571 519.837 837.102 519.754 836.634 519.785C836.165 519.817 835.711 519.962 835.312 520.209C835.312 520.21 835.311 520.21 835.311 520.21L829.837 523.569L829.835 523.57C829.533 523.757 829.184 523.856 828.829 523.855C828.473 523.853 828.125 523.753 827.824 523.563C827.523 523.374 827.281 523.104 827.126 522.784C826.971 522.465 826.909 522.108 826.946 521.754L826.946 521.753L827.608 515.403L827.608 515.402C827.656 514.935 827.59 514.462 827.414 514.026C827.239 513.591 826.96 513.204 826.601 512.9C826.601 512.9 826.601 512.9 826.601 512.9L821.718 508.727L821.716 508.725C821.445 508.496 821.244 508.195 821.135 507.857C821.026 507.518 821.015 507.157 821.102 506.812C821.188 506.468 821.37 506.154 821.626 505.908L821.279 505.548L821.626 505.908C821.881 505.661 822.201 505.492 822.548 505.418C822.548 505.418 822.548 505.418 822.548 505.418L828.827 504.094L828.829 504.093C829.287 503.994 829.715 503.785 830.075 503.484C830.434 503.182 830.714 502.798 830.891 502.364C830.891 502.364 830.892 502.363 830.892 502.363L833.337 496.435L833.337 496.435L833.338 496.432C833.473 496.1 833.698 495.813 833.988 495.603C834.278 495.394 834.621 495.27 834.979 495.245C835.336 495.221 835.693 495.298 836.008 495.467C836.324 495.636 836.585 495.891 836.763 496.201Z"
        stroke="#A9A9A9"
      />
      <path
        d="M236.674 258.403L238.792 262.695L243.528 263.384L240.101 266.724L240.91 271.442L236.674 269.215L232.437 271.442L233.247 266.724L229.818 263.384L234.555 262.695L236.674 258.403Z"
        fill="#828282"
      />
      <path
        d="M664.808 474.707L666.927 478.999L671.662 479.688L668.235 483.028L669.045 487.745L664.808 485.518L660.572 487.745L661.38 483.028L657.953 479.688L662.69 478.999L664.808 474.707Z"
        fill="#BABABA"
      />
      <path
        d="M576.677 585.011L578.796 589.302L583.533 589.99L580.104 593.332L580.914 598.049L576.677 595.822L572.441 598.049L573.251 593.332L569.822 589.99L574.559 589.302L576.677 585.011Z"
        fill="#A9A9A9"
      />
      <path
        d="M388.714 196.556L389.5 198.148L391.258 198.404C391.697 198.468 391.872 199.007 391.554 199.317L390.282 200.558L390.582 202.309C390.656 202.747 390.198 203.079 389.806 202.874L388.233 202.047L386.66 202.874C386.267 203.081 385.808 202.747 385.884 202.309L386.184 200.558L384.912 199.317C384.595 199.008 384.769 198.468 385.208 198.404L386.966 198.148L387.752 196.556C387.95 196.157 388.517 196.157 388.714 196.556Z"
        fill="#828282"
      />
      <path
        d="M168.81 558.85L170.954 563.381L175.749 564.108C176.946 564.29 177.425 565.822 176.556 566.705L173.087 570.234L173.905 575.216C174.109 576.461 172.859 577.408 171.79 576.823L167.499 574.471L163.208 576.823C162.136 577.408 160.886 576.461 161.093 575.216L161.911 570.234L158.442 566.705C157.577 565.826 158.053 564.29 159.249 564.108L164.044 563.381L166.188 558.85C166.727 557.717 168.275 557.717 168.81 558.85Z"
        fill="#A9A9A9"
      />
      <path
        d="M207.686 179.719L208.472 181.312L210.23 181.567C210.669 181.631 210.844 182.17 210.526 182.48L209.254 183.721L209.554 185.472C209.628 185.91 209.17 186.242 208.777 186.035L207.204 185.209L205.632 186.035C205.239 186.242 204.78 185.908 204.855 185.472L205.155 183.721L203.883 182.48C203.566 182.171 203.74 181.631 204.179 181.567L205.937 181.312L206.723 179.719C206.922 179.32 207.489 179.32 207.686 179.719Z"
        fill="#909090"
      />
      <path
        d="M205.557 495.452C205.557 496.412 204.779 497.191 203.818 497.191C202.858 497.191 202.08 496.412 202.08 495.452C202.08 494.492 202.858 493.714 203.818 493.714C204.777 493.714 205.557 494.492 205.557 495.452Z"
        fill="#828282"
      />
      <path
        d="M453.856 277.436C453.856 278.396 453.078 279.174 452.117 279.174C451.157 279.174 450.379 278.396 450.379 277.436C450.379 276.475 451.157 275.697 452.117 275.697C453.078 275.697 453.856 276.475 453.856 277.436Z"
        fill="#828282"
      />
      <path
        d="M363.512 181.158C363.512 182.119 362.734 182.897 361.774 182.897C360.813 182.897 360.035 182.119 360.035 181.158C360.035 180.198 360.813 179.42 361.774 179.42C362.734 179.42 363.512 180.199 363.512 181.158Z"
        fill="#8F8F8F"
      />
      <path
        d="M362.154 591.471C367.328 590.249 370.532 585.065 369.31 579.891C368.088 574.717 362.904 571.514 357.73 572.735C352.556 573.957 349.353 579.141 350.574 584.315C351.796 589.489 356.981 592.693 362.154 591.471Z"
        fill="#DCDCDC"
      />
      <path
        d="M356.595 576.527C354.551 576.527 352.652 577.182 351.106 578.309C350.595 579.488 350.307 580.785 350.307 582.147C350.307 587.466 354.617 591.763 359.935 591.763C361.73 591.763 363.42 591.265 364.861 590.402C364.848 590.388 364.848 590.388 364.861 590.388C365.581 589.052 365.987 587.533 365.987 585.908C365.987 580.732 361.782 576.527 356.595 576.527Z"
        fill="#CBCBCB"
      />
      <path
        d="M282.033 414.379H202.312V378.387L282.033 283.625H320.173V380.427H339.941V414.379H320.173V443.817H282.033V414.379ZM282.033 380.427V330.86L239.908 380.427H282.033Z"
        fill="#808080"
      />
      <path
        d="M353.588 364.419C353.588 334.481 358.976 313.531 369.757 301.568C380.537 289.606 396.955 283.625 419.018 283.625C429.618 283.625 438.321 284.934 445.125 287.547C451.93 290.162 457.481 293.564 461.778 297.753C466.075 301.944 469.46 306.348 471.931 310.969C474.403 315.588 476.39 320.978 477.894 327.139C480.829 338.886 482.298 351.135 482.298 363.883C482.298 392.461 477.463 413.378 467.794 426.627C458.123 439.879 441.471 446.503 417.834 446.503C404.582 446.503 393.874 444.388 385.71 440.163C377.544 435.939 370.846 429.742 365.619 421.577C361.82 415.777 358.866 407.843 356.755 397.78C354.642 387.716 353.588 376.597 353.588 364.419ZM396.993 364.527C396.993 384.583 398.766 398.281 402.311 405.624C405.857 412.965 410.996 416.636 417.728 416.636C422.169 416.636 426.019 415.078 429.278 411.962C432.538 408.847 434.936 403.924 436.478 397.19C438.017 390.458 438.788 379.964 438.788 365.71C438.788 344.797 437.015 330.739 433.47 323.541C429.924 316.341 424.607 312.742 417.515 312.742C410.279 312.742 405.051 316.414 401.827 323.756C398.605 331.097 396.993 344.688 396.993 364.527Z"
        fill="#808080"
      />
      <path
        d="M575.557 414.379H495.837V378.387L575.557 283.625H613.698V380.427H633.466V414.379H613.698V443.817H575.557V414.379ZM575.557 380.427V330.86L533.434 380.427H575.557Z"
        fill="#808080"
      />
      <g opacity="0.3">
        <path
          d="M351.819 228.76C351.819 230.109 350.725 231.203 349.376 231.203C348.026 231.203 346.933 230.109 346.933 228.76C346.933 227.41 348.026 226.316 349.376 226.316C350.725 226.316 351.819 227.41 351.819 228.76Z"
          fill="white"
          fillOpacity="0.28"
        />
        <path
          d="M184.157 322.862C185.114 322.862 185.89 322.086 185.89 321.129C185.89 320.171 185.114 319.396 184.157 319.396C183.2 319.396 182.424 320.171 182.424 321.129C182.424 322.086 183.2 322.862 184.157 322.862Z"
          fill="white"
          fillOpacity="0.28"
        />
        <path
          d="M469.853 575.396C471.222 575.396 472.333 574.286 472.333 572.916C472.333 571.547 471.222 570.437 469.853 570.437C468.483 570.437 467.373 571.547 467.373 572.916C467.373 574.286 468.483 575.396 469.853 575.396Z"
          fill="white"
          fillOpacity="0.28"
        />
        <path
          d="M529.384 474.653C530.341 474.653 531.117 473.877 531.117 472.92C531.117 471.962 530.341 471.187 529.384 471.187C528.426 471.187 527.65 471.962 527.65 472.92C527.65 473.877 528.426 474.653 529.384 474.653Z"
          fill="white"
          fillOpacity="0.28"
        />
        <path
          d="M644.238 163.26C644.238 164.218 643.462 164.994 642.505 164.994C641.547 164.994 640.771 164.218 640.771 163.26C640.771 162.303 641.547 161.527 642.505 161.527C643.462 161.527 644.238 162.303 644.238 163.26Z"
          fill="white"
          fillOpacity="0.28"
        />
        <path
          d="M314.879 128.342C314.879 129.299 314.104 130.075 313.146 130.075C312.189 130.075 311.413 129.299 311.413 128.342C311.413 127.384 312.189 126.608 313.146 126.608C314.104 126.608 314.879 127.384 314.879 128.342Z"
          fill="white"
          fillOpacity="0.28"
        />
        <path
          d="M452.352 219.128C453.309 219.128 454.085 218.352 454.085 217.395C454.085 216.438 453.309 215.662 452.352 215.662C451.395 215.662 450.619 216.438 450.619 217.395C450.619 218.352 451.395 219.128 452.352 219.128Z"
          fill="white"
          fillOpacity="0.28"
        />
      </g>
      <path
        d="M273.587 219.135C289.439 219.135 302.289 206.284 302.289 190.433C302.289 174.581 289.439 161.73 273.587 161.73C257.735 161.73 244.885 174.581 244.885 190.433C244.885 206.284 257.735 219.135 273.587 219.135Z"
        fill="white"
        fillOpacity="0.28"
      />
      <path
        opacity="0.7"
        d="M273.587 219.135C289.439 219.135 302.289 206.284 302.289 190.433C302.289 174.581 289.439 161.73 273.587 161.73C257.735 161.73 244.885 174.581 244.885 190.433C244.885 206.284 257.735 219.135 273.587 219.135Z"
        fill="#767676"
        fillOpacity="0.5"
      />
      <path
        opacity="0.2"
        d="M260.843 176.366C260.843 177.815 259.668 178.99 258.219 178.99C256.77 178.99 255.595 177.815 255.595 176.366C255.595 174.917 256.77 173.742 258.219 173.742C259.668 173.742 260.843 174.917 260.843 176.366Z"
        fill="white"
        fillOpacity="0.28"
      />
      <path
        opacity="0.2"
        d="M291.706 173.742C291.706 175.191 290.531 176.366 289.082 176.366C287.633 176.366 286.458 175.191 286.458 173.742C286.458 172.293 287.633 171.118 289.082 171.118C290.531 171.119 291.706 172.293 291.706 173.742Z"
        fill="white"
        fillOpacity="0.28"
      />
      <path
        opacity="0.2"
        d="M286.458 192.45C286.458 194.822 284.536 196.745 282.163 196.745C279.792 196.745 277.869 194.822 277.869 192.45C277.869 190.079 279.791 188.156 282.163 188.156C284.534 188.156 286.458 190.079 286.458 192.45Z"
        fill="white"
        fillOpacity="0.28"
      />
      <path
        opacity="0.2"
        d="M272.813 204.692C272.813 207.862 270.243 210.431 267.074 210.431C263.904 210.431 261.335 207.861 261.335 204.692C261.335 201.522 263.905 198.953 267.074 198.953C270.243 198.953 272.813 201.523 272.813 204.692Z"
        fill="white"
        fillOpacity="0.28"
      />
      <path
        opacity="0.2"
        d="M263.695 168.776C259.672 168.776 255.854 169.608 252.376 171.095C247.725 176.195 244.888 182.987 244.888 190.433C244.888 206.28 257.735 219.128 273.582 219.128C277.618 219.128 281.464 218.296 284.957 216.796C284.957 216.796 284.957 216.796 284.957 216.783C289.58 211.681 292.403 204.917 292.403 197.485C292.404 181.624 279.557 168.776 263.695 168.776Z"
        fill="black"
        fillOpacity="0.25"
      />
      <path
        d="M613.759 536.722C616.956 523.185 608.573 509.619 595.036 506.423C581.499 503.226 567.933 511.609 564.737 525.146C561.54 538.683 569.923 552.248 583.46 555.445C596.997 558.641 610.563 550.259 613.759 536.722Z"
        fill="white"
        fillOpacity="0.28"
      />
      <g opacity="0.3">
        <path
          d="M613.759 536.722C616.956 523.185 608.573 509.619 595.036 506.423C581.499 503.226 567.933 511.609 564.737 525.146C561.54 538.683 569.923 552.248 583.46 555.445C596.997 558.641 610.563 550.259 613.759 536.722Z"
          fill="#1E1E1E"
          fillOpacity="0.5"
        />
        <path
          d="M594.921 506.909C608.189 510.042 616.406 523.338 613.273 536.607C610.139 549.875 596.844 558.091 583.575 554.958C570.307 551.825 562.091 538.529 565.224 525.261C568.357 511.993 581.653 503.776 594.921 506.909Z"
          stroke="#1E1E1E"
          strokeOpacity="0.5"
        />
      </g>
      <path
        opacity="0.4"
        d="M605.148 511.354C602.857 510.658 600.416 510.29 597.893 510.29C583.982 510.29 572.703 521.569 572.703 535.479C572.703 543.362 576.317 550.385 581.991 555.008C584.282 555.704 586.723 556.072 589.247 556.072C603.158 556.072 614.436 544.793 614.436 530.883C614.436 523.001 610.822 515.964 605.148 511.354Z"
        fill="#747474"
        fillOpacity="0.28"
      />
      <path
        d="M565.189 538.352C538.289 551.009 558.356 560.765 596.252 547.146C631.339 534.536 644.678 518.629 613.35 523.558C614.966 530.25 571.021 547.373 565.189 538.352Z"
        fill="#A2A2A2"
        fillOpacity="0.28"
      />
      <path
        d="M329.933 511.571C345.742 511.571 379.369 511.571 379.369 511.571L383.385 377.152C350.51 391.916 329.934 464.201 329.933 511.571Z"
        fill="#B4B4B4"
      />
      <path
        d="M502.328 511.571C486.519 511.571 452.892 511.571 452.892 511.571L448.877 377.153C481.751 391.916 502.328 464.2 502.328 511.571Z"
        fill="#B4B4B4"
      />
      <path
        d="M441.153 261.213L416.13 215.43L391.107 261.213C383.068 286.577 378.584 312.932 377.781 339.527L372.587 511.569L459.67 511.569L454.477 339.527C453.676 312.932 449.192 286.577 441.153 261.213Z"
        fill="white"
        fillOpacity="0.28"
      />
      <path
        d="M441.152 261.213L416.129 214.601L391.106 261.213C383.067 286.578 378.583 312.933 377.78 339.528L372.587 511.569L459.669 511.569L454.476 339.527C453.675 312.932 449.191 286.578 441.152 261.213Z"
        fill="#CDCDCD"
      />
      <path
        d="M434.348 365.135C444.409 355.074 444.409 338.762 434.348 328.701C424.287 318.64 407.975 318.64 397.914 328.701C387.852 338.762 387.852 355.074 397.914 365.135C407.975 375.196 424.287 375.196 434.348 365.135Z"
        fill="white"
      />
      <path
        d="M427.83 358.621C434.292 352.159 434.292 341.681 427.83 335.219C421.368 328.757 410.891 328.757 404.428 335.219C397.966 341.681 397.966 352.159 404.428 358.621C410.891 365.083 421.368 365.083 427.83 358.621Z"
        fill="white"
        fillOpacity="0.28"
      />
      <path
        opacity="0.3"
        d="M458.756 481.207L373.505 481.208L375.549 413.453L456.711 413.453L458.756 481.207Z"
        fill="white"
      />
      <path
        d="M467.249 544.694L365.01 544.694L386.352 511.57L445.908 511.569L467.249 544.694Z"
        fill="white"
        fillOpacity="0.28"
      />
      <path
        d="M531.628 148.359L533.594 151.748C533.712 151.954 533.878 152.128 534.079 152.254C534.28 152.38 534.508 152.454 534.745 152.471L538.654 152.771C538.926 152.792 539.187 152.889 539.407 153.051C539.628 153.213 539.798 153.433 539.9 153.687C540.002 153.941 540.03 154.218 539.983 154.487C539.935 154.756 539.814 155.006 539.631 155.21L537.015 158.128C536.854 158.305 536.74 158.52 536.682 158.753C536.625 158.986 536.626 159.229 536.685 159.461L537.598 163.269C537.661 163.534 537.648 163.812 537.562 164.072C537.476 164.331 537.32 164.561 537.111 164.736C536.901 164.912 536.647 165.025 536.377 165.065C536.107 165.104 535.831 165.067 535.58 164.959L531.998 163.371C531.781 163.276 531.544 163.234 531.308 163.25C531.071 163.266 530.842 163.339 530.64 163.463L527.315 165.503C527.082 165.647 526.813 165.722 526.539 165.721C526.265 165.719 525.997 165.641 525.765 165.495C525.533 165.349 525.347 165.14 525.227 164.894C525.108 164.647 525.06 164.372 525.089 164.099L525.482 160.204C525.507 159.965 525.473 159.724 525.384 159.502C525.295 159.28 525.153 159.082 524.97 158.927L521.992 156.387C521.784 156.209 521.63 155.977 521.546 155.717C521.463 155.457 521.455 155.178 521.521 154.913C521.588 154.648 521.727 154.407 521.924 154.217C522.12 154.027 522.366 153.896 522.633 153.838L526.46 153.016C526.693 152.968 526.91 152.863 527.093 152.711C527.276 152.559 527.419 152.365 527.51 152.145L529.003 148.523C529.108 148.271 529.282 148.052 529.505 147.893C529.728 147.735 529.991 147.641 530.264 147.624C530.538 147.607 530.81 147.667 531.051 147.797C531.292 147.927 531.492 148.122 531.628 148.359Z"
        fill="#A9A9A9"
      />
      <path
        d="M671.061 74.3415L671.064 74.3421L681.29 76.2782C681.29 76.2783 681.29 76.2783 681.29 76.2783C681.988 76.4136 682.708 76.3766 683.388 76.1704C684.068 75.9641 684.687 75.5948 685.192 75.0946L685.194 75.0933L692.563 67.7371C692.563 67.7369 692.563 67.7368 692.564 67.7366C693.013 67.2893 693.58 66.977 694.198 66.8352C694.817 66.6933 695.463 66.7278 696.063 66.9346C696.663 67.1414 697.193 67.5123 697.592 68.0052C697.992 68.498 698.246 69.0932 698.324 69.7229L698.325 69.7252L699.663 80.0629C699.663 80.0632 699.663 80.0634 699.663 80.0637C699.751 80.7701 700.011 81.4443 700.418 82.028C700.826 82.6118 701.37 83.0875 702.003 83.4136L711.209 88.1596L711.21 88.1599C711.773 88.4493 712.244 88.8905 712.57 89.4336C712.895 89.9767 713.063 90.6001 713.052 91.2332C713.042 91.8663 712.855 92.484 712.512 93.0164C712.169 93.5488 711.684 93.9746 711.112 94.2458L701.691 98.7036C701.691 98.7037 701.691 98.7039 701.691 98.704C701.046 99.0061 700.485 99.461 700.057 100.029C699.629 100.598 699.346 101.262 699.233 101.965C699.233 101.965 699.233 101.965 699.233 101.966L697.545 112.25L697.544 112.252C697.444 112.88 697.17 113.466 696.753 113.945C696.336 114.424 695.792 114.777 695.185 114.962C694.577 115.148 693.93 115.159 693.316 114.995C692.703 114.831 692.147 114.498 691.714 114.034L691.713 114.033L684.622 106.48L684.622 106.48C684.134 105.961 683.526 105.569 682.853 105.338C682.179 105.107 681.46 105.044 680.757 105.154C680.756 105.154 680.756 105.154 680.756 105.154L670.449 106.717L670.447 106.717C669.82 106.815 669.178 106.735 668.594 106.487C668.01 106.239 667.508 105.831 667.143 105.312C666.779 104.792 666.568 104.18 666.534 103.547C666.5 102.913 666.644 102.283 666.95 101.727C666.95 101.727 666.95 101.727 666.95 101.726L671.99 92.6151L671.991 92.6134C672.333 91.99 672.517 91.2927 672.528 90.582C672.539 89.8717 672.376 89.1696 672.054 88.5364C672.054 88.5361 672.053 88.5357 672.053 88.5354L667.374 79.2398L667.372 79.2372C667.081 78.6678 666.956 78.0282 667.012 77.3913C667.067 76.7544 667.301 76.1459 667.685 75.6354C668.07 75.1248 668.591 74.7327 669.188 74.5039C669.785 74.2751 670.434 74.2189 671.061 74.3415Z"
        stroke="#A9A9A9"
      />
      <path
        d="M246.702 717.404C258.136 717.404 267.404 708.136 267.404 696.702C267.404 685.269 258.136 676 246.702 676C235.269 676 226 685.269 226 696.702C226 708.136 235.269 717.404 246.702 717.404Z"
        fill="white"
        fillOpacity="0.28"
      />
      <path
        opacity="0.7"
        d="M266.904 696.702C266.904 707.859 257.859 716.904 246.702 716.904C235.545 716.904 226.5 707.859 226.5 696.702C226.5 685.545 235.545 676.5 246.702 676.5C257.859 676.5 266.904 685.545 266.904 696.702Z"
        stroke="#A6A6A6"
      />
      <path
        d="M237.01 686.557C237.01 687.326 236.386 687.949 235.617 687.949C234.848 687.949 234.225 687.326 234.225 686.557C234.225 685.788 234.848 685.164 235.617 685.164C236.386 685.164 237.01 685.788 237.01 686.557Z"
        stroke="#9F9F9F"
        strokeOpacity="0.5"
      />
      <path
        d="M259.27 684.664C259.27 685.433 258.647 686.057 257.878 686.057C257.109 686.057 256.485 685.433 256.485 684.664C256.485 683.895 257.109 683.272 257.878 683.271C258.647 683.272 259.27 683.896 259.27 684.664Z"
        stroke="#9F9F9F"
        strokeOpacity="0.5"
      />
      <path
        d="M255.486 698.158C255.486 699.592 254.323 700.755 252.888 700.755C251.454 700.755 250.291 699.592 250.291 698.158C250.291 696.724 251.453 695.561 252.888 695.561C254.322 695.561 255.486 696.724 255.486 698.158Z"
        stroke="#9F9F9F"
        strokeOpacity="0.5"
      />
      <path
        d="M245.644 706.987C245.644 708.998 244.014 710.627 242.005 710.627C239.994 710.627 238.365 708.997 238.365 706.987C238.365 704.977 239.995 703.348 242.005 703.348C244.014 703.348 245.644 704.978 245.644 706.987Z"
        stroke="#9F9F9F"
        strokeOpacity="0.5"
      />
      <path
        d="M76.8518 328.579C89.0834 317.286 89.8446 298.216 78.552 285.985C67.2594 273.753 48.1893 272.992 35.9577 284.284C23.7262 295.577 22.965 314.647 34.2576 326.879C45.5501 339.11 64.6202 339.871 76.8518 328.579Z"
        fill="white"
        fillOpacity="0.28"
      />
      <path
        opacity="0.5"
        d="M78.1846 286.324C89.2899 298.352 88.5413 317.106 76.5126 328.211C64.4839 339.317 45.7302 338.568 34.6249 326.539C23.5196 314.511 24.2682 295.757 36.2969 284.652C48.3256 273.547 67.0793 274.295 78.1846 286.324Z"
        stroke="#1E1E1E"
      />
      <path
        opacity="0.5"
        d="M52.6264 326.72C40.9779 323.766 31.4189 320.112 24.1835 316.384C16.9335 312.649 12.0623 308.863 9.73583 305.668C8.57122 304.069 8.08941 302.68 8.18624 301.555C8.27996 300.466 8.92703 299.514 10.2969 298.78C11.6837 298.036 13.7783 297.541 16.6509 297.409C19.4253 297.281 22.8906 297.493 27.0786 298.121C27.1899 299.756 28.0891 301.327 29.4954 302.797C31.0315 304.403 33.2214 305.941 35.8301 307.379C41.0506 310.257 48.0423 312.787 55.1112 314.666C62.1817 316.546 69.362 317.784 74.9687 318.065C77.7683 318.206 80.2029 318.11 82.0438 317.723C82.9643 317.529 83.7601 317.258 84.3845 316.891C84.8384 316.624 85.2162 316.297 85.478 315.901C89.5554 317.826 92.7769 319.61 95.2165 321.239C97.7492 322.93 99.4089 324.433 100.317 325.725C101.223 327.013 101.327 328.003 100.957 328.768C100.566 329.576 99.5657 330.318 97.836 330.892C94.397 332.035 88.5159 332.374 80.7455 331.727C72.9939 331.081 63.4188 329.458 52.6264 326.72Z"
        stroke="#1E1E1E"
      />
      <defs>
        <filter
          id="filter0_f_1_35271"
          x="141"
          y="585"
          width="17"
          height="17"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="BackgroundImageFix"
            result="shape"
          />
          <feGaussianBlur
            stdDeviation="3"
            result="effect1_foregroundBlur_1_35271"
          />
        </filter>
        <filter
          id="filter1_f_1_35271"
          x="339"
          y="0"
          width="17"
          height="17"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="BackgroundImageFix"
            result="shape"
          />
          <feGaussianBlur
            stdDeviation="3"
            result="effect1_foregroundBlur_1_35271"
          />
        </filter>
        <filter
          id="filter2_f_1_35271"
          x="624"
          y="711"
          width="17"
          height="17"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="BackgroundImageFix"
            result="shape"
          />
          <feGaussianBlur
            stdDeviation="3"
            result="effect1_foregroundBlur_1_35271"
          />
        </filter>
      </defs>
    </svg>
  );
}

export default Icon404CMS;
