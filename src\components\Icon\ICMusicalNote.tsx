import {SVGProps} from "react";

function ICMusicalNote({
  width = "25",
  height = "24",
  ...props
}: SVGProps<SVGSVGElement>) {
  {
    return (
      <svg
        className={props.className}
        width={width}
        height={height}
        viewBox={`0 0 25 24`}
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M22.15 2.23991C22.041 2.14636 21.9131 2.0774 21.7751 2.03771C21.637 1.99802 21.492 1.98854 21.35 2.00991L8.35 4.00991C8.11326 4.04582 7.89727 4.1655 7.74129 4.34717C7.5853 4.52884 7.49969 4.76046 7.5 4.99991V15.3499C7.03277 15.1217 6.51999 15.002 6 14.9999C5.30777 14.9999 4.63108 15.2052 4.05551 15.5898C3.47993 15.9743 3.03133 16.521 2.76642 17.1605C2.50152 17.8001 2.4322 18.5038 2.56725 19.1827C2.7023 19.8617 3.03564 20.4853 3.52513 20.9748C4.01461 21.4643 4.63825 21.7976 5.31719 21.9327C5.99612 22.0677 6.69985 21.9984 7.33939 21.7335C7.97894 21.4686 8.52556 21.02 8.91015 20.4444C9.29473 19.8688 9.5 19.1921 9.5 18.4999V10.8599L20.5 9.16991V13.3499C20.0328 13.1217 19.52 13.002 19 12.9999C18.3078 12.9999 17.6311 13.2052 17.0555 13.5898C16.4799 13.9743 16.0313 14.521 15.7664 15.1605C15.5015 15.8001 15.4322 16.5038 15.5673 17.1827C15.7023 17.8617 16.0356 18.4853 16.5251 18.9748C17.0146 19.4643 17.6383 19.7976 18.3172 19.9327C18.9961 20.0677 19.6999 19.9984 20.3394 19.7335C20.9789 19.4686 21.5256 19.02 21.9101 18.4444C22.2947 17.8688 22.5 17.1921 22.5 16.4999V2.99991C22.5 2.85547 22.4687 2.71274 22.4083 2.58154C22.3479 2.45034 22.2598 2.33379 22.15 2.23991ZM6 19.9999C5.70333 19.9999 5.41332 19.9119 5.16665 19.7471C4.91997 19.5823 4.72771 19.348 4.61418 19.0739C4.50065 18.7998 4.47095 18.4982 4.52882 18.2073C4.5867 17.9163 4.72956 17.649 4.93934 17.4392C5.14912 17.2295 5.41639 17.0866 5.70737 17.0287C5.99834 16.9709 6.29994 17.0006 6.57403 17.1141C6.84812 17.2276 7.08238 17.4199 7.24721 17.6666C7.41203 17.9132 7.5 18.2032 7.5 18.4999C7.5 18.8977 7.34197 19.2793 7.06066 19.5606C6.77936 19.8419 6.39783 19.9999 6 19.9999ZM19 17.9999C18.7033 17.9999 18.4133 17.9119 18.1666 17.7471C17.92 17.5823 17.7277 17.348 17.6142 17.0739C17.5007 16.7998 17.4709 16.4982 17.5288 16.2073C17.5867 15.9163 17.7296 15.649 17.9393 15.4392C18.1491 15.2295 18.4164 15.0866 18.7074 15.0287C18.9983 14.9709 19.2999 15.0006 19.574 15.1141C19.8481 15.2276 20.0824 15.4199 20.2472 15.6666C20.412 15.9132 20.5 16.2032 20.5 16.4999C20.5 16.8977 20.342 17.2793 20.0607 17.5606C19.7794 17.8419 19.3978 17.9999 19 17.9999ZM20.5 7.13991L9.5 8.82991V5.82991L20.5 4.16991V7.13991Z"
          fill="white"
        />
      </svg>
    );
  }
}

export default ICMusicalNote;
