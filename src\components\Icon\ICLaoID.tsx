import {SVGProps} from "react";
const ICLaoID = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={30}
    height={31}
    fill="none"
    {...props}
  >
    <circle cx={15.001} cy={15.258} r={15.001} fill="#002868" />
    <path
      fill="#FF001C"
      fillRule="evenodd"
      d="M27.797 23.11H2.195c2.637 4.305 7.384 7.176 12.8 7.176 5.418 0 10.165-2.872 12.802-7.176ZM2.196 7.434h25.6C25.16 3.129 20.415.258 14.997.258c-5.417 0-10.164 2.871-12.8 7.176Z"
      clipRule="evenodd"
    />
    <circle cx={17.468} cy={15.421} r={4.097} fill="#FF001C" />
    <path
      fill="#fff"
      d="M9.448 9.949c.07-.02.138-.043.207-.069.246-.026.499-.*************.059.395.16.552.302l.22.244c.*************.095.24l.04-.003.029.159c.04.126.048.257.047.389-.002 2.69.002 5.382-.002 *************-.04.27-.075.402-.079.14-.16.278-.243.416-.049.043-.098.086-.147.127l-.01.018a6.52 6.52 0 0 0-.254.163c-.07.025-.14.049-.21.07v.028a4.522 4.522 0 0 0-.248.06c-.132.005-.263.005-.395.005a2.886 2.886 0 0 1-.461-.166 5.299 5.299 0 0 0-.237-.144c-.18-.124-.28-.314-.37-.501l-.02-.004a3.416 3.416 0 0 0-.085-.218c-.039-1.42-.006-2.843-.017-4.264v-4.126c-.002-.13.01-.26.043-.386l.094-.18c.068-.141.158-.271.276-.38l.003-.022c.133-.1.271-.195.428-.257ZM13.288 9.883c.607-.045 1.216-.014 1.824-.02.763.01 1.526-.014 2.289.024.338.063.686.08 ************.***************.069l-.012.045.211.009v.04c.2.068.395.146.591.224l.007.031c.2.097.397.202.576.333l.097-.001-.009.074.075-.004-.004.051c.091.052.183.105.276.156l-.008.054.052-.014.092.092-.007.046.077-.013.005.041c.341.253.608.578.88.895.09.137.184.273.274.41l.024.004c.044.091.091.182.135.275.042.09.088.178.146.26l.018.157.074-.003-.009.093c.033.066.065.133.094.2.053.209.094.419.148.626.013.13.027.261.076.385.007.464.068.942-.037 1.399-.016.106-.027.212-.04.319-.065.265-.127.53-.196.793l-.06.007.03.12-.084-.003-.006.192-.05.003c-.072.17-.152.34-.232.508l-.062-.002.017.123h-.058a5.112 5.112 0 0 1-.271.402c-.091.108-.185.215-.268.328-.261.283-.544.545-.842.79-.11.07-.215.146-.322.218l.003.053-.127-.01v.056c-.138.076-.275.153-.417.222l.004.054-.126-.015-.012.077a7.402 7.402 0 0 0-.122-.017l-.008.053c-.128.04-.253.087-.373.145l-.095-.008.003.073h-.208l.004.058-.17.024a4.928 4.928 0 0 1-1.28.184c-1.053.016-2.107.003-3.16.007-.419-.003-.874.022-1.233-.22-.276-.303-.274-.725-.28-1.102.001-2.838-.004-5.677.002-8.515-.012-.229.053-.45.14-.662l.028-.013c.132-.235.412-.296.662-.36Zm2.992 2.338.01.057-.177.013c-.185.068-.376.127-.537.24l-.093.002.006.075-.046-.003a7.572 7.572 0 0 1-.168.153c.002.09.006.182.01.272.113.024.243.11.351.025.155-.109.3-.231.487-.291.657-.165 1.356-.244 2.02-.076l.262.076c.113.046.226.094.334.153l.048-.01-.008.072.26.015.135-.11-.012-.093.13-.03a23.05 23.05 0 0 0-.09-.026.838.838 0 0 0-.234-.258 12.16 12.16 0 0 1-.428-.157 8.104 8.104 0 0 0-.237-.05l.014-.045-.34-.012-.004-.054a19.88 19.88 0 0 0-1.418.005l.007.055-.282.002Zm.283.755-.244.02c-.15.06-.305.103-.455.158a13.48 13.48 0 0 1-.267.137 2.834 2.834 0 0 0-1.015.96l-.05.007.015.123h-.084c.001.1.003.202.007.304a.524.524 0 0 0 .38.063l.033-.125.036.01c.078-.14.165-.275.252-.41.143-.123.277-.256.413-.39.101-.053.202-.111.303-.167l.003-.023c.084-.032.166-.065.25-.099.06-.026.121-.05.184-.075.111-.028.223-.055.334-.088a8.106 8.106 0 0 1 1.285-.005c.109.036.22.067.33.102l.168.02.004.068c.033-.004.097-.014.13-.02l.002.088.127-.023.002.057.238.122c.307.247.58.539.741.892.173.182.482.048.457-.194-.283-.532-.72-1.013-1.297-1.272v-.025c-.187-.067-.377-.123-.561-.195a12.46 12.46 0 0 0-.25-.02l.006-.05a13.052 13.052 0 0 0-1.488 0l.01.05Zm.396.618a2.211 2.211 0 0 0-1.099.437c-.245.13-.43.332-.605.536l-.026.002c-.098.158-.193.317-.283.478l.012.096-.06.001c-.021.096-.045.192-.083.284-.014.295-.088.62.056.895l.002.067c.077.091.153.183.228.277l.025-.001.067.084c.238.147.518.193.8.164l.28-.125c.059-.05.12-.099.181-.148.108-.148.222-.298.264-.477l.033.003c.008-.076.017-.152.028-.228a3.64 3.64 0 0 0 .063-.205c.065-.07.137-.136.213-.198.253-.045.535-.005.678.221.073.225.04.463.023.694-.085.219-.16.441-.278.648l-.033.006a8.863 8.863 0 0 1-.068.088l-.184.211-.14.11c-.069.042-.137.085-.203.13-.093.05-.187.096-.286.132l-.005.049-.103-.003-.314.133c.01.123-.052.297.1.363.19.031.37-.043.55-.091.642-.284 1.118-.833 1.371-1.45.146-.359.098-.752.076-1.126l-.08-.003.024-.097c-.104-.108-.205-.22-.306-.33a1.337 1.337 0 0 0-.996-.092c-.303.092-.48.376-.546.654l-.043-.007c-.004.078-.01.156-.013.234-.074.128-.15.256-.223.385-.112.028-.223.06-.339.075a.924.924 0 0 1-.435-.325 5.43 5.43 0 0 1 .008-.593c.073-.188.138-.38.23-.561l.119-.144c.161-.221.383-.383.62-.527l.096.001.001-.08.096.007c.162-.098.356-.123.538-.17.413-.032.852-.02 1.222.176l.096-.013-.006.058c.19.097.379.203.525.358.198.19.365.413.452.67l.022.005c.02.076.042.152.067.228.064.392.017.786-.067 1.17l-.05.003.002.193-.055-.003c-.01.155-.183.409.058.468l-.01.043h.254c.075-.11.145-.226.187-.353l.057-.192c.163-.617.225-1.32-.084-1.905l.006-.092-.06-.002a15.401 15.401 0 0 1-.096-.175c-.099-.16-.226-.299-.363-.429a6.185 6.185 0 0 0-.217-.173 10.12 10.12 0 0 1-.165-.102l-.19-.095.002-.054-.094.006-.14-.07c-.127-.106-.297-.136-.453-.187-.29.026-.578-.031-.863-.073l-.068.056Zm-.235.823c-.34.085-.571.345-.818.563a4.65 4.65 0 0 0-.357.693c-.021.205-.03.411-.001.615.124.005.25.004.375-.007.025-.108.052-.214.083-.32.056-.395.278-.75.611-.992a1.362 1.362 0 0 1 1.315-.058c.075.066.153.132.233.196l.008.016.094.078.025.002c.065.119.135.234.213.346l.01.167.062-.005c.02.277.018.556 0 .835l-.062-.001-.013.255h-.068l.008.09c-.072.134-.137.271-.21.404l-.08.136-.029-.001c-.098.162-.2.327-.348.455-.109.096-.093.238-.095.365.1.04.203.1.318.071.195-.105.295-.3.432-.46l.1-.174.034.007c.087-.147.168-.297.233-.454l.058-.004-.022-.12.07-.002c.006-.04.014-.12.018-.16.23-.485.227-1.055.05-1.556l-.032-.003c-.032-.079-.067-.156-.1-.233-.126-.184-.261-.365-.457-.487l-.003-.042-.075.005.01-.055c-.207-.1-.425-.176-.641-.255-.32-.025-.65-.037-.949.09Zm.372 1.371-.004.247h-.054l-.017.295c-.065.157-.133.314-.204.469l-.026.009a6.34 6.34 0 0 1-.156.142l-.01.022-.199.097-.002.036c-.335.123-.698.09-1.048.087l-.173.056.021.06-.091.004c.002.127.038.245.157.317.322.014.646.018.967-.02.142-.046.285-.09.429-.133l.005-.024c.1-.063.2-.127.301-.187.072-.064.14-.132.206-.202l.02-.011c.054-.106.113-.21.169-.317l.02-.005c.026-.073.055-.144.087-.215.031-.192.068-.384.067-.58l-.07-.16-.068.019.008-.088a1.23 1.23 0 0 0-.335.082Z"
    />
  </svg>
);
export default ICLaoID;
