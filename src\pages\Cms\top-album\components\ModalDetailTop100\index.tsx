import ApiPlaylist from "@api/ApiCMSPlaylist";
import QUERY_KEY from "@api/QueryKey";
import IconClose from "@components/Icon/IconClose";
import {CircularProgress, Modal} from "@mui/material";
import {GridRowId} from "@mui/x-data-grid";
import {useQuery} from "@tanstack/react-query";
import {useTranslation} from "react-i18next";
import {convertDate} from "src/utils/timeUtils";
import BasicInfo from "./components/BasicInfo";
import ListSongTop100 from "./components/ListSongTop100";
import TabContext from "@mui/lab/TabContext";
import TabList from "@mui/lab/TabList";
import TabPanel from "@mui/lab/TabPanel";
import {Tab} from "@mui/material";
import {useState} from "react";

interface IModalDetailTop100 {
  open: boolean;
  onClose: () => void;
  albumId: GridRowId;
}

export default function ModalDetailTop100({
  open,
  onClose,
  albumId,
}: IModalDetailTop100) {
  const {t} = useTranslation();
  const [tabValue, setTabValue] = useState("1");

  const getDetailTop100 = useQuery({
    queryKey: [QUERY_KEY.TOP_100.GET_ALBUM_TOP_100_DETAIL, albumId],
    queryFn: () => ApiPlaylist.detailPlaylist(String(albumId)),
    enabled: !!albumId,
  });

  const tabComponentArray = [
    {
      value: "1",
      component: <BasicInfo albumData={getDetailTop100?.data || undefined} />,
    },
    {
      value: "2",
      component: (
        <ListSongTop100 albumData={getDetailTop100?.data || undefined} />
      ),
    },
  ];

  const tabPanelArray = [
    {value: "1", label: t("cms.song.basic_info")},
    {value: "2", label: t("cms.artist.song_list")},
  ];

  return (
    <Modal sx={{outline: 0}} open={open} onClose={onClose}>
      <div className="absolute top-1/2 left-1/2 bg-white transform -translate-x-1/2 -translate-y-1/2 rounded-lg min-w-[367px] max-w-[932px] w-full">
        <div className="py-4 px-6 flex justify-between items-center">
          <span className="text-base font-bold">
            {t("cms.album.top_album_detail")}
          </span>
          <IconClose className="cursor-pointer" onClick={onClose} />
        </div>
        <hr className="bg-[#F0F0F0]" />
        <div className="flex flex-col p-6">
          {getDetailTop100?.isFetching ? (
            <div className="flex justify-center items-center h-[50vh]">
              <CircularProgress />
            </div>
          ) : (
            <div className="flex flex-col gap-1">
              <div className="flex items-start justify-between">
                <div className="flex flex-row items-center gap-3">
                  <img
                    src={
                      getDetailTop100?.data?.images?.SMALL ||
                      getDetailTop100?.data?.images?.DEFAULT ||
                      "/image/default-music.png"
                    }
                    className="w-10 h-10 object-cover rounded"
                  />
                  <div className="text-xl font-semibold capitalize">
                    {getDetailTop100?.data?.name} - (
                    {getDetailTop100?.data?.totalSongs} {t("common.song")})
                  </div>
                </div>
                <span className="text-sm text-[#656565] font-semibold">
                  {convertDate(getDetailTop100?.data?.releaseDate)}
                </span>
              </div>
              <div className="flex flex-col gap-3">
                <TabContext value={tabValue}>
                  <TabList
                    className="cms-tabs border-b border-[#D9D9D9]"
                    onChange={(
                      _event: React.SyntheticEvent,
                      newValue: string,
                    ) => setTabValue(newValue)}
                  >
                    {tabPanelArray?.map((item) => (
                      <Tab
                        sx={{
                          textTransform: "none",
                        }}
                        label={item.label}
                        value={item.value}
                        key={item.value}
                      />
                    ))}
                  </TabList>
                  {tabComponentArray?.map((item) => (
                    <TabPanel
                      sx={{padding: 0}}
                      value={item.value}
                      key={item.value}
                    >
                      {item.component}
                    </TabPanel>
                  ))}
                </TabContext>
              </div>
            </div>
          )}
        </div>
      </div>
    </Modal>
  );
}
