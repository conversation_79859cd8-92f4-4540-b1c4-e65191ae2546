import {useTranslation} from "react-i18next";
import {SwiperSlide} from "swiper/react";
import {useQuery} from "@tanstack/react-query";
import ApiHome from "@api/ApiHome";
import QUERY_KEY from "@api/QueryKey";
import Slider from "@components/Slider";
import Subtitle from "@components/Subtitle";
import SubTitleSkeleton from "@components/SubTitleSkeleton";
import CommonArtistCard from "@components/CommonArtistCard";
import ArtistCardSkeleton from "@components/ArtistCardSkeleton";
import {IArtist, IParamsDefault} from "src/types";

export default function FollowingArtist() {
  const {t} = useTranslation();

  const params: IParamsDefault = {
    page: 0,
    pageSize: 10,
  };

  const {data: dataArtist, isLoading} = useQuery({
    queryKey: [QUERY_KEY.ARTIST.GET_TOP_ARTIST_FAVOURITE, params],
    queryFn: () => ApiHome.getTopArtistFavourite(params),
    // staleTime: 5 * 60 * 1000,
  });

  return (
    <>
      {isLoading && (
        <div className="flex flex-col gap-2 sm:gap-3 md:gap-4 lg:gap-5">
          <SubTitleSkeleton />
          <Slider slidesPerView={6}>
            {[...Array(6)].map((_, index) => (
              <SwiperSlide key={index} virtualIndex={index}>
                <ArtistCardSkeleton />
              </SwiperSlide>
            ))}
          </Slider>
        </div>
      )}
      {dataArtist?.data && dataArtist?.data?.length > 0 && (
        <div className="flex flex-col gap-2 sm:gap-3 md:gap-4 lg:gap-5">
          <Subtitle subtitle={t("playlist.favorite_artist")} seeMore={false} />
          <Slider slidesPerView={6} spaceBetween={16}>
            {dataArtist?.data?.map((item: IArtist, index: number) => {
              return (
                <SwiperSlide
                  key={`following_artist_${item.id}`}
                  virtualIndex={index}
                >
                  <CommonArtistCard data={item} />
                </SwiperSlide>
              );
            })}
          </Slider>
        </div>
      )}
    </>
  );
}
