import {SVGProps} from "react";

function IconMicrophone(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={20}
      height={20}
      fill="none"
      {...props}
    >
      <g clipPath="url(#a)">
        <path
          fill={props.fill || "currentColor"}
          d="M17.454 3.004A6.416 6.416 0 0 0 6.693 9.105l.063.235-3.117 4.008a2.475 2.475 0 0 0-.354 2.412.687.687 0 0 1-.342.015l-.108-.028a.917.917 0 0 0-.579 1.738 2.534 2.534 0 0 0 2.334-.356 2.476 2.476 0 0 0 2.37-.202l.15-.107 4.008-3.118a6.417 6.417 0 0 0 6.336-10.698Zm-9.816 8.188a6.459 6.459 0 0 0 1.629 1.629l-3.282 2.552a.642.642 0 0 1-.898-.9l2.55-3.281ZM9.157 4.92l.186.308a18.337 18.337 0 0 0 2.603 3.285c.992.99 2.094 1.863 3.284 2.603l.309.186A4.585 4.585 0 0 1 9.157 4.92Zm1.373-1.293A4.585 4.585 0 0 1 16.83 9.93l-.297-.167a16.505 16.505 0 0 1-3.293-2.544 16.503 16.503 0 0 1-2.339-2.955l-.106-.174-.186-.318-.08-.143Z"
        />
      </g>
      <defs>
        <clipPath id="a">
          <path fill={props.fill || "currentColor"} d="M20 0H0v20h20z" />
        </clipPath>
      </defs>
    </svg>
  );
}

export default IconMicrophone;
