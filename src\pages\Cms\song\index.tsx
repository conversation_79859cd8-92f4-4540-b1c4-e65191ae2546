import {useState} from "react";
import SearchInput from "../components/SearchInput";
import {GridColDef, GridRowId, GridSortModel} from "@mui/x-data-grid";
import {
  convertDate,
  convertNumber,
  convertSongDuration,
} from "src/utils/timeUtils";
import CmsTable from "../components/CmsTable";
import {keepPreviousData, useMutation, useQuery} from "@tanstack/react-query";
import QUERY_KEY from "@api/QueryKey";
import ApiCMSSong from "@api/ApiCMSSong";
import GlobalButton from "@components/ButtonGlobal";
import IconAdd from "@components/Icon/IconAdd";
import ApiAutofill from "@api/ApiAutofill";
import {
  EThemeAndGenreType,
  IArtist,
  IPlaylist,
  ISong,
  IThemeAndGenre,
  PlaylistType,
} from "src/types";
import {LocalizationProvider} from "@mui/x-date-pickers/LocalizationProvider/LocalizationProvider";
import {AdapterDayjs} from "@mui/x-date-pickers/AdapterDayjs";
import {DatePicker} from "@mui/x-date-pickers/DatePicker";
import dayjs from "dayjs";
import ModalAddEditSong from "./components/ModalAddEditSong";
import {t} from "i18next";
import ModalDetailSong from "./components/ModalDetailSong";
import AutoCompleteAutofill from "@components/AutoCompleteAutofill";
import {Avatar, AvatarGroup, IconButton, Tooltip} from "@mui/material";
import useDebounce from "src/hooks/useDebounce";
import IconCmsEdit from "@components/Icon/IconCmsEdit";
import IconCmsDelete from "@components/Icon/IconCmsDelete";
import ModalComfirm from "@components/ModalConfirm";
import {toast} from "react-toastify";

export default function CmsSong() {
  const [openModalSong, setOpenModalSong] = useState(false);
  const [openModalDetail, setOpenModalDetail] = useState(false);
  const [songId, setSongId] = useState<GridRowId>("");

  const [page, setPage] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const [sortModel, setSortModel] = useState<GridSortModel>([]);
  const [searchText, setSearchText] = useState("");
  const debounceSearchText = useDebounce(searchText);
  const [theme, setTheme] = useState("");
  const [genre, setGenre] = useState("");
  const [album, setAlbum] = useState("");
  const [updateTime, setUpdateTime] = useState("");
  const [selectedItem, setSelectedItem] = useState<ISong | undefined>();
  const [isConfirmDeleteModalOpen, setIsConfirmDeleteModalOpen] =
    useState(false);

  const getSongCms = useQuery({
    queryKey: [
      QUERY_KEY.SONG.GET_LIST_SONG_CMS,
      theme,
      genre,
      album,
      debounceSearchText,
      updateTime,
      page,
      pageSize,
      sortModel,
    ],
    placeholderData: keepPreviousData,
    queryFn: () =>
      ApiCMSSong.getCmsSongs({
        page: page,
        pageSize: pageSize,
        keyword: debounceSearchText,
        themeId: theme,
        genreId: genre,
        albumId: album,
        updatedAt: updateTime,
      }),
  });

  const deleteSongMutation = useMutation({
    mutationFn: (id: string) => ApiCMSSong.deleteSong(id),
    onSuccess: () => {
      toast.success(t("cms.song.delete_song_success"));
      getSongCms.refetch();
    },
  });

  const handleDeleteSong = (song: ISong) => {
    setSelectedItem(song);
    setIsConfirmDeleteModalOpen(true);
  };

  const confirmDeleteArtist = () => {
    if (selectedItem) {
      deleteSongMutation.mutate(selectedItem.id);
      setIsConfirmDeleteModalOpen(false);
      setSelectedItem(undefined);
    }
  };

  const tableRows = getSongCms?.data?.data?.map((item: ISong) => ({
    id: item?.id,
    images: item?.images,
    name: item?.name,
    artists: item?.artists,
    album: item?.playlists?.filter((item) => item.type === PlaylistType.ALBUM),
    themes: item?.genres?.filter(
      (item) => item?.type === EThemeAndGenreType.THEME,
    ),
    genres: item?.genres?.filter(
      (item) => item?.type === EThemeAndGenreType.GENRE,
    ),
    audios: item?.audios,
    lrcLyrics: item?.lrcLyrics,
    textLyrics: item?.textLyrics,
    updatedAt: item?.updatedAt,
    favoriteCount: item?.totalLikes,
    shareCount: item?.totalShares,
    playlistCount: item?.totalAddedToPlaylists,
    duration: item?.duration,
    releaseDate: item?.releaseDate,
    language: item?.language,
  }));

  const columns: GridColDef[] = [
    {
      field: "images",
      headerName: t("cms.song.image_song"),
      headerClassName: "flex item-center",
      width: 136,
      renderCell: (params) => (
        <div className="w-full h-full flex justify-center items-center">
          <img
            src={
              params.value.SMALL ||
              params.value.DEFAULT ||
              "/image/default-music.png"
            }
            className="h-12 w-12 rounded-[4px] object-cover"
          />
        </div>
      ),
      disableColumnMenu: true,
      sortable: false,
      headerAlign: "center",
    },
    {
      field: "name",
      headerName: t("cms.song.song_name"),
      width: 190,
      sortable: false,
      renderCell: (params) => (
        <Tooltip placement="left" arrow title={params.value}>
          <div
            style={{whiteSpace: "normal", wordWrap: "break-word"}}
            className="line-clamp-3"
          >
            {params.value}
          </div>
        </Tooltip>
      ),
    },
    {
      field: "artist",
      headerName: t("common.performing_artist_composer"),
      sortable: false,
      width: 250,
      renderCell: (params) => {
        return (
          <div className="w-full h-full flex justify-start items-center space-x-1">
            <AvatarGroup
              max={3}
              sx={{
                "& .MuiAvatar-root": {
                  width: 24,
                  height: 24,
                  fontSize: 12,
                },
              }}
            >
              {params?.row?.artists.map((artist: IArtist, index: number) => (
                <Avatar
                  key={`avatar_${index}`}
                  src={
                    artist?.images?.SMALL ||
                    artist.images?.DEFAULT ||
                    "/image/default-avatar.png"
                  }
                />
              ))}
            </AvatarGroup>
            <span className="font-semibold text-sm text-[#242728]">
              {params?.row?.artists
                ?.slice(0, 2)
                .map((artist: IArtist) => artist?.stageName ?? artist?.name)
                .join(", ")}
              {params?.row?.artists?.length > 2 &&
                ` & ${t("common.lots_artist")}`}
            </span>
          </div>
        );
      },
    },
    {
      field: "album",
      headerName: t("cms.song.album"),
      sortable: false,
      width: 164,
      renderCell: (params) => {
        return (
          <Tooltip
            placement="left"
            arrow
            title={params?.value?.map((item: IPlaylist) => (
              <span key={item?.id}>
                {item?.name}
                {params?.value?.length > 1 &&
                params?.value?.indexOf(item) < params?.value?.length - 1
                  ? ", "
                  : ""}
              </span>
            ))}
          >
            <div className="line-clamp-3">
              {params?.value?.map((item: IPlaylist) => (
                <span key={item?.id}>
                  {item?.name}
                  {params?.value?.length > 1 &&
                  params?.value?.indexOf(item) < params?.value?.length - 1
                    ? ", "
                    : ""}
                </span>
              ))}
            </div>
          </Tooltip>
        );
      },
    },
    {
      field: "themes",
      headerName: t("cms.song.theme"),
      width: 164,
      sortable: false,
      renderCell: (params) => {
        return (
          <Tooltip
            placement="left"
            arrow
            title={params?.value?.map((item: IThemeAndGenre) => (
              <span key={item?.id}>
                {item?.name}
                {params?.value?.length > 1 &&
                params?.value?.indexOf(item) < params?.value?.length - 1
                  ? ", "
                  : ""}
              </span>
            ))}
          >
            <div className="line-clamp-3">
              {params?.value?.map((item: IThemeAndGenre) => (
                <span key={item?.id}>
                  {item?.name}
                  {params?.value?.length > 1 &&
                  params?.value?.indexOf(item) < params?.value?.length - 1
                    ? ", "
                    : ""}
                </span>
              ))}
            </div>
          </Tooltip>
        );
      },
    },
    {
      field: "genres",
      headerName: t("cms.song.genres"),
      sortable: false,
      width: 164,
      renderCell: (params) => {
        return (
          <div className="flex flex-wrap gap-2 max-h-[94px] overflow-hidden">
            {params?.value?.map((item: IThemeAndGenre) => (
              <span
                className="rounded-lg  py-0.5 px-2 bg-[#F2F2F3] border border-[#DCDCDC]"
                key={item?.id}
              >
                {item?.name}
              </span>
            ))}
          </div>
        );
      },
    },
    {
      field: "releaseDate",
      headerName: t("cms.song.release_time"),
      sortable: false,
      width: 197,
      valueGetter: (value) => {
        return convertDate(value);
      },
    },
    {
      field: "updatedAt",
      headerName: t("cms.song.update_time"),
      sortable: false,
      width: 186,
      valueGetter: (value) => {
        return convertDate(value);
      },
    },
    {
      field: "favoriteCount",
      headerName: t("cms.song.favourite_count"),
      width: 160,
      sortable: false,
      renderCell: (params) => (
        <span className="text-[#FF4319]">{convertNumber(params?.value)}</span>
      ),
    },
    {
      field: "shareCount",
      headerName: t("cms.song.share_count"),
      sortable: false,
      width: 120,
      renderCell: (params) => <span>{convertNumber(params?.value)}</span>,
    },
    {
      field: "playlistCount",
      headerName: t("cms.song.add_to_playlist_collection"),
      sortable: false,
      width: 190,
      renderCell: (params) => <span>{convertNumber(params?.value)}</span>,
    },
    {
      field: "duration",
      headerName: t("cms.song.duration"),
      sortable: false,
      width: 136,
      valueGetter: (value) => {
        return convertSongDuration(value);
      },
    },
    {
      field: "actions",
      headerName: t("common.actions"),
      minWidth: 30,
      width: 120,
      sortable: false,
      disableColumnMenu: true,
      align: "center",
      headerAlign: "center",
      headerClassName: "sticky-header",
      cellClassName: "sticky-cell",
      renderCell: (param) => (
        <div className="flex justify-center items-center">
          <IconButton
            onClick={() => {
              setSelectedItem(param.row);
              setOpenModalSong(true);
            }}
          >
            <IconCmsEdit />
          </IconButton>
          <IconButton
            onClick={() => {
              handleDeleteSong(param?.row);
            }}
          >
            <IconCmsDelete />
          </IconButton>
        </div>
      ),
    },
  ];

  return (
    <>
      <div className="p-5 bg-white space-y-4 rounded-[20px]">
        <div className="flex justify-between sm:items-center sm:flex-row flex-col sm:gap-0 gap-3 items-start">
          <div className="flex gap-3 flex-wrap w-full">
            <SearchInput
              placeholder={t("cms.song.placeholder_search_song")}
              searchText={searchText}
              className="py-[5px]"
              onChange={(v) => setSearchText(v)}
            />

            {/*filter artist*/}
            {/* TODO: Đợi BA chốt */}
            {/* <AutoCompleteAutofill
                className="border-lg placeholder-dark-600 gray-theme"
                name="artistId"
                placeHolder={t("cms.song.placeholder_singer")}
                suggestionAPI={ApiAutofill.autoArtist}
                onChange={(val) => {
                  if (!Array.isArray(val)) {
                    setArtistId(val && val.id);
                  }
                }}
              /> */}
            {/*filter theme*/}
            <AutoCompleteAutofill
              className="border-lg placeholder-dark-600 gray-theme"
              name="selectTheme"
              placeHolder={t("cms.song.placeholder_theme")}
              suggestionAPI={ApiAutofill.autoTheme}
              onChange={(val) => {
                if (!Array.isArray(val)) {
                  setTheme(val && val.id);
                }
              }}
            />
            {/*filter genre*/}
            <AutoCompleteAutofill
              className="border-lg placeholder-dark-600 gray-theme"
              name="selectGenre"
              placeHolder={t("cms.song.placeholder_genres")}
              suggestionAPI={ApiAutofill.autoGenre}
              onChange={(val) => {
                if (!Array.isArray(val)) {
                  setGenre(val && val.id);
                }
              }}
            />
            {/*filter album*/}
            <AutoCompleteAutofill
              className="border-lg placeholder-dark-600 gray-theme"
              name="selectAlbum"
              placeHolder={t("cms.song.placeholder_album")}
              suggestionAPI={ApiAutofill.autoAlbum}
              onChange={(val) => {
                if (!Array.isArray(val)) {
                  setAlbum(val && val.id);
                }
              }}
            />
            <LocalizationProvider dateAdapter={AdapterDayjs}>
              <DatePicker
                disableFuture
                onChange={(v) => {
                  if (v) {
                    setUpdateTime(dayjs(v).format("YYYY-MM-DD"));
                  }
                }}
                slotProps={{
                  textField: {
                    size: "small",
                    placeholder: t("cms.song.placeholder_updateTime"),
                  },
                  field: {
                    clearable: true,
                    onClear: () => setUpdateTime(""),
                  },
                }}
                className="cms-datepicker-gray"
              />
            </LocalizationProvider>
          </div>
          <GlobalButton
            text={t("cms.song.btn_add")}
            startIcon={<IconAdd />}
            className="w-auto whitespace-nowrap font-semibold"
            onClick={() => setOpenModalSong(true)}
          />
        </div>
        <CmsTable
          onRowDoubleClick={(params) => {
            setOpenModalDetail(true);
            setSongId(params?.id);
          }}
          ordinalColumn
          rows={tableRows}
          columns={columns}
          loading={getSongCms?.isLoading}
          totalItems={getSongCms?.data?.meta?.totalItems}
          currentPage={page}
          onPageChange={(page) => setPage(page)}
          rowsPerPage={pageSize || 10}
          onRowsPerPageChange={(rowsPerPage) => setPageSize(rowsPerPage)}
          onSortModelChange={(model) => setSortModel(model)}
          hideFooter
        />
      </div>
      <ModalAddEditSong
        open={openModalSong}
        refetch={getSongCms.refetch}
        initValue={selectedItem || undefined}
        onClose={() => {
          setOpenModalSong(false);
          setSelectedItem(undefined);
        }}
      />
      {openModalDetail && (
        <ModalDetailSong
          open={openModalDetail}
          onClose={() => {
            setOpenModalDetail(false);
            setSongId("" as GridRowId);
          }}
          songId={songId}
        />
      )}
      <ModalComfirm
        open={isConfirmDeleteModalOpen}
        onConfirm={confirmDeleteArtist}
        onCancel={() => setIsConfirmDeleteModalOpen(false)}
        title={t("cms.song.delete_song")}
        loading={deleteSongMutation.isPending}
      >
        <div className="flex flex-col gap-y-2">
          <div className="font-normal text-xl">
            {t("cms.song.question_confirm_delete")}
          </div>
          <div className="flex items-center gap-x-2">
            <img
              src={
                selectedItem?.images?.SMALL ||
                selectedItem?.images?.DEFAULT ||
                "/image/default-music.png"
              }
              className="w-12 h-12 border-[1.5px] border-solid border-[#262626] rounded-full object-cover"
            />
            <span className="font-semibold text-base">
              {selectedItem?.name}
            </span>
          </div>
        </div>
      </ModalComfirm>
    </>
  );
}
