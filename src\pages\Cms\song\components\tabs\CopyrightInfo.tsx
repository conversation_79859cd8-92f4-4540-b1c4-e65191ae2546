import IconFileText from "@components/Icon/IconFileText";
import {Link} from "@mui/material";
import {useTranslation} from "react-i18next";
import {ISong} from "src/types";

export default function CopyrightInfo({
  songData,
}: {
  songData: ISong | undefined;
}) {
  const {t} = useTranslation();

  return (
    <div className="flex justify-between py-2.5 px-4 bg-[#F6F6F6] border border-[#E5E5E5] rounded-xl">
      <div className="flex flex-col gap-2.5">
        <div className="flex gap-10 items-center">
          <span className="text-[#656565] text-sm min-w-36">
            {t("cms.song.song_license")}
          </span>
          {songData?.license ? (
            <Link
              className="flex items-center gap-2"
              sx={{
                paddingY: "5px",
                paddingX: "12px",
                color: "#242728",
                fontSize: "14px",
                textDecoration: "none",
                backgroundColor: "#E2E2E2",
                borderRadius: "4px",
                wordBreak: "break-all",
              }}
              href={songData?.license}
              target="_blank"
            >
              <IconFileText className="flex-shrink-0" />
              <span>
                {songData?.license ? songData.license.split("/").pop() : "-"}
              </span>
            </Link>
          ) : (
            "-"
          )}
        </div>
      </div>
    </div>
  );
}
