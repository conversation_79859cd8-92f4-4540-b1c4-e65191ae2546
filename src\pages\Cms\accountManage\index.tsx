import {useTranslation} from "react-i18next";
import {
  useFullName,
  useCountryOptions,
  useGenderOptions,
  useRoleOptions,
} from "src/utils/global";
import {IRootState} from "@redux/store";
import {useSelector} from "react-redux";
export default function AccountPersonalInfo() {
  const {t} = useTranslation();
  const genderOptions = useGenderOptions();
  const roleOptions = useRoleOptions();
  const countryOptions = useCountryOptions();
  const getFullName = useFullName();

  const {userInfo} = useSelector((state: IRootState) => state.user);

  return (
    <div className="bg-white rounded-[20px] p-5 flex flex-col gap-4">
      <div className="flex flex-col gap-[10px] justify-start">
        <span className="text-[#000000D9] text-sm font-semibold">
          {t("common.cover")}
        </span>
        <div className="rounded-lg flex items-center">
          <div className="flex items-start">
            {userInfo?.avatar ? (
              <img
                src={userInfo?.avatar || "./image/default-avatar.png"}
                alt="avatar"
                className="w-[120px] h-[120px] object-cover rounded-lg bg-orange-800"
              />
            ) : (
              <div className="w-[120px] h-[120px] border border-dashed border-orange-500 bg-orange-800 flex items-center justify-center rounded-lg">
                <span className="text-sm text-orange-500">
                  {t("common.no_image")}
                </span>
              </div>
            )}
          </div>
        </div>
      </div>
      <div className="flex gap-6 w-full flex-col md:flex-row">
        <div className="flex flex-col gap-[10px] justify-start w-full md:w-[50%]">
          <span className="text-[#000000D9] text-sm font-semibold">
            {t("common.full_name")}
          </span>
          <input
            type="text"
            name="fullName"
            value={getFullName ?? "-"}
            placeholder={t("common.full_name")}
            disabled
            className={
              "py-2 px-3 border border-solid border-[#D9D9D9] rounded-md bg-white"
            }
          />
        </div>
        <div className="flex flex-col gap-[10px] justify-start w-full md:w-[50%]">
          <span className="text-[#000000D9] text-sm font-semibold">
            {t("common.phone_num")}
          </span>
          <input
            type="text"
            name="phone"
            value={userInfo?.phoneNumber?.[0]?.phoneNumber ?? "-"}
            placeholder={t("common.phone_num")}
            disabled
            className={
              "py-2 px-3 border border-solid border-[#D9D9D9] rounded-md bg-white"
            }
          />
        </div>
      </div>
      <div className="flex gap-6 w-full flex-col md:flex-row">
        <div className="flex flex-col gap-[10px] justify-start w-full md:w-[50%]">
          <span className="text-[#000000D9] text-sm font-semibold">Email</span>
          <input
            type="text"
            name="email"
            value={userInfo?.email?.[0]?.email ?? "-"}
            placeholder="Email"
            disabled
            className={
              "py-2 px-3 border border-solid border-[#D9D9D9] rounded-md bg-white"
            }
          />
        </div>
        <div className="flex flex-col gap-[10px] justify-start w-full md:w-[50%]">
          <span className="text-[#000000D9] text-sm font-semibold">
            {t("common.birthday")}
          </span>
          <input
            type="date"
            name="birthday"
            value={userInfo?.dateOfBirth ?? "-"}
            placeholder={t("common.birthday")}
            disabled
            className={
              "py-2 px-3 border border-solid border-[#D9D9D9] rounded-md bg-white"
            }
          />
        </div>
      </div>
      <div className="flex gap-6 w-full flex-col md:flex-row">
        <div className="flex flex-col gap-[10px] justify-start w-full md:w-[50%]">
          <span className="text-[#000000D9] text-sm font-semibold">
            {t("common.role")}
          </span>
          <input
            type="text"
            name="role"
            value={
              roleOptions.find((option) => option.value === userInfo?.role)
                ?.label ?? "-"
            }
            placeholder={t("common.role")}
            disabled
            className={
              "py-2 px-3 border border-solid border-[#D9D9D9] rounded-md bg-white"
            }
          />
        </div>
        <div className="flex flex-col gap-[10px] justify-start w-full md:w-[50%]">
          <span className="text-[#000000D9] text-sm font-semibold">
            {t("common.gender")}
          </span>
          <input
            type="text"
            name="gender"
            value={
              genderOptions.find(
                (option) => option.value === Number(userInfo?.gender),
              )?.label ?? "-"
            }
            placeholder={t("common.gender")}
            disabled
            className={
              "py-2 px-3 border border-solid border-[#D9D9D9] rounded-md bg-white"
            }
          />
        </div>
      </div>
      <div className="flex gap-6 w-full flex-col md:flex-row">
        <div className="flex flex-col gap-[10px] justify-start w-full md:w-[50%]">
          <span className="text-[#000000D9] text-sm font-semibold">
            {t("common.province")}
          </span>
          <input
            type="text"
            name="province"
            value={userInfo?.province ?? "-"}
            placeholder="province"
            disabled
            className={
              "py-2 px-3 border border-solid border-[#D9D9D9] rounded-md bg-white"
            }
          />
        </div>
        <div className="flex flex-col gap-[10px] justify-start w-full md:w-[50%]">
          <span className="text-[#000000D9] text-sm font-semibold">
            {t("common.country")}
          </span>
          <input
            type="text"
            name="countryName"
            value={
              countryOptions.find(
                (option) =>
                  option.value ===
                  userInfo?.countryName?.toUpperCase()?.replace(/\s+/g, ""),
              )?.label ?? "-"
            }
            placeholder="countryName"
            disabled
            className={
              "py-2 px-3 border border-solid border-[#D9D9D9] rounded-md bg-white"
            }
          />
        </div>
      </div>
    </div>
  );
}
