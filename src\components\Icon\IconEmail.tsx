import {SVGProps} from "react";

function IconEmail(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={24}
      height={25}
      viewBox="0 0 24 25"
      fill="none"
      {...props}
    >
      <path
        stroke="#fff"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeOpacity=".8"
        d="M4 7.686v9.143a2.286 2.286 0 0 0 2.286 2.286h11.428A2.286 2.286 0 0 0 20 16.829V7.686a2.286 2.286 0 0 0-2.286-2.285H6.286A2.286 2.286 0 0 0 4 7.686Z"
        clipRule="evenodd"
      />
      <path
        stroke="#fff"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeOpacity=".8"
        d="M6.286 8.83 12 12.257l5.715-3.429"
      />
    </svg>
  );
}

export default IconEmail;
