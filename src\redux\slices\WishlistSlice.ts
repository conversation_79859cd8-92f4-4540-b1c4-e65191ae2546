import {createSlice} from "@reduxjs/toolkit";

interface WishlistState {
  isOpen: boolean;
}

const initialState: WishlistState = {
  isOpen: false,
};

const WishlistSlice = createSlice({
  name: "wishlist",
  initialState,
  reducers: {
    toggleWishlist(state) {
      state.isOpen = !state.isOpen;
    },
    openWishlist(state) {
      state.isOpen = true;
    },
    closeWishlist(state) {
      state.isOpen = false;
    },
  },
});

export const {toggleWishlist, openWishlist, closeWishlist} =
  WishlistSlice.actions;

export default WishlistSlice.reducer;
