import {SVGProps} from "react";

function IconPackage({
  width = "20",
  height = "20",
  ...props
}: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox={`0 0 ${width} ${height}`}
      className={props.className}
      fill="none"
      {...props}
    >
      <path
        d="M14.5165 2.5C14.7871 2.49997 15.0536 2.56582 15.293 2.69184C15.5324 2.81787 15.7376 3.00028 15.8907 3.22333L15.9632 3.34L18.7415 8.2C18.9112 8.49664 18.9855 8.83825 18.9545 9.17855C18.9235 9.51885 18.7887 9.8414 18.5682 10.1025L18.4732 10.205L10.7374 17.9408C10.5559 18.1222 10.3139 18.2303 10.0577 18.2443C9.80152 18.2583 9.54917 18.1772 9.34904 18.0167L9.26404 17.9417L1.52821 10.205C1.28657 9.96346 1.12499 9.65349 1.06537 9.31707C1.00575 8.98065 1.05096 8.63404 1.19488 8.32417L1.25988 8.19917L4.03738 3.33917C4.17162 3.10423 4.36104 2.90551 4.58928 2.76017C4.81752 2.61484 5.07771 2.52726 5.34738 2.505L5.48321 2.5H14.5165ZM14.5165 4.16667H5.48321L2.70571 9.0275L9.99988 16.3217L17.294 9.0275L14.5165 4.16667ZM6.07738 7.74417C6.22087 7.60069 6.41179 7.51449 6.61431 7.50176C6.81683 7.48903 7.01704 7.55062 7.17738 7.675L7.25571 7.74417L9.99988 10.4883L12.744 7.74417C12.894 7.59471 13.0952 7.50794 13.3069 7.50148C13.5185 7.49501 13.7246 7.56935 13.8834 7.70937C14.0422 7.8494 14.1418 8.04463 14.1619 8.2554C14.182 8.46616 14.1211 8.67667 13.9915 8.84417L13.9224 8.9225L10.7365 12.1083C10.5551 12.2897 10.3131 12.3978 10.0569 12.4118C9.80069 12.4258 9.54834 12.3447 9.34821 12.1842L9.26321 12.1092L6.07738 8.9225C5.92115 8.76623 5.83339 8.5543 5.83339 8.33333C5.83339 8.11236 5.92115 7.90044 6.07738 7.74417Z"
        fill="white"
      />
    </svg>
  );
}

export default IconPackage;
