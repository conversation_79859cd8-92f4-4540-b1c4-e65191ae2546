import {SVGProps} from "react";
const IconClose = ({
  width = "24",
  height = "24",
  ...props
}: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={width}
    height={height}
    viewBox={`0 0 ${width} ${height}`}
    className={props.className}
    fill="none"
    {...props}
  >
    <path
      fill={props.fill || "#242728"}
      fillRule="evenodd"
      d="M6.043 17.96a1 1 0 0 0 1.414 0l4.544-4.545 4.545 4.545a1 1 0 0 0 1.414-1.415l-4.545-4.544 4.545-4.544a1 1 0 0 0-1.415-1.414l-4.544 4.544-4.544-4.544a1 1 0 0 0-1.414 1.414l4.544 4.544-4.544 4.545a1 1 0 0 0 0 1.414Z"
      clipRule="evenodd"
    />
  </svg>
);
export default IconClose;
