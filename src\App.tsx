import {lazy, useEffect} from "react";
import Artist from "@pages/Artist";
import Home from "@pages/Home";
import MusicLibrary from "@pages/MusicLibrary";
import Playlists from "@pages/Playlists";
import Profile from "@pages/Profile";
import Ranking from "@pages/Ranking";
import Search from "@pages/Search";
import Topic from "@pages/Topic";
import TopicDetail from "@pages/Topic/TopicDetail";
import {IRootState} from "@redux/store";
import {useDispatch, useSelector} from "react-redux";
import {BrowserRouter, Navigate, Outlet, Route, Routes} from "react-router-dom";
import {ToastContainer} from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import NotFound from "./components/NotFound/clientNotFound";
import HomeLayout from "./layout/user/HomeLayout";
import "./styles/global.scss";
import "./styles/style.scss";
import {IAccountInfo, Role} from "./types";
import Top100 from "@pages/Top100";
import {loginUser} from "@redux/slices/UserSlice";
import LibraryRecent from "@pages/MusicLibrary/LibraryRecent";
import ArtistPlaylists from "@pages/Artist/components/ArtistPlaylists";
import MyPlaylist from "@pages/MusicLibrary/MyPlaylist";
import PlayerUtil from "./core/PlayerUtil";
import {updateUserInfo} from "@redux/slices/UserSlice";
import ApiAuth from "@api/ApiAuth";
import {useMutation, useQueryClient} from "@tanstack/react-query";
import {closeWishlist} from "@redux/slices/WishlistSlice";
import {clearLike, handleQueueLike} from "./utils/like";
import UpgradePage from "@pages/Upgrade";

const CmsLayout = lazy(() => import("./layout/cms/CmsLayout"));
const CmsAccountUser = lazy(() => import("@pages/Cms/account-user"));
const CmsAccountPremium = lazy(
  () => import("@pages/Cms/account-user/account-premium"),
);
const AccountPersonalInfo = lazy(() => import("@pages/Cms/accountManage"));
const CmsAlbum = lazy(() => import("@pages/Cms/album"));
const ArtistsManage = lazy(() => import("@pages/Cms/artistsManage"));
const CmsPlaylist = lazy(() => import("@pages/Cms/playlist"));
const CmsSong = lazy(() => import("@pages/Cms/song"));
const CmsStatistical = lazy(() => import("@pages/Cms/statistical"));
const CmsThemeAndGenre = lazy(() => import("@pages/Cms/theme-and-genre"));
const CmsTopAlbum = lazy(() => import("@pages/Cms/top-album"));
const NotFoundCMS = lazy(() => import("@components/NotFound/cmsNotFound"));

let baseName = "/";
if (
  window.location.origin !== import.meta.env.VITE_API_BASE_URL &&
  (window.location.hostname === "miniapp.laoapp.la" ||
    window.location.hostname === "uatmnapp.laoapp.la")
) {
  const match = window.location.href.match(
    /^(?:https?:\/\/[^/]+)(\/[^?#]+?)(?:\/|index\.html)?$/,
  );
  const matched = match?.[1];
  if (matched) {
    baseName = matched;
  }
}
const PrivateRoute = ({children}: {children: JSX.Element}) => {
  const {accessToken} = useSelector((state: IRootState) => state.user);

  return accessToken ? children : <Navigate to="/" />;
};

function App() {
  const dispatch = useDispatch();
  const {accessToken, userInfo} = useSelector(
    (state: IRootState) => state.user,
  );
  const {currentSong, queueList} = useSelector(
    (state: IRootState) => state.player,
  );
  const {language} = useSelector((state: IRootState) => state.settings);
  const queryClient = useQueryClient();

  const isAdmin = accessToken && userInfo?.role === Role.ADMIN;
  const isArtist = accessToken && userInfo?.role === Role.ARTIST;

  useEffect(() => {
    queryClient.invalidateQueries({type: "active"});
  }, [accessToken, language]);

  useEffect(() => {
    if (accessToken) {
      ApiAuth.getMe().then((res) => {
        dispatch(updateUserInfo({accessToken, userInfo: res}));
      });
    }
  }, [accessToken]);

  const {mutateAsync: getMeToken} = useMutation({
    mutationFn: (accessToken: string) => ApiAuth.getMeToken(accessToken),
    onSuccess: (data, token) => {
      dispatch(
        loginUser({
          accessToken: token,
          userInfo: data,
        } as IAccountInfo),
      );
    },
  });

  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const token = searchParams.get("tokenLaoID");

    if (token && !accessToken) {
      getMeToken(token);
    }
  }, [location.search, accessToken]);

  useEffect(() => {
    if (!currentSong) {
      dispatch(closeWishlist());
      return;
    }
    if (!PlayerUtil.instance.isSongPlaying(currentSong)) {
      PlayerUtil.instance.loadSong(currentSong);
    }
  }, [currentSong]);

  useEffect(() => {
    if (!accessToken) {
      clearLike();
    } else {
      handleQueueLike(queueList);
    }
  }, [queueList, accessToken]);

  return (
    <>
      <BrowserRouter basename={baseName}>
        <Routes>
          <Route>
            <Route path="*" element={<NotFound />} />
            <Route
              element={
                <HomeLayout>
                  <Outlet />
                </HomeLayout>
              }
            >
              <Route path="/" element={<Home />} />
              <Route path="/index.html" element={<Home />} />
              <Route path="/search/:tab" element={<Search />} />
              <Route path="/ranking" element={<Ranking />} />
              <Route path="/library" element={<MusicLibrary />} />
              <Route path="/playlist/:urlSlug" element={<Playlists />} />
              <Route path="/topic" element={<Topic />} />
              <Route
                path="/library/recent"
                element={
                  <PrivateRoute>
                    <LibraryRecent />
                  </PrivateRoute>
                }
              />
              <Route
                path="/library/favorite"
                element={
                  <PrivateRoute>
                    <MusicLibrary />
                  </PrivateRoute>
                }
              />
              <Route
                path="/library/my-playlists"
                element={
                  <PrivateRoute>
                    <MyPlaylist />
                  </PrivateRoute>
                }
              />
              <Route
                path="/profile"
                element={
                  <PrivateRoute>
                    <Profile />
                  </PrivateRoute>
                }
              />
              <Route
                path="/upgrade"
                element={
                  <PrivateRoute>
                    <UpgradePage />
                  </PrivateRoute>
                }
              />
              <Route path="/artist/:urlSlug" element={<Artist />} />
              <Route
                path="/artist/:urlSlug/playlist"
                element={<ArtistPlaylists />}
              />
              <Route path="/topic/:slug" element={<TopicDetail />} />
              <Route path="/top-100" element={<Top100 />} />
            </Route>
          </Route>

          {(isAdmin || isArtist) && (
            <Route>
              <Route
                element={
                  <CmsLayout>
                    <Outlet />
                  </CmsLayout>
                }
              >
                <Route
                  path="/cms"
                  element={isAdmin ? <CmsStatistical /> : <CmsSong />}
                />
                <Route path="/cms/song" element={<CmsSong />} />
                <Route path="/cms/album" element={<CmsAlbum />} />
                <Route
                  path="/cms/theme-and-genre"
                  element={<CmsThemeAndGenre />}
                />
                <Route
                  path="/cms/account-personal-info"
                  element={<AccountPersonalInfo />}
                />
                {isAdmin && (
                  <>
                    <Route path="/cms/playlist" element={<CmsPlaylist />} />
                    <Route
                      path="/cms/account-personal-info"
                      element={<AccountPersonalInfo />}
                    />
                    <Route
                      path="/cms/account-user"
                      element={<CmsAccountUser />}
                    />
                    <Route path="/cms/artist" element={<ArtistsManage />} />
                    <Route path="/cms/top-album" element={<CmsTopAlbum />} />
                    <Route
                      path="/cms/account-premium"
                      element={<CmsAccountPremium />}
                    />
                  </>
                )}
              </Route>
              <Route path="/cms/*" element={<NotFoundCMS />} />
            </Route>
          )}
        </Routes>
      </BrowserRouter>

      <ToastContainer
        position="top-right"
        draggable
        pauseOnFocusLoss
        autoClose={3000}
        hideProgressBar
        newestOnTop
        pauseOnHover
      />
    </>
  );
}

export default App;
