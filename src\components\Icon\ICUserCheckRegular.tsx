import {SVGProps} from "react";
const ICUserCheckRegular = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={24}
    height={24}
    fill="none"
    {...props}
  >
    <path
      fill="#12B981"
      d="M14 19c0 .736.179 1.434.497 2.052.105.205-.026.448-.256.448H7c-2.29 0-3.5-1.2-3.5-3.48 0-2.55 1.44-5.52 5.5-5.52h4c2.002 0 3.369.724 4.235 1.76.135.162.042.401-.158.467A4.487 4.487 0 0 0 14 19Zm-2.991-8.5c2.206 0 4-1.794 4-4s-1.794-4-4-4-4 1.794-4 4 1.794 4 4 4Zm10.521 6.97a.75.75 0 0 0-1.061 0l-2.137 2.136-.803-.803a.75.75 0 0 0-1.061 1.061l1.333 1.333a.748.748 0 0 0 1.06 0l2.667-2.667a.747.747 0 0 0 .002-1.06Z"
    />
  </svg>
);
export default ICUserCheckRegular;
