import {SVGProps} from "react";

function IconSetting({
  width = "24",
  height = "24",
  ...props
}: SVGProps<SVGSVGElement>) {
  return (
    <svg
      width={width}
      height={height}
      viewBox={`0 0 ${width} ${height}`}
      className={props.className}
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M19.2205 7.4381L20.0853 6.93605L19.2205 7.4381ZM12.3704 2.5H11.6304V4.5H12.3704V2.5ZM20.4458 7.55702L20.0853 6.93605L18.3556 7.94014L18.7161 8.5611L20.4458 7.55702ZM18.7154 15.4371L18.3551 16.0577L20.0848 17.0618L20.4451 16.4412L18.7154 15.4371ZM11.6308 21.5H12.3696V19.5H11.6308V21.5ZM3.55365 16.4415L3.91392 17.0621L5.6436 16.058L5.28334 15.4374L3.55365 16.4415ZM5.28297 8.56012L5.64246 7.94083L3.91278 6.93675L3.55328 7.55604L5.28297 8.56012ZM6.70855 7.66027C8.54302 8.71365 10.8636 7.40547 10.8636 5.26685H8.8636C8.8636 5.84476 8.22579 6.22521 7.70447 5.92587L6.70855 7.66027ZM5.64246 7.94083C5.85487 7.57493 6.33284 7.44453 6.70855 7.66027L7.70447 5.92587C6.3829 5.167 4.68297 5.60997 3.91278 6.93675L5.64246 7.94083ZM5.56269 9.60373C5.19637 9.39339 5.07099 8.92528 5.28297 8.56012L3.55328 7.55604C2.78482 8.87982 3.23954 10.576 4.56677 11.3381L5.56269 9.60373ZM5.56288 14.3941C7.41186 13.3324 7.41131 10.6652 5.56269 9.60373L4.56677 11.3381C5.07698 11.6311 5.0768 12.367 4.56696 12.6597L5.56288 14.3941ZM5.28334 15.4374C5.07137 15.0723 5.19674 14.6044 5.56288 14.3941L4.56696 12.6597C3.23974 13.4219 2.78529 15.1179 3.55365 16.4415L5.28334 15.4374ZM6.70874 16.3387C6.33352 16.5541 5.85607 16.424 5.6436 16.058L3.91392 17.0621C4.68383 18.3884 6.38298 18.832 7.70466 18.0731L6.70874 16.3387ZM10.8636 18.7328C10.8636 16.5947 8.54412 15.2848 6.70874 16.3387L7.70466 18.0731C8.22552 17.774 8.8636 18.1539 8.8636 18.7328H10.8636ZM11.6308 19.5C11.2071 19.5 10.8636 19.1565 10.8636 18.7328H8.8636C8.8636 20.2611 10.1025 21.5 11.6308 21.5V19.5ZM13.1368 18.7328C13.1368 19.1565 12.7933 19.5 12.3696 19.5V21.5C13.8979 21.5 15.1368 20.2611 15.1368 18.7328H13.1368ZM17.2911 16.3384C15.4552 15.2842 13.1368 16.5953 13.1368 18.7328H15.1368C15.1368 18.1532 15.7749 17.774 16.2952 18.0728L17.2911 16.3384ZM18.3551 16.0577C18.1426 16.4237 17.6658 16.5535 17.2911 16.3384L16.2952 18.0728C17.6168 18.8317 19.3152 18.3876 20.0848 17.0618L18.3551 16.0577ZM18.436 14.3943C18.8019 14.6045 18.9272 15.0721 18.7154 15.4371L20.4451 16.4412C21.2133 15.1177 20.7589 13.4219 19.4319 12.6599L18.436 14.3943ZM18.4364 9.60451C16.5882 10.6658 16.5871 13.3327 18.436 14.3943L19.4319 12.6599C18.9225 12.3674 18.9221 11.6318 19.4323 11.3389L18.4364 9.60451ZM18.7161 8.5611C18.928 8.92609 18.8027 9.39417 18.4364 9.60451L19.4323 11.3389C20.7593 10.5769 21.2143 8.88079 20.4458 7.55702L18.7161 8.5611ZM17.2909 7.65974C17.666 7.44431 18.1434 7.57446 18.3556 7.94014L20.0853 6.93605C19.3154 5.60982 17.6163 5.1666 16.295 5.92534L17.2909 7.65974ZM13.1368 5.26645C13.1368 7.40434 15.4563 8.71318 17.2909 7.65974L16.295 5.92534C15.7743 6.22432 15.1368 5.84447 15.1368 5.26645H13.1368ZM11.6304 2.5C10.1024 2.5 8.8636 3.73876 8.8636 5.26685H10.8636C10.8636 4.84333 11.2069 4.5 11.6304 4.5V2.5ZM12.3704 4.5C12.7937 4.5 13.1368 4.84315 13.1368 5.26645H15.1368C15.1368 3.73858 13.8982 2.5 12.3704 2.5V4.5Z"
        fill={props.fill || "currentColor"}
      />
      <path
        d="M12 14.5C13.3807 14.5 14.5 13.3807 14.5 12C14.5 10.6193 13.3807 9.5 12 9.5C10.6193 9.5 9.5 10.6193 9.5 12C9.5 13.3807 10.6193 14.5 12 14.5Z"
        stroke={props.fill || "currentColor"}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}

export default IconSetting;
