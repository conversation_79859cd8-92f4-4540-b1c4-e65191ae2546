import {useEffect, useState} from "react";
import {useDispatch, useSelector} from "react-redux";
import clsx from "clsx";
import {IRootState} from "@redux/store";
import {toggleLyrics} from "@redux/slices/PlayerSlice";
import {logEvent} from "src/utils/firebase";
import PlayerUtil from "src/core/PlayerUtil";
import {ArrowForwardIos as ArrowForwardIosIcon} from "@mui/icons-material";
import {useWindowWidth} from "src/utils/hooks";
import DesktopDetail from "./components/DesktopDetail";
import MobileDetail from "./components/MobileDetail";
import {ESongType, PlaylistType} from "src/types";
import {useTranslation} from "react-i18next";
import {IconButton} from "@mui/material";
import IconShare from "@components/Icon/IconShare";
import ModalShare from "@components/ModalShare";
import {toast} from "react-toastify";
import {useMutation} from "@tanstack/react-query";
import Color from "color";
import ApiSong from "@api/ApiSong";
import {generateShareLink} from "src/utils/global";
import ICMusicalNote from "@components/Icon/ICMusicalNote";

export function MusicPlayDetailsModal(): JSX.Element {
  const {t} = useTranslation();
  const isOpenLyrics = useSelector(
    (state: IRootState) => state.player?.isOpenLyrics,
  );
  const {currentSong} = useSelector((state: IRootState) => state.player);
  const windowWidth = useWindowWidth();
  const dispatch = useDispatch();

  const [activeSlide, setActiveSlide] = useState(0);
  const [openModalShare, setOpenModalShare] = useState(false);

  const shareMutate = useMutation({
    mutationFn: ApiSong.shareSong,
    onSuccess: () => {
      const link = generateShareLink({type: "song", data: currentSong});
      navigator.clipboard.writeText(link).then(() => {
        toast.success(t("common.copy_link_success"));
      });
    },
    onError: () => {
      toast.error(t("common.copy_link_failed"));
    },
  });

  const handleCopyLink = () => {
    shareMutate.mutateAsync(currentSong?.id ?? "");
  };

  const handleOpenModalShare = () => {
    setOpenModalShare(true);
  };

  const handleCloseModalShare = () => {
    setOpenModalShare(false);
  };

  useEffect(() => {
    if (isOpenLyrics) {
      logEvent("lyric_song", {
        song_id: currentSong?.id,
        song_name: currentSong?.name,
        song_artist: currentSong?.artists
          ?.map((artist) => artist?.stageName ?? artist?.name)
          .join(", "),
        play_position: PlayerUtil.instance.currentTime,
        is_song_liked: currentSong?.isLiked,
        like_count: currentSong?.totalLikes,
        share_count: currentSong?.totalShares,
      });
    }
  }, [isOpenLyrics]);

  const getPlaylistTitle = () => {
    const playlist = currentSong?.playlists?.[0];
    if (!playlist || !playlist.name) return "";
    switch (playlist.type) {
      case PlaylistType.ALBUM:
        return t("common.detail_song.from_album", {name: playlist.name});
      case PlaylistType.TOP_100:
        return t("common.detail_song.from_top_100", {name: playlist.name});
      default:
        return t("common.detail_song.from_playlist", {name: playlist.name});
    }
  };

  return (
    <div
      className={clsx(
        "lyric-container hidden-scrollbar fixed h-dvh w-full top-full flex flex-col left-0 px-2 lg:px-8 py-2 lg:py-5 text-center z-[29] backdrop-blur-lg duration-500 scroll-container",
        isOpenLyrics && "-translate-y-full",
      )}
      style={{
        background: `linear-gradient(to top,
        ${Color(currentSong?.keyColor || "#2D0707CC")
          .desaturate(0.5)
          .darken(0.8)
          .string()} 0%,
        ${Color(currentSong?.keyColor || "#2D0707CC")
          .desaturate(0)
          .darken(0.7)
          .string()} 50%,
        ${Color(currentSong?.keyColor || "#2D0707CC")
          .lighten(0.5)
          .darken(0.7)
          .string()} 100%)`,
      }}
    >
      <div className="flex items-center justify-between">
        <div
          onClick={() => dispatch(toggleLyrics())}
          className="text-white rotate-90 md:bg-[#FFFFFF17] md:h-10 md:w-10 rounded-full cursor-pointer flex justify-center items-center"
        >
          <ArrowForwardIosIcon fontSize="small" />
        </div>
        <div className="flex flex-col gap-y-1 md:hidden">
          {activeSlide !== 1 ? (
            <div className="text-[#FBFDFF] line-clamp-1">
              {activeSlide === 0
                ? currentSong?.name
                : t("common.playlist_common")}
            </div>
          ) : (
            <div className="font-semibold text-[#FBFDFF] flex gap-2 justify-center items-center">
              <ICMusicalNote width={16} height={16} /> {t("common.lyrics")}
            </div>
          )}
          {currentSong?.playlists?.length && currentSong.playlists?.[0].name ? (
            <div className="text-[#FFFFFF80] text-xs font-light line-clamp-1">
              {getPlaylistTitle()}
            </div>
          ) : null}
        </div>
        <div className="md:hidden">
          <IconButton
            className="cursor-pointer !p-0"
            onClick={handleOpenModalShare}
            disabled={currentSong?.type === ESongType.YOUTUBE}
          >
            <IconShare
              stroke={
                currentSong?.type === ESongType.YOUTUBE ? "grey" : undefined
              }
            />
          </IconButton>
        </div>
      </div>
      {windowWidth <= 678 ? (
        <MobileDetail onSlideChange={setActiveSlide} />
      ) : (
        <DesktopDetail handleOpenModalShare={handleOpenModalShare} />
      )}
      <ModalShare
        open={openModalShare}
        onCancel={handleCloseModalShare}
        handleCopyLink={handleCopyLink}
        image={currentSong?.images?.SMALL || currentSong?.images?.DEFAULT}
        name={currentSong?.name}
        artists={currentSong?.artists}
        shareUrl={generateShareLink({type: "song", data: currentSong})}
      />
    </div>
  );
}
