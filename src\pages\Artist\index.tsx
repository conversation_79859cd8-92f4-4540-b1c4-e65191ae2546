import ArtistBanner from "./components/ArtistBanner";
import Slider from "@components/Slider";
import {SwiperSlide} from "swiper/react";
import CommonAlbumCard from "@components/CommonAlbumCard";
import CommonArtistCard from "@components/CommonArtistCard";
import {useNavigate, useParams} from "react-router-dom";
import {useDispatch} from "react-redux";
import {useTranslation} from "react-i18next";
import AlbumCardSkeleton from "@components/AlbumCardSkeleton";
import ArtistCardSkeleton from "@components/ArtistCardSkeleton";
import QUERY_KEY from "@api/QueryKey";
import {useQuery} from "@tanstack/react-query";
import {IParamsDefault, ISong} from "src/types";
import ApiArtistDetail from "@api/ApiArtistDetail";
import SongItem from "@components/SongItem";
import {playSongFromList} from "@redux/slices/PlayerSlice";
import Subtitle from "@components/Subtitle";
import SongCardSkeleton from "@components/SongCardSkeleton";
import SubTitleSkeleton from "@components/SubTitleSkeleton";
import HeaderTitle from "@components/HeaderTitle";

export default function Artist() {
  const {t} = useTranslation();
  const {urlSlug} = useParams<{urlSlug: string}>();
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const {data: artistDetails, refetch: refetchDetail} = useQuery({
    queryKey: [QUERY_KEY.ARTIST.GET_ARTIST_DETAIL, urlSlug],
    queryFn: () => ApiArtistDetail.getArtistDetail(urlSlug || ""),
    enabled: !!urlSlug,
  });

  const songsParams: IParamsDefault = {
    page: 0,
    pageSize: 200,
  };

  const {data: artistSongs, isLoading: isLoadingSongs} = useQuery({
    queryKey: [QUERY_KEY.ARTIST.GET_ARTIST_SONGS, urlSlug, songsParams],
    queryFn: () => ApiArtistDetail.getArtistSongs(urlSlug || "", songsParams),
    enabled: !!urlSlug,
  });

  const playlistsParams: IParamsDefault = {
    page: 0,
    pageSize: 12,
  };

  const {data: artistPlaylists, isLoading: isLoadingPlaylist} = useQuery({
    queryKey: [QUERY_KEY.ARTIST.GET_ARTIST_PLAYLISTS, urlSlug, playlistsParams],
    queryFn: () =>
      ApiArtistDetail.getArtistPlaylists(urlSlug || "", playlistsParams),
    enabled: !!urlSlug,
  });

  const {data: artistFavoritePlaylists, isLoading: isLoadingFavorite} =
    useQuery({
      queryKey: [
        QUERY_KEY.ARTIST.GET_ARTIST_FAVORITE_PLAYLISTS,
        urlSlug,
        playlistsParams,
      ],
      queryFn: () =>
        ApiArtistDetail.getArtistFavoritePlaylists(
          urlSlug || "",
          playlistsParams,
        ),
      enabled: !!urlSlug,
    });

  const artistsParams: IParamsDefault = {
    page: 0,
    pageSize: 12,
  };

  const {data: collabArtist, isLoading: isLoadingCollabArtist} = useQuery({
    queryKey: [QUERY_KEY.ARTIST.GET_COLLAB_ARTIST, urlSlug, artistsParams],
    queryFn: () =>
      ApiArtistDetail.getArtistCollab(urlSlug || "", artistsParams),
    enabled: !!urlSlug,
  });

  const handlePlayAritstSongs = (startSong?: ISong) => {
    dispatch(
      playSongFromList({
        song: startSong,
        songList: artistSongs?.data,
        artistId: artistDetails?.id,
      }),
    );
  };

  return (
    <div className="mb-[5vh]">
      <HeaderTitle title={t("common.artist")} name={artistDetails?.stageName} />
      <ArtistBanner
        data={artistDetails}
        refetch={refetchDetail}
        handlePlayAllSongs={handlePlayAritstSongs}
      />

      {isLoadingSongs && !artistSongs && (
        <div className="px-4 md:px-6 sm:ml-6 mt-6 flex flex-col gap-2 sm:gap-3 md:gap-4 lg:gap-5">
          <SubTitleSkeleton />
          <div className="relative flex flex-col">
            {Array.from({length: 5}).map((_, index) => (
              <SongCardSkeleton key={`skeleton_song_${index}`} />
            ))}
          </div>
        </div>
      )}
      {artistSongs?.data && artistSongs?.data?.length > 0 && (
        <div className="px-4 md:px-6 sm:ml-6 mt-6 flex flex-col gap-2 sm:gap-3 md:gap-4 lg:gap-5">
          <Subtitle subtitle={`${t("common.songs")}`} seeMore={false} />
          <div className="max-sm:-mx-2">
            {artistSongs?.data?.map((song, index) => (
              <SongItem
                song={song}
                key={song.id}
                className="pl-4"
                showReleaseDate={true}
                left={
                  <div className="flex gap-x-6 items-center mr-6" key={index}>
                    <div className="text-base text-center lg:w-10 md:w-8 sm:w-6 w-4 text-white font-normal">
                      {index + 1}
                    </div>
                  </div>
                }
                handlePlayMusic={() => {
                  handlePlayAritstSongs(song);
                }}
              />
            ))}
          </div>
        </div>
      )}

      {artistFavoritePlaylists?.data?.length !== 0 && (
        <>
          {isLoadingFavorite && (
            <div className="px-4 md:px-6 sm:ml-6 mt-6 flex flex-col gap-2 sm:gap-3 md:gap-4 lg:gap-5">
              <SubTitleSkeleton />
              <Slider slidesPerView={5.5}>
                {[...Array(6)].map((_, index) => (
                  <SwiperSlide
                    key={`favorite_playlist_${index}`}
                    virtualIndex={index}
                  >
                    <AlbumCardSkeleton />
                  </SwiperSlide>
                ))}
              </Slider>
            </div>
          )}
          {(artistFavoritePlaylists?.data?.length ?? 0) > 0 && (
            <div className="px-4 md:px-6 sm:ml-6 mt-6 flex flex-col gap-2 sm:gap-3 md:gap-4 lg:gap-5">
              <Subtitle
                subtitle={`${t("common.favorite_albums")}`}
                seeMore={false}
              />
              <Slider slidesPerView={5.5}>
                {artistFavoritePlaylists?.data?.map((album, index) => (
                  <SwiperSlide
                    key={`favorite_playlist_${album.id}`}
                    virtualIndex={index}
                  >
                    <CommonAlbumCard data={album} />
                  </SwiperSlide>
                ))}
              </Slider>
            </div>
          )}
        </>
      )}
      {artistPlaylists?.data?.length !== 0 && (
        <>
          {isLoadingPlaylist && (
            <div className="px-4 md:px-6 sm:ml-6 mt-6 flex flex-col gap-2 sm:gap-3 md:gap-4 lg:gap-5">
              <SubTitleSkeleton />
              <Slider slidesPerView={5.5}>
                {[...Array(6)].map((_, index) => (
                  <SwiperSlide
                    key={`artist_playlist_${index}`}
                    virtualIndex={index}
                  >
                    <AlbumCardSkeleton isMultipleInfo={false} />
                  </SwiperSlide>
                ))}
              </Slider>
            </div>
          )}
          {(artistPlaylists?.data?.length ?? 0) > 0 && (
            <div className="px-4 md:px-6 sm:ml-6 mt-6 flex flex-col gap-2 sm:gap-3 md:gap-4 lg:gap-5">
              <Subtitle
                subtitle={`${artistDetails?.name} & ${t("common.playlist")}`}
                handleClick={() => navigate(`/artist/${urlSlug}/playlist`)}
                seeMore={artistPlaylists && artistPlaylists?.data.length > 4}
              />
              <Slider slidesPerView={5.5}>
                {artistPlaylists?.data?.map((album, index) => (
                  <SwiperSlide
                    key={`artist_playlist_${album.id}`}
                    virtualIndex={index}
                  >
                    <CommonAlbumCard data={album} haveLayer={false} />
                  </SwiperSlide>
                ))}
              </Slider>
            </div>
          )}
        </>
      )}

      {collabArtist?.data?.length !== 0 && (
        <>
          {isLoadingCollabArtist && (
            <div className="px-4 md:px-6 sm:ml-6 mt-6 flex flex-col gap-2 sm:gap-3 md:gap-4 lg:gap-5">
              <SubTitleSkeleton />
              <Slider slidesPerView={6}>
                {[...Array(6)].map((_, index) => (
                  <SwiperSlide
                    key={`artist_collab_${index}`}
                    virtualIndex={index}
                  >
                    <ArtistCardSkeleton />
                  </SwiperSlide>
                ))}
              </Slider>
            </div>
          )}
          {(collabArtist?.data?.length ?? 0) > 0 && (
            <div className="px-4 md:px-6 sm:ml-6 mt-6 flex flex-col gap-2 sm:gap-3 md:gap-4 lg:gap-5">
              <Subtitle
                subtitle={`${t("common.singers_combination")}`}
                seeMore={false}
              />
              <Slider slidesPerView={6}>
                {collabArtist?.data?.map((artist, index) => (
                  <SwiperSlide
                    key={`artist_collab_${artist.id}`}
                    virtualIndex={index}
                  >
                    <CommonArtistCard data={artist} />
                  </SwiperSlide>
                ))}
              </Slider>
            </div>
          )}
        </>
      )}
    </div>
  );
}
