import {ErrorMessage} from "formik";
import {TextareaAutosize} from "@mui/material";

interface TextAreaCustomerProps {
  name: string;
  label: string;
  required?: boolean;
  minRows?: number;
  maxRows?: number;
  placeholder?: string;
  value?: string;
  onChange?: (event: React.ChangeEvent<HTMLTextAreaElement>) => void;
}

const TextAreaCustomer = ({
  name,
  label,
  required = false,
  minRows = 0,
  maxRows = 14,
  ...props
}: TextAreaCustomerProps) => {
  return (
    <div>
      <label className="block text-sm font-bold text-gray-700 pb-2">
        {label}
        {required && <span className="text-red-600"> *</span>}
      </label>
      <TextareaAutosize
        name={name}
        minRows={minRows}
        maxRows={maxRows}
        style={{
          width: "100%",
          borderRadius: "2px",
          background: "#fff",
          paddingTop: "8px",
          paddingBottom: "8px",
          paddingLeft: "12px",
          paddingRight: "12px",
          border: "1px solid #D9D9D9",
          fontFamily: "inherit",
          fontSize: "inherit",
          boxShadow: "0px 2px 4px rgba(0, 0, 0, 0.1)",
        }}
        {...props}
      />
      <div className="text-red-500 text-sm">
        <ErrorMessage name={name} />
      </div>
    </div>
  );
};

export default TextAreaCustomer;
