import Header<PERSON>hart from "./HeaderChart";
import AreaChartCustom from "./AreaChartCustom";
import {useTranslation} from "react-i18next";

function MusicSpeedDownload() {
  const {t} = useTranslation();

  const data = [
    {name: t("cms.dashboard.mon"), hour: 10},
    {name: t("cms.dashboard.tue"), hour: 20},
    {name: t("cms.dashboard.wed"), hour: 25},
    {name: t("cms.dashboard.thu"), hour: 35},
    {name: t("cms.dashboard.fri"), hour: 40},
    {name: t("cms.dashboard.sat"), hour: 45},
    {name: t("cms.dashboard.sun"), hour: 50},
  ];

  const LINEARGRADIENT = {
    id: "colorBlue",
    linear: [
      {offset: "0%", color: "#0F96FF", stopOpacity: 0.8},
      {offset: "100%", color: "#0F96FF", stopOpacity: 0},
    ],
  };

  const labels = {
    x: t("cms.dashboard.time"),
    y: t("cms.dashboard.mbs"),
  };

  return (
    <div className="bg-white rounded-2xl shadow-md p-5">
      <HeaderChart title={t("cms.dashboard.speed_download")} />
      <AreaChartCustom
        data={data}
        linearGradientColor={LINEARGRADIENT}
        labels={labels}
      />
    </div>
  );
}

export default MusicSpeedDownload;
