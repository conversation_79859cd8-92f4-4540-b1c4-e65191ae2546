import HeaderTitle from "@components/HeaderTitle";
import Icon404 from "@components/Icon/Icon404";
import {useTranslation} from "react-i18next";
import {useNavigate} from "react-router-dom";

interface INotFoundProps {
  className?: string;
}

export default function NotFound({className}: INotFoundProps) {
  const navigate = useNavigate();
  const {t} = useTranslation();

  return (
    <div
      id="error-page"
      className={`${className} bg-[linear-gradient(#340707,#1B0606_20%,#090404)] flex gap-4 flex-col items-center justify-center w-screen h-screen`}
    >
      <HeaderTitle />
      <div className="flex flex-col justify-center items-center gap-2">
        <Icon404 className="w-full h-full" />
        <span className="text-white sm:text-base text-sm">
          {t("404.not_found_page")}
        </span>
      </div>
      <button
        className="text-orange-500 sm:text-base text-sm"
        onClick={() => navigate("/")}
      >
        {t("404.back_to_home")}
      </button>
    </div>
  );
}
