import {SVGProps} from "react";

function IconTrashSearch({
  width = "24",
  height = "24",
  ...props
}: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox={`0 0 ${width} ${height}`}
      fill="none"
      {...props}
    >
      <path
        d="M5 7.3335H19"
        stroke={props.stroke || "#ffffff"}
        strokeWidth="0.972222"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M17.4446 7.3335V18.2224C17.4446 19.0002 16.6668 19.7779 15.889 19.7779H8.11122C7.33344 19.7779 6.55566 19.0002 6.55566 18.2224V7.3335"
        stroke={props.stroke || "#ffffff"}
        strokeWidth="0.972222"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M8.88867 7.33328V5.77772C8.88867 4.99995 9.66645 4.22217 10.4442 4.22217H13.5553C14.3331 4.22217 15.1109 4.99995 15.1109 5.77772V7.33328"
        stroke={props.stroke || "#ffffff"}
        strokeWidth="0.972222"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M10.4443 11.2222V15.8888"
        stroke={props.stroke || "#ffffff"}
        strokeWidth="0.972222"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M13.5557 11.2222V15.8888"
        stroke={props.stroke || "#ffffff"}
        strokeWidth="0.972222"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}

export default IconTrashSearch;
