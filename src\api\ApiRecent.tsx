import {IPlaylist, IParamsDefault, ISong} from "src/types";
import {fetcherWithMetadata, IDataWithMeta} from "./Fetcher";

const path = {
  songRecents: "songs/recently-listen",
  recentPlaylists: "playlists/recently-listen",
};

function recentSongs(params: IParamsDefault): Promise<IDataWithMeta<ISong[]>> {
  return fetcherWithMetadata(
    {
      url: path.songRecents,
      method: "get",
      params,
    },
    {
      displayError: false,
    },
  );
}

function recentPlaylists(
  params: IParamsDefault,
): Promise<IDataWithMeta<IPlaylist[]>> {
  return fetcherWithMetadata(
    {
      url: path.recentPlaylists,
      method: "get",
      params,
    },
    {
      displayError: false,
    },
  );
}

export default {
  recentSongs,
  recentPlaylists,
};
