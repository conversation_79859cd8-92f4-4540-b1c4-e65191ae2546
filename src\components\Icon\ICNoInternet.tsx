import {SVGProps} from "react";

export default function ICNoInternet({
  width = "106",
  height = "85",
  ...props
}: SVGProps<SVGSVGElement>) {
  return (
    <svg
      width={width}
      height={height}
      viewBox={`0 0 ${width} ${height}`}
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M103.243 34.1751C96.5611 27.582 88.9389 22.3867 80.3771 18.5894C71.8152 14.7921 62.6478 12.8934 52.8747 12.8934C50.7029 12.8934 48.552 12.9978 46.422 13.2064C44.292 13.4151 42.5169 13.6863 41.0969 14.0201L37.5887 10.6401C39.3428 10.1393 41.4728 9.74292 43.9787 9.45082C46.4846 9.15872 49.45 9.01267 52.8747 9.01267C63.316 9.01267 73.0473 11.0156 82.0686 15.0216C91.0898 19.0276 99.067 24.5358 106 31.5462L103.243 34.1751ZM82.9456 54.3301C81.8597 53.3286 80.8991 52.494 80.0638 51.8264C79.2285 51.1587 78.1426 50.4911 76.8061 49.8234L69.9149 42.813C71.8361 43.3137 74.1123 44.3987 76.7435 46.0678C79.3747 47.737 82.0268 49.9068 84.6998 52.5775L82.9456 54.3301ZM93.3452 83.6237L52.7494 42.9381C47.1529 43.0216 41.7861 44.19 36.6489 46.4434C31.5118 48.6967 27.1891 51.7012 23.6809 55.4568L21.0496 52.8279C24.5579 49.2392 28.7136 46.1721 33.5165 43.6267C38.3195 41.0812 43.4775 39.5581 48.9905 39.0574L27.565 17.7757C23.5556 19.1945 19.2537 21.427 14.6596 24.4732C10.0654 27.5194 6.01418 30.7533 2.50591 34.1751L0 31.5462C3.67533 27.8741 7.68479 24.5149 12.0284 21.4687C16.3719 18.4225 20.4649 16.1066 24.3073 14.5209L12.279 2.50298L14.2837 0.5L95.4752 81.6207L93.3452 83.6237ZM52.8747 84.5L41.974 73.6088C43.394 72.19 45.0229 71.0842 46.8605 70.2913C48.6982 69.4985 50.7029 69.1021 52.8747 69.1021C55.0465 69.1021 57.0512 69.4985 58.8889 70.2913C60.7266 71.0842 62.3554 72.19 63.7754 73.6088L52.8747 84.5Z"
        fill={props.fill}
      />
    </svg>
  );
}
