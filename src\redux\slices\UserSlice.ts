import {createSlice, PayloadAction} from "@reduxjs/toolkit";
import {IAccountInfo} from "src/types";

const initialState: IAccountInfo = {};

const UserSlice = createSlice({
  name: "user",
  initialState,
  reducers: {
    loginUser: (_, action: PayloadAction<IAccountInfo>) => {
      return action.payload;
    },
    logoutUser: () => {
      return initialState;
    },
    updateUserInfo: (state, action: PayloadAction<Partial<IAccountInfo>>) => {
      return {...state, ...action.payload};
    },
  },
});

// Action creators are generated for each case reducer function
export const {loginUser, logoutUser, updateUserInfo} = UserSlice.actions;

export default UserSlice.reducer;
