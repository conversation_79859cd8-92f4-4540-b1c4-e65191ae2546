import {AudioQualityTypeEnum, ISong} from "src/types";
import {EPlayerType, IBasePlayer} from "./types";
import PlayerListenerHandler from "./PlayerListenerHandler";
import PlayerUtil from "./PlayerUtil";
import {t} from "i18next";
import store from "@redux/store";

// TODO: implement youtube player
export default class YoutubePlayer implements IBasePlayer {
  _player: YT.Player | null = null;
  type = EPlayerType.YOUTUBE;
  ready = false;
  private playerReady = false;
  private urlSlug = "";
  private deferActions: (() => void)[] = [];
  private iframeId =
    window.innerWidth <= 834
      ? "mobile-youtube-player"
      : "desktop-youtube-player";

  constructor() {
    window.onYouTubeIframeAPIReady = () => {
      this._player = new YT.Player(this.iframeId, {
        height: "0",
        width: "0",
        playerVars: {
          enablejsapi: 1,
          autoplay: 0,
          disablekb: 1,
        },
        events: {
          onReady: (e) => {
            this.playerReady = true;
            if (this.deferActions.length) {
              this.deferActions.forEach((action) => action());
            }
            const iframeWindow = e.target.getIframe().contentWindow;
            if (iframeWindow) {
              window.addEventListener("message", (event) => {
                if (event.source === iframeWindow) {
                  const data = JSON.parse(event.data);
                  if (
                    data.event === "infoDelivery" &&
                    data.info &&
                    data.info.currentTime
                  ) {
                    PlayerListenerHandler.handleOnTimeChange(this);
                  }
                }
              });
            }
          },
          onError(event) {
            const errorCode = event.data;
            switch (errorCode) {
              case 2:
                PlayerListenerHandler.handleOnError(
                  new Error(t("youtube_error.invalid_param")),
                );
                break;
              case 5:
                PlayerListenerHandler.handleOnError(
                  new Error(t("youtube_error.html5_error")),
                );
                break;
              case 100:
                PlayerListenerHandler.handleOnError(
                  new Error(t("youtube_error.not_found_or_private")),
                );
                break;
              case 101: // Fall through to 150 as they are the same
              case 150:
                PlayerListenerHandler.handleOnError(
                  new Error(t("youtube_error.embedded_not_available")),
                );
                break;
              case 105:
                PlayerListenerHandler.handleOnError(
                  new Error(t("youtube_error.too_short")),
                );
                break;
              default:
                // Handle other potential errors not specifically listed
                PlayerListenerHandler.handleOnError(
                  new Error(t("youtube_error.unknown_code_error", {errorCode})),
                );
            }
          },
          onStateChange: (event) => {
            const state = event.data;
            switch (state) {
              case YT.PlayerState.ENDED:
                PlayerListenerHandler.handleOnEnded(this);
                break;
              case YT.PlayerState.PLAYING:
                PlayerListenerHandler.handleOnPlay(this);
                break;
              case YT.PlayerState.PAUSED:
                PlayerListenerHandler.handleOnPause(this);
                break;
              case YT.PlayerState.UNSTARTED:
                if (!this.ready) {
                  PlayerListenerHandler.handleOnReady(this);
                  this.ready = true;
                }
                PlayerListenerHandler.handleOnLoadedMetadata(this);
                PlayerListenerHandler.handleOnStart(this);
                PlayerListenerHandler.handleOndurationChange(this);
                break;
              default:
                break;
            }
          },
        },
      });
    };
  }
  get paused() {
    if (!this.playerReady) return true;
    return this._player?.getPlayerState() === YT.PlayerState.PAUSED;
  }
  get currentTime() {
    if (!this.playerReady) return 0;
    return this._player?.getCurrentTime?.() || 0;
  }
  get duration() {
    if (!this.playerReady) return 0;
    return this._player?.getDuration?.() || 0;
  }
  get volume() {
    if (this._player?.getVolume) {
      return this._player.getVolume() / 100;
    }
    return PlayerUtil.getLocalValue("PLAYER_VOLUME").volume;
  }
  get muted() {
    return this._player?.isMuted?.() || false;
  }

  isSongPlaying(data?: ISong): boolean {
    return data?.urlSlug === this.urlSlug;
  }

  loadSong(songData: ISong) {
    if (!songData?.urlSlug) {
      PlayerListenerHandler.handleOnError(
        new Error(t("youtube_error.unsupported_format")),
      );
    } else {
      this.urlSlug = songData.urlSlug;
      const musicQuality =
        store.getState().settings.musicQuality === AudioQualityTypeEnum.KBPS_128
          ? "small"
          : "large";
      if (!this.playerReady) {
        this.deferActions.push(() => this.attachIframe());
        if (!this.ready) {
          this.deferActions.push(() =>
            this._player?.cueVideoById(this.urlSlug, 0, musicQuality),
          );
        } else {
          this.deferActions.push(() =>
            this._player?.loadVideoById(this.urlSlug, 0, musicQuality),
          );
        }
      } else {
        this.attachIframe();
        if (!this.ready) {
          this._player?.cueVideoById(songData.urlSlug, 0, musicQuality);
        } else {
          this._player?.loadVideoById(songData.urlSlug, 0, musicQuality);
        }
      }
    }
  }
  play() {
    if (!this.playerReady) {
      this.deferActions.push(() => this._player?.playVideo());
    }
    this._player?.playVideo();
  }
  pause() {
    if (!this.playerReady) {
      this.deferActions.push(() => this._player?.pauseVideo());
    }
    this._player?.pauseVideo();
  }
  seek(time: number) {
    if (!this.playerReady) {
      this.deferActions.push(() => this._player?.seekTo(time, true));
    } else {
      this._player?.seekTo(time, true);
    }
  }
  setVolume(volume: number) {
    const realVolume = volume * 100;
    if (!this.playerReady) {
      this.deferActions.push(() => this._player?.setVolume(realVolume));
    } else {
      this._player?.setVolume(realVolume);
    }
  }
  mute() {
    if (!this.playerReady) {
      this.deferActions.push(() => this._player?.mute());
    } else {
      this._player?.mute();
    }
  }
  unMute() {
    if (!this.playerReady) {
      this.deferActions.push(() => this._player?.unMute());
    } else {
      this._player?.unMute();
    }
  }

  clear() {
    this._player?.stopVideo();
    this.deattachIframe();
    this.urlSlug = "";
  }

  private attachIframe() {
    if (this.iframeId === "mobile-youtube-player") {
      const width = window.innerWidth - 16;
      this._player?.setSize(width, (width / 16) * 9);
    } else {
      this._player?.setSize(350, 196);
    }
  }
  private deattachIframe() {
    this._player?.setSize(0, 0);
  }
}
