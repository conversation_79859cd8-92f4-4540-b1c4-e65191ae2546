import ApiLibrary from "@api/ApiLibrary";
import ApiSong from "@api/ApiSong";
import QUERY_KEY from "@api/QueryKey";
import IconAdd from "@components/Icon/IconAdd";
import ModalCreatePlaylist from "@components/ModalCreatePlaylist";
import PlaylistItemCard from "@components/PlaylistItemCard";
import CloseIcon from "@mui/icons-material/Close";
import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
  Skeleton,
} from "@mui/material";
import {useMutation, useQuery, useQueryClient} from "@tanstack/react-query";
import {useEffect, useState} from "react";
import {useTranslation} from "react-i18next";
import {toast} from "react-toastify";
import {IPlaylist, ISong} from "src/types";
import {logEvent} from "src/utils/firebase";

interface IAddPlaylistModalProps {
  open: boolean;
  onClose: () => void;
  songData?: ISong;
}

export default function ModalAddToPlaylist({
  open,
  songData,
  onClose,
}: IAddPlaylistModalProps): JSX.Element {
  const {t} = useTranslation();
  const queryClient = useQueryClient();
  const [openModal, setOpenModal] = useState<boolean>(false);

  const {data: playlists, isLoading: isPlaylistLoading} = useQuery({
    queryKey: [QUERY_KEY.LIBRARY.GET_MY_PLAYLISTS, songData?.id],
    queryFn: () => ApiLibrary.getMyPlaylists({page: 0, pageSize: 200}),
    enabled: open,
  });

  const {data: includedPlaylist, isLoading: isSongLoading} = useQuery({
    queryKey: [QUERY_KEY.LIBRARY.IS_SONG_IN_PLAYLISTS, songData?.id],
    queryFn: () => ApiSong.getSongIncludedInPlaylist(songData?.id ?? ""),
    enabled: open && !!songData?.id,
  });

  const addAndRemoveMutate = useMutation({
    mutationFn: ({songId, playlistId}: {songId: string; playlistId: string}) =>
      ApiSong.addAndRemoveSongToPlaylist({songId, playlistId}),
  });

  const [checkedItems, setCheckedItems] = useState<Record<string, boolean>>({});

  const handleOpenCreatePlaylistModal = () => {
    setOpenModal(true);
  };

  const handleCloseModal = () => {
    setOpenModal(false);
  };

  useEffect(() => {
    if (includedPlaylist && !isSongLoading) {
      const initialChecked: Record<string, boolean> = {};
      if (Array.isArray(includedPlaylist)) {
        includedPlaylist?.forEach((playlist: IPlaylist) => {
          if (playlist?.id) {
            initialChecked[playlist.id] = true;
          }
        });
      }
      setCheckedItems(initialChecked);
    }
  }, [includedPlaylist, isSongLoading]);

  const handleToggleSongInPlaylist = (
    songId: string,
    playlistId: string,
    newCheckedState: boolean,
  ) => {
    addAndRemoveMutate.mutate(
      {songId, playlistId},
      {
        onSuccess: () => {
          queryClient.invalidateQueries({
            queryKey: [QUERY_KEY.LIBRARY.GET_MY_PLAYLISTS],
          });
          queryClient.invalidateQueries({
            queryKey: [QUERY_KEY.LIBRARY.IS_SONG_IN_PLAYLISTS],
          });
          queryClient.invalidateQueries({
            queryKey: [QUERY_KEY.PLAYLIST.GET_PLAYLIST_SONGS],
          });
          if (newCheckedState) {
            toast.success(t("common.add_to_playlist_successfully"));
            logEvent("add_to_playlist", {
              song_id: songId,
              song_name: songData?.name,
              song_artist: songData?.artists
                ?.map((artist) => artist?.stageName ?? artist?.name)
                .join(", "),
              playlist_id: playlistId,
              quality: songData?.audios?.[0]?.type,
            });
          } else {
            toast.success(t("common.remove_from_playlist_successfully"));
            logEvent("remove_from_playlist", {
              song_id: songId,
              song_name: songData?.name,
              song_artist: songData?.artists
                ?.map((artist) => artist?.stageName ?? artist?.name)
                .join(", "),
              playlist_id: playlistId,
              quality: songData?.audios?.[0]?.type,
              song_liked: songData?.isLiked,
            });
          }
        },
        onError: () => {
          toast.error(t("common.action_error"));
        },
      },
    );
  };

  const handleCheckToggle = (playlistId: string) => {
    setCheckedItems((prev) => {
      const isCurrentlyChecked = !!prev[playlistId];
      const newCheckedState = !isCurrentlyChecked;

      if (songData?.id) {
        handleToggleSongInPlaylist(songData.id, playlistId, newCheckedState);
      }

      return {
        ...prev,
        [playlistId]: newCheckedState,
      };
    });
  };

  return (
    <>
      <Dialog
        open={open}
        onClose={onClose}
        fullWidth
        sx={{
          "& .MuiDialog-paper": {
            backgroundColor: "#1C1717",
            color: "#E3E3E3",
            width: "444px",
            display: "flex",
            height: "540px",
            flexDirection: "column",
            justifyContent: "center",
            padding: "24px",
            gap: "4px",
            borderRadius: "12px",
            overflowY: "auto",
          },
        }}
      >
        <DialogTitle
          sx={{
            display: "flex",
            flexDirection: "row",
            alignItems: "center",
            justifyContent: "space-between",
            padding: "0",
            fontSize: "16px",
            fontWeight: "400",
          }}
        >
          {t("common.menu.add_to_playlist")}
          <IconButton
            aria-label="close"
            onClick={onClose}
            sx={{color: "#FFFFFF", fontSize: "16px"}}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <div className="w-full h-0 border-solid border-[#FFFFFF0D] border-[1px] mb-4" />
        <DialogContent sx={{p: 0}}>
          <div className="w-full flex flex-col">
            <div
              className="flex flex-row items-center gap-3 hover:bg-[#FFFFFF17] py-2 cursor-pointer px-3"
              onClick={handleOpenCreatePlaylistModal}
            >
              <div className="bg-[#FFFFFF1A] rounded-full h-[52px] w-[52px] flex items-center justify-center">
                <IconAdd />
              </div>
              {t("playlist.add_new")}
            </div>
            <div>
              {isPlaylistLoading || isSongLoading ? (
                <div className="relative overflow-y-auto flex flex-col gap-3 mt-4">
                  {Array.from({length: 4}).map((_, index) => (
                    <Skeleton
                      key={index}
                      variant="rectangular"
                      animation="wave"
                      sx={{
                        "bgcolor": "#471A1A",
                        "&::after": {
                          bgcolor: "#571d1d",
                        },
                        "height": "66px",
                        "borderRadius": "8px",
                      }}
                    />
                  ))}
                </div>
              ) : (
                playlists?.data &&
                playlists.data.length > 0 &&
                playlists.data.map((playlist, index) => (
                  <div
                    key={index}
                    className="flex flex-row items-center gap-3 hover:bg-[#FFFFFF17] py-2 cursor-pointer"
                  >
                    <PlaylistItemCard
                      checked={
                        playlist?.id
                          ? checkedItems[playlist.id] || false
                          : false
                      }
                      data={playlist}
                      onClick={() =>
                        playlist?.id && handleCheckToggle(playlist.id)
                      }
                    />
                  </div>
                ))
              )}
            </div>
          </div>
        </DialogContent>
        <DialogActions>
          <div className="w-full flex justify-center items-center mt-2">
            <Button
              variant="contained"
              color="error"
              sx={{
                bgcolor: "#FF4319",
                width: "126px",
                borderRadius: "20px",
                height: "39px",
                textTransform: "none",
                fontSize: "16px",
                fontWeight: "500",
              }}
              onClick={onClose}
            >
              {t("common.completed")}
            </Button>
          </div>
        </DialogActions>
      </Dialog>
      <ModalCreatePlaylist open={openModal} onClose={handleCloseModal} />
    </>
  );
}
