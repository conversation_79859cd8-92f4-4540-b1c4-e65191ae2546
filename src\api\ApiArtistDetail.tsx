import {IArtist, IParamsDefault, IPlaylist, ISong} from "src/types";
import {fetcher, fetcherWithMetadata, IDataWithMeta} from "./Fetcher";

export interface IArtistParam {
  page?: number;
  pageSize?: number;
  order?: string;
  direction?: string;
  keyword?: string;
}

const path = {
  artists: "artists",
  likeArtist: "interaction-artist/like",
  shareArtist: "interaction-artist/share",
};

function getArtistDetail(urlSlug: string): Promise<IArtist> {
  return fetcher<IArtist>(
    {
      url: `/${path.artists}/${urlSlug}`,
      method: "get",
    },
    {
      displayError: false,
    },
  );
}

function getArtistSongs(
  urlSlug: string,
  params: IParamsDefault,
): Promise<IDataWithMeta<ISong[]>> {
  return fetcherWithMetadata<ISong[]>(
    {
      url: `/${path.artists}/${urlSlug}/songs`,
      method: "get",
      params,
    },
    {
      displayError: false,
      withMetadata: true,
    },
  );
}

function shareArtist(id: string): Promise<IArtist> {
  return fetcher(
    {
      url: path.shareArtist,
      method: "post",
      data: {
        artistId: id,
      },
    },
    {
      displayError: false,
    },
  );
}

function getArtistPlaylists(
  urlSlug: string,
  params: IParamsDefault,
): Promise<IDataWithMeta<IPlaylist[]>> {
  return fetcherWithMetadata<IPlaylist[]>(
    {
      url: `/${path.artists}/${urlSlug}/playlists`,
      method: "get",
      params,
    },
    {
      displayError: false,
      withMetadata: true,
    },
  );
}

function getArtistFavoritePlaylists(
  urlSlug: string,
  params: IParamsDefault,
): Promise<IDataWithMeta<IPlaylist[]>> {
  return fetcherWithMetadata<IPlaylist[]>(
    {
      url: `/${path.artists}/${urlSlug}/favorite-playlists`,
      method: "get",
      params,
    },
    {
      displayError: false,
      withMetadata: true,
    },
  );
}

function getArtistCollab(
  urlSlug: string,
  params: IParamsDefault,
): Promise<IDataWithMeta<IArtist[]>> {
  return fetcherWithMetadata<IArtist[]>(
    {
      url: `/${path.artists}/${urlSlug}/collabArtist`,
      method: "get",
      params,
    },
    {
      displayError: false,
      withMetadata: true,
    },
  );
}

function likeArtist(id: string): Promise<IArtist> {
  return fetcher(
    {
      url: path.likeArtist,
      method: "post",
      data: {
        artistId: id,
      },
    },
    {
      displayError: false,
    },
  );
}

export default {
  getArtistDetail,
  getArtistSongs,
  getArtistPlaylists,
  getArtistFavoritePlaylists,
  getArtistCollab,
  shareArtist,
  likeArtist,
};
