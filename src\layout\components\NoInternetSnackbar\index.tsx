import {Box} from "@mui/system";
import {<PERSON>ert, Slide, SlideProps, Snackbar} from "@mui/material";
import {useNetworkStatus} from "src/utils/hooks";
import {useTranslation} from "react-i18next";
import {useEffect, useState} from "react";

function SlideTransition(props: SlideProps) {
  return <Slide {...props} direction="up" />;
}

export default function NoInternetSnackbar({className}: {className?: string}) {
  const {t} = useTranslation();
  const {isOnline} = useNetworkStatus();
  const [open, setOpen] = useState(false);

  useEffect(() => {
    if (!isOnline) {
      setOpen(true);
      return;
    }
    const timer = setTimeout(() => setOpen(false), 4000);
    return () => clearTimeout(timer);
  }, [isOnline]);

  return (
    <div className={className}>
      <Box sx={{maxWidth: 500, width: "100%"}}>
        <Snackbar
          anchorOrigin={{vertical: "bottom", horizontal: "right"}}
          open={open}
          key={"bottom" + "right"}
          sx={{
            "width": "100vw",
            "left": "0",
            "right": "0",
            "bottom": "0",
            "@media (min-width: 600px)": {
              left: "0",
              right: "0",
              bottom: "0",
            },
            ".MuiPaper-root": {
              fontSize: "10px",
              display: "flex",
              height: "20px",
              alignItems: "center",
              borderRadius: "0",
              justifyContent: "center",
            },
          }}
          slots={{transition: SlideTransition}}
          TransitionProps={{mountOnEnter: true, unmountOnExit: true}}
        >
          <Alert
            severity={isOnline ? "success" : "info"}
            variant="filled"
            sx={{
              "width": "100vw",
              ".MuiSvgIcon-root": {
                display: "none",
              },
            }}
          >
            {isOnline ? t("common.you_online") : t("common.you_offline")}
          </Alert>
        </Snackbar>
      </Box>
    </div>
  );
}
