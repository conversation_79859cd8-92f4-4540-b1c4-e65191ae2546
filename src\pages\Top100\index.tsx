import {useTranslation} from "react-i18next";
import QUERY_KEY from "@api/QueryKey";
import ApiHome from "@api/ApiHome";
import {IParamsDefault} from "src/types";
import {useQuery} from "@tanstack/react-query";
import ListAlbums from "@components/ListAlbums";
import ListSongs from "@components/ListSongs";
import ApiRanking from "@api/ApiRanking";
import ListArtists from "@components/ListArtists";
import HeaderTitle from "@components/HeaderTitle";

const params: IParamsDefault = {
  page: 0,
  pageSize: 15,
  language: "all",
};

const songParams: IParamsDefault = {
  page: 0,
  pageSize: 20,
  language: "all",
};

const artistParams: IParamsDefault = {
  page: 0,
  pageSize: 10,
};

export default function Top100() {
  const {t} = useTranslation();

  const {data: dataGetTop100, isLoading} = useQuery({
    queryKey: [QUERY_KEY.ALBUM.GET_TOP_100, params],
    queryFn: () => ApiHome.getTop100(params),
  });

  const {data: songData, isLoading: songLoading} = useQuery({
    queryKey: [QUERY_KEY.RAKING_BOARD, songParams],
    queryFn: () => ApiRanking.getListOfRankingBoard(songParams),
  });

  const {data: artistData, isLoading: artistLoading} = useQuery({
    queryKey: [QUERY_KEY.ARTIST.GET_TOP_ARTIST_FAVOURITE, artistParams],
    queryFn: () => ApiHome.getTopArtistFavourite(artistParams),
  });

  return (
    <div className="flex flex-col gap-4 sm:gap-5 md:gap-6 lg:gap-7 pt-5 px-4 sm:px-6 md:px-8">
      <HeaderTitle title={t("common.album_top_100")} />
      <ListAlbums
        data={dataGetTop100?.data}
        isLoading={isLoading}
        title={t("common.album_top_100")}
      />
      <ListSongs data={songData?.data} isLoading={songLoading} />
      <ListArtists data={artistData?.data} isLoading={artistLoading} />
    </div>
  );
}
