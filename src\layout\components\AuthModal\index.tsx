import {<PERSON><PERSON><PERSON><PERSON><PERSON>, Modal} from "@mui/material";
import Welcome from "./Welcome";
import {useDispatch, useSelector} from "react-redux";
import {IRootState} from "@redux/store";
import {EGlobalModal, hideModal} from "@redux/slices/GlobalModalSlice";
import {useState} from "react";
import {EAuthRoute, IAuthRouteInfo} from "./types";
import CloseIcon from "@mui/icons-material/Close";

export default function AuthModal() {
  const {currentModal} = useSelector((state: IRootState) => state.modal);
  const {accessToken} = useSelector((state: IRootState) => state.user);

  const [routeInfo, setRouteInfo] = useState<IAuthRouteInfo>({
    route: EAuthRoute.WELCOME,
  });

  const dispatch = useDispatch();
  const handleCloseModal = () => {
    dispatch(hideModal());
    setRouteInfo({route: EAuthRoute.WELCOME, authInfo: undefined});
  };

  return (
    <Modal
      open={currentModal === EGlobalModal.AUTH_MODAL && !accessToken}
      onClose={() => {
        if ([EAuthRoute.WELCOME, EAuthRoute.LOGIN].includes(routeInfo.route)) {
          handleCloseModal();
        }
      }}
      keepMounted
      sx={{
        zIndex: 2000,
      }}
    >
      <div className="autofill-dark absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 focus-visible:outline-none shadow-[0_4px_50px_0_#FFFFFF36] rounded-xl">
        <div className="relative">
          <div className="absolute z-20 right-4 top-4 bg-[#FFFFFF47] rounded-full">
            <IconButton
              className="absolute"
              onClick={handleCloseModal}
              size="small"
            >
              <CloseIcon className="text-white" />
            </IconButton>
          </div>
          <Welcome />
        </div>
      </div>
    </Modal>
  );
}
