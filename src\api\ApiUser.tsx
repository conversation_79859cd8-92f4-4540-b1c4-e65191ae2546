import {IUserInfo} from "src/types";
import {fetcher} from "./Fetcher";

const path = {
  getMe: "auth/me",
  updateMe: "user/me",
  uploadAvatar: "user/upload-avatar",
  changePassword: "user/change-password",
};

function getMe(): Promise<IUserInfo> {
  return fetcher({url: path.getMe, method: "get"}, {displayError: false});
}

function updateUserInfo(data: Partial<IUserInfo>): Promise<IUserInfo> {
  return fetcher(
    {url: path.updateMe, method: "patch", data},
    {displayError: false},
  );
}

function uploadAvatar(image: Blob): Promise<{avatarUrl: string}> {
  const formData = new FormData();
  formData.append("image", image);

  return fetcher(
    {
      url: path.uploadAvatar,
      method: "post",
      data: formData,
      headers: {
        "Content-Type": "multipart/form-data",
      },
    },
    {
      displayError: false,
      logRequest: true,
    },
  );
}

function changePasswordUser(data: {
  oldPassword: string;
  newPassword: string;
  confirmPassword: string;
}): Promise<void> {
  return fetcher(
    {
      url: path.changePassword,
      method: "patch",
      data: {
        oldPassword: data.oldPassword,
        newPassword: data.newPassword,
        confirmPassword: data.confirmPassword,
      },
    },
    {displayError: false},
  );
}

export default {
  getMe,
  updateUserInfo,
  uploadAvatar,
  changePasswordUser,
};
