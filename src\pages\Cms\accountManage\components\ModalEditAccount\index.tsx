import ApiUser from "@api/ApiUser";
import QUERY_KEY from "@api/QueryKey";
import ImageCropper from "@components/ImageCropper";
import CloseIcon from "@mui/icons-material/Close";
import {
  CircularProgress,
  Dialog,
  DialogContent,
  DialogTitle,
  IconButton,
  MenuItem,
  Select,
} from "@mui/material";
import {useMutation, useQueryClient} from "@tanstack/react-query";
import {useFormik} from "formik";
import {useTranslation} from "react-i18next";
import {toast} from "react-toastify";
import {IUserInfo} from "src/types";
import {useGenderOptions, useRoleOptions} from "src/utils/global";
import * as Yup from "yup";

interface ChangePasswordModalProps {
  open: boolean;
  onClose: () => void;
  dataGetMe: any;
  isFetching: boolean;
}

export default function ModalEditAccount({
  open,
  onClose,
  dataGetMe,
  isFetching,
}: ChangePasswordModalProps) {
  const {t} = useTranslation();
  const queryClient = useQueryClient();
  const genderOptions = useGenderOptions();
  const roleOptions = useRoleOptions();

  const validationSchema = Yup.object({
    fullName: Yup.string().required(
      t("validation.field_is_require", {field: t("common.full_name")}),
    ),
    email: Yup.string()
      .email(t("validation.invalid_email"))
      .required(t("validation.field_is_require", {field: t("common.email")})),
  });

  const formik = useFormik({
    initialValues: {
      image: dataGetMe?.avatar ?? "",
      fullName: dataGetMe?.fullName ?? "",
      phone: dataGetMe?.phone ?? "",
      email: dataGetMe?.email ?? "",
      identificationNumber: dataGetMe?.identificationNumber ?? "",
      role: dataGetMe?.role ?? "",
      gender: dataGetMe?.gender ?? "",
    },
    validationSchema,
    onSubmit: async (values) => {
      try {
        await updateProfileMutation.mutateAsync(values);
        onClose();
      } catch (error) {
        toast.error(`Error updating profile: ${error}`);
      }
    },
    enableReinitialize: true,
  });

  const updateUserInfoMutation = useMutation({
    mutationFn: ApiUser.updateUserInfo,
    onSuccess: (updatedData) => {
      queryClient.setQueryData<IUserInfo | undefined>(
        [QUERY_KEY.USER.GET_USER],
        (oldData) => ({
          ...oldData,
          ...updatedData,
        }),
      );
      toast.success(t("common.updated_successfully"));
    },
    onError: () => {
      toast.error(t("common.error_while_update"));
    },
  });

  const uploadAvatarMutation = useMutation({
    mutationFn: async (file: File) => {
      return ApiUser.uploadAvatar(file);
    },
    onSuccess: (data) => {
      queryClient.setQueryData<IUserInfo | undefined>(
        [QUERY_KEY.USER.GET_USER],
        (oldData) =>
          oldData ? {...oldData, avatar: data.avatarUrl} : undefined,
      );
      toast.success(t("common.avatar_updated_successfully"));
    },
    onError: (error: any) => {
      toast.error(
        `${t("common.error_while_upload_image")}: ${error.errorMessage || error.message || "Unknown error"}`,
      );
    },
  });

  const updateProfileMutation = useMutation({
    mutationFn: async (values: typeof formik.values) => {
      await updateUserInfoMutation.mutateAsync(values);
      if (values.image) {
        await uploadAvatarMutation.mutateAsync(values.image);
      }
    },
  });

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      classes={{paper: "rounded-lg"}}
    >
      <div className="flex items-center justify-between border-b border-gray-300 pr-4">
        <DialogTitle className="p-0 text-base !font-bold text-[#000000D9]">
          Thông tin tài khoản
        </DialogTitle>
        <IconButton
          aria-label="close"
          onClick={onClose}
          className="text-gray-500 hover:text-[#000000D9]"
          size="small"
        >
          <CloseIcon />
        </IconButton>
      </div>

      <DialogContent className="!pb-0">
        {isFetching && (
          <div className="flex items-center justify-center w-full h-[293px]">
            <CircularProgress sx={{color: "blue"}} />
          </div>
        )}
        {!isFetching && dataGetMe && (
          <form onSubmit={formik.handleSubmit} className="flex flex-col gap-4">
            <div className="flex flex-col gap-[10px]">
              <div className="flex flex-col gap-[10px]">
                <span className="text-[#000000D9] text-sm font-semibold">
                  {t("common.cover_img")}
                  <span className="text-red-600"> *</span>
                </span>
                <div className="flex items-center gap-4">
                  <div className="relative h-[120px] w-[120px] overflow-hidden rounded-lg">
                    <img
                      src={
                        formik.values.image
                          ? typeof formik.values.image === "string"
                            ? formik.values.image
                            : URL.createObjectURL(formik.values.image)
                          : "/image/default-avatar.png"
                      }
                      alt={t("common.cover_img")}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div className="h-9 border-l border-solid border-[#C5C5C5]" />
                  <ImageCropper
                    onChange={(file) =>
                      file && formik.setFieldValue("image", file)
                    }
                    className="h-[120px] w-[120px]"
                  />
                </div>
              </div>
            </div>
            <div className="flex flex-col gap-[10px]">
              <span className="text-[#000000D9] text-sm font-semibold">
                {t("common.full_name")}
                <span className="text-red-600"> *</span>
              </span>
              <input
                type="text"
                id="fullName"
                name="fullName"
                placeholder={t("common.full_name")}
                className="border border-solid border-[#D9D9D9] rounded-[4px] py-2 px-3"
                value={formik.values.fullName}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
              />
              {formik.touched.fullName && formik.errors.fullName ? (
                <div className="text-red-500 text-sm">
                  {formik.errors.fullName as string}
                </div>
              ) : null}
            </div>
            <div className="flex flex-col gap-[10px]">
              <span className="text-[#000000D9] text-sm font-semibold">
                {t("common.email")}
                <span className="text-red-600"> *</span>
              </span>
              <input
                type="text"
                id="Email"
                name="Email"
                placeholder={t("common.email")}
                className="border border-solid border-[#D9D9D9] rounded-[4px] py-2 px-3"
                value={formik.values.email}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
              />
              {formik.touched.email && formik.errors.email ? (
                <div className="text-red-500 text-sm">
                  {formik.errors.email as string}
                </div>
              ) : null}
            </div>
            <div className="flex flex-col gap-[10px]">
              <span className="text-[#000000D9] text-sm font-semibold">
                {t("common.gender")}
              </span>
              <Select
                name="gender"
                size="small"
                value={formik.values.gender}
                onChange={formik.handleChange}
                className="rounded-[4px]"
              >
                <MenuItem value="" disabled>
                  {t("common.gender")}
                </MenuItem>
                {genderOptions.map((option) => (
                  <MenuItem key={option.value} value={option.value}>
                    {option.label}
                  </MenuItem>
                ))}
              </Select>
            </div>
            <div className="flex flex-col gap-[10px]">
              <span className="text-[#000000D9] text-sm font-semibold">
                {t("common.phone_num")}
              </span>
              <input
                type="text"
                name="phone"
                value={formik.values.phone}
                placeholder={t("common.phone_num")}
                className="border border-solid border-[#D9D9D9] rounded-[4px] py-2 px-3"
                onChange={formik.handleChange}
              />
            </div>
            <div className="flex flex-col gap-[10px]">
              <span className="text-[#000000D9] text-sm font-semibold">
                {t("common.CCCD_num")}
              </span>
              <input
                type="text"
                name="identificationNumber"
                value={formik.values.identificationNumber}
                placeholder={t("common.CCCD_num")}
                className="border border-solid border-[#D9D9D9] rounded-[4px] py-2 px-3"
                onChange={formik.handleChange}
              />
            </div>
            <div className="flex flex-col gap-[10px]">
              <span className="text-[#000000D9] text-sm font-semibold">
                {t("common.role")}
              </span>
              <Select
                name="role"
                size="small"
                value={formik.values.role}
                onChange={formik.handleChange}
                className="rounded-[4px]"
              >
                {roleOptions.map((option) => (
                  <MenuItem key={option?.value} value={option?.value}>
                    {option?.label}
                  </MenuItem>
                ))}
              </Select>
            </div>
            <div className="flex justify-end space-x-2 border-t border-gray-300 py-2">
              <div
                onClick={onClose}
                className="rounded-lg cursor-pointer select-none px-4 py-2 text-base text-gray-600 hover:bg-gray-300"
              >
                {t("common.cancel")}
              </div>
              <IconButton
                type="submit"
                loading={updateProfileMutation.isPending}
                disabled={updateProfileMutation.isPending}
                className={`!rounded-lg !px-4 !text-base !text-white w-[102px]
                ${updateProfileMutation.isPending ? "!bg-gray-400 cursor-not-allowed" : "!bg-orange-500 hover:bg-red-600"}
              `}
              >
                {t("common.confirm")}
              </IconButton>
            </div>
          </form>
        )}
      </DialogContent>
    </Dialog>
  );
}
