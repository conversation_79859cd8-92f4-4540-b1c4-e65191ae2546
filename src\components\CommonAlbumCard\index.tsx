import ApiPlaylistDetail from "@api/ApiPlaylistDetail";
import QUERY_KEY from "@api/QueryKey";
import PlayArrowRounded from "@mui/icons-material/PlayArrowRounded";
import {CircularProgress} from "@mui/material";
import {playSongFromList} from "@redux/slices/PlayerSlice";
import {IRootState} from "@redux/store";
import {useMutation, useQuery} from "@tanstack/react-query";
import {useState} from "react";
import {useDispatch, useSelector} from "react-redux";
import {useNavigate} from "react-router-dom";
import PlayerUtil from "src/core/PlayerUtil";
import {IParamsDefault, IPlaylist, PlaylistType} from "src/types";
import {logEvent} from "src/utils/firebase";

interface IAlbumCardProps {
  className?: string;
  data: IPlaylist;
  haveLayer?: boolean;
}

export default function CommonAlbumCard({
  className,
  data,
  haveLayer = true,
}: IAlbumCardProps): JSX.Element {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(false);
  const [hover, setHover] = useState(false);
  const listenPlaylistMutate = useMutation({
    mutationFn: ApiPlaylistDetail.listenPlaylist,
  });
  const params: IParamsDefault = {
    page: 0,
    pageSize: 10,
  };
  const {refetch} = useQuery({
    queryKey: [QUERY_KEY.PLAYLIST.GET_PLAYLIST_SONGS, data?.urlSlug, params],
    queryFn: () =>
      ApiPlaylistDetail.getPlaylistSongs(data?.urlSlug || "", params),
    enabled: false,
  });
  const currentPlaylistId = useSelector(
    (state: IRootState) => state.player.currentPlaylistId,
  );
  const isPaused = useSelector((state: IRootState) => state.player.paused);
  const isCurrentPlaylist = data.id === currentPlaylistId;
  const isPlaying = isCurrentPlaylist && !isPaused;
  const isPausedInPlaylist = isCurrentPlaylist && isPaused;

  const goToDetailPlaylist = (item: IPlaylist) => {
    if (item?.id !== currentPlaylistId)
      logEvent("interact_with_recommendation", {
        current_playlist_id: currentPlaylistId,
        action: "view",
        next_playlist_id: item?.id,
      });

    navigate(`/playlist/${item.urlSlug}`);
  };

  const handlePlayPauseClick = async (e: React.MouseEvent) => {
    e.stopPropagation();
    if (!data?.urlSlug) return;

    if (isPlaying) {
      PlayerUtil.instance.pause();
      return;
    }

    if (isPausedInPlaylist) {
      PlayerUtil.instance.play();
      return;
    }

    setLoading(true);
    try {
      const response = await refetch();
      const songList = response.data?.data || [];

      if (songList) {
        listenPlaylistMutate.mutateAsync(data?.id ?? "", {
          onSuccess: (res) => {
            if (res) {
              dispatch(
                playSongFromList({
                  songList: songList,
                  playlistId: data?.id,
                }),
              );
            }
          },
        });
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div
      className={`${className} relative w-full aspect-square group rounded-lg p-3 hover:bg-[#FFFFFF0F] cursor-pointer ${haveLayer ? "pt-[calc(10%+12px)]" : ""} ${isPlaying ? "bg-[#FFFFFF0F]" : ""}`}
      onClick={() => goToDetailPlaylist(data)}
      onMouseEnter={() => setHover(true)}
      onMouseLeave={() => setHover(false)}
    >
      <div className="relative rounded-lg flex">
        {haveLayer && (
          <>
            <div className="bg-[#D9D9D91A] absolute left-1/2 bottom-0 -translate-x-1/2 rounded-lg w-4/5 h-[110%]" />
            <div className="bg-[#D9D9D94D] absolute rounded-lg w-[90%] left-1/2 z-5 h-[105%] translate-x-[-50%] bottom-0" />
          </>
        )}
        <div className="w-full h-full aspect-square overflow-hidden rounded-lg">
          <img
            src={
              data?.images?.DEFAULT ||
              data?.images?.SMALL ||
              "/image/default-music.png"
            }
            alt={data?.name}
            className="w-full h-full object-cover rounded-lg transform transition-transform duration-700 ease-in-out group-hover:scale-110"
          />
        </div>
        {(hover || isPlaying || isPausedInPlaylist) && (
          <div className="z-10 absolute flex top-0 left-0 w-full h-full justify-center items-center space-x-3">
            {loading ? (
              <CircularProgress sx={{color: "white"}} />
            ) : (
              <div onClick={handlePlayPauseClick}>
                {isPlaying ? (
                  <img
                    src="/image/animation_play.gif"
                    style={{filter: "grayscale(100%) brightness(0) invert(1)"}}
                    alt="Playing"
                  />
                ) : (
                  <span className="bg-orange-500 text-white flex rounded-full items-center justify-center h-10 w-10 shadow-[4px_4px_10px_0px_#B112004F,_-4px_-4px_10.9px_0px_#A708004A]">
                    <PlayArrowRounded />
                  </span>
                )}
              </div>
            )}
          </div>
        )}
      </div>
      <div className="relative flex flex-col justify-start mt-2">
        <span className="text-base font-medium text-white line-clamp-1">
          {data?.name}
        </span>
        <span className="text-sm font-normal text-[#FFFFFF80] line-clamp-1">
          {data?.type === PlaylistType.PLAYLIST
            ? data?.user?.username
            : data?.artists
                ?.map((artist) => artist?.stageName ?? artist?.name)
                .join(", ")}
        </span>
      </div>
    </div>
  );
}
