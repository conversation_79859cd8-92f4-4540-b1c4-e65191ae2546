import {SVGProps} from "react";
const ICUserPlusWatch = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={24}
    height={24}
    fill="none"
    {...props}
  >
    <path
      fill="#EBAC00"
      d="M6.008 6.5c0-2.206 1.794-4 4-4s4 1.794 4 4-1.794 4-4 4-4-1.794-4-4ZM11.91 20c-.55-.93-.55-2.07 0-3.01.565-.938 1.644-2.333 3.329-3.035.227-.095.27-.393.067-.533-.827-.569-1.911-.922-3.305-.922H8c-4.06 0-5.5 2.97-5.5 5.52 0 2.28 1.21 3.48 3.5 3.48h6.39c.17 0 .3-.13.3-.3a.377.377 0 0 0-.1-.22c-.281-.35-.51-.69-.68-.98Zm9.89-.76c-.62 1.03-2 2.76-4.3 2.76s-3.68-1.73-4.3-2.76c-.27-.46-.27-1.02 0-1.48.62-1.03 2-2.76 4.3-2.76s3.68 1.73 4.3 2.76c.27.46.27 1.02 0 1.48Zm-3.05-.74c0-.69-.56-1.25-1.25-1.25h-.01c-.69 0-1.24.56-1.24 1.25a1.25 1.25 0 0 0 2.5 0Z"
    />
  </svg>
);
export default ICUserPlusWatch;
