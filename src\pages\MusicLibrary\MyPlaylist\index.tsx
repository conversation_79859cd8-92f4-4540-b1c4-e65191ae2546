import ApiLibrary from "@api/ApiLibrary";
import {IDataWithMeta} from "@api/Fetcher";
import QUERY_KEY from "@api/QueryKey";
import {useInfiniteQuery} from "@tanstack/react-query";
import {useEffect, useRef} from "react";
import {useTranslation} from "react-i18next";
import {IPlaylist} from "src/types";
import CreatePlaylistCard from "../components/CreatePlaylistCard";
import AlbumCardSkeleton from "@components/AlbumCardSkeleton";
import {Grid} from "@mui/material";
import CommonAlbumCard from "@components/CommonAlbumCard";
import Subtitle from "@components/Subtitle";
import HeaderTitle from "@components/HeaderTitle";

export default function MyPlaylist(): JSX.Element {
  const {t} = useTranslation();

  const observer = useRef<IntersectionObserver | null>(null);
  const lastElementRef = useRef<HTMLDivElement | null>(null);
  const {data, fetchNextPage, isLoading, isError, hasNextPage} =
    useInfiniteQuery<IDataWithMeta<IPlaylist[]>, Error>({
      queryKey: [QUERY_KEY.LIBRARY.GET_MY_PLAYLISTS_VIEW],
      queryFn: ({pageParam = 0}) =>
        ApiLibrary.getMyPlaylists({
          pageSize: 14,
          page: pageParam as number,
        }),
      getNextPageParam: (lastPage) =>
        lastPage?.meta?.totalPage - 1 > lastPage?.meta?.currentPage
          ? (lastPage?.meta?.currentPage || 0) + 1
          : undefined,
      initialPageParam: 0,
    });

  useEffect(() => {
    if (isLoading || isError) return;

    if (observer.current) observer.current.disconnect();

    observer.current = new IntersectionObserver((entries) => {
      if (entries[0].isIntersecting && hasNextPage) {
        fetchNextPage();
      }
    });

    if (lastElementRef?.current) {
      observer.current.observe(lastElementRef.current);
    }

    return () => {
      if (observer.current) {
        observer.current.disconnect();
      }
    };
  }, [isLoading, isError, data, hasNextPage]);

  return (
    <div className="py-5 px-8 max-sm:px-4">
      <HeaderTitle title={t("common.library.my_playlists")} />
      <div className="flex flex-col gap-2 sm:gap-3 md:gap-4 lg:gap-5 w-full">
        <Subtitle subtitle={t("common.library.my_playlist")} seeMore={false} />

        <div>
          <Grid
            container
            spacing={{xs: 2, md: 3}}
            columns={{xs: 2, sm: 6, md: 12, lg: 15}}
          >
            <Grid item xs={1} sm={2} md={3} lg={3}>
              <CreatePlaylistCard />
            </Grid>

            {isLoading && !data && (
              <>
                {[...Array(4)].map((_, index) => (
                  <Grid item xs={1} sm={2} md={3} lg={3} key={index}>
                    <AlbumCardSkeleton isMultipleInfo={false} />
                  </Grid>
                ))}
              </>
            )}
            {data?.pages?.map((page) =>
              page?.data?.map((playlist, index) => (
                <Grid item xs={1} sm={2} md={3} lg={3} key={index}>
                  <CommonAlbumCard data={playlist} haveLayer={false} />
                </Grid>
              )),
            )}
            {hasNextPage && (
              <Grid
                container
                spacing={{xs: 2, md: 3}}
                columns={{xs: 2, sm: 6, md: 12, lg: 15}}
              >
                {[...Array(5)].map((_, index) => (
                  <Grid item xs={1} sm={2} md={3} lg={3} key={index}>
                    <AlbumCardSkeleton isMultipleInfo={false} />
                  </Grid>
                ))}
              </Grid>
            )}
          </Grid>
        </div>
        {!isLoading && <div ref={lastElementRef} className="h-1"></div>}
      </div>
    </div>
  );
}
