// Declare the global YT object provided by the YouTube IFrame Player API script
declare namespace YT {
  // https://developers.google.com/youtube/iframe_api_reference#Constants
  // Define the player states constants
  export enum PlayerState {
    ENDED = 0,
    PLAYING = 1,
    PAUSED = 2,
    BUFFERING = 3,
    VIDEO_CUED = 5,
    UNSTARTED = -1,
  }

  // https://developers.google.com/youtube/iframe_api_reference#Errors
  // Define the error codes (partial list, refer to docs for full list)
  export enum PlayerError {
    INVALID_PARAM = 2,
    HTML5_ERROR = 5,
    NOT_FOUND = 100,
    NOT_EMBEDDABLE = 101, // or 150
    NOT_EMBEDDABLE_NEW = 150,
    TOO_SHORT = 105,
  }

  // https://developers.google.com/youtube/iframe_api_reference#Player_parameters
  // Define the PlayerVars interface for player parameters
  interface PlayerVars {
    autoplay?: 0 | 1;
    cc_lang_pref?: string;
    cc_load_policy?: 1; // Set to 1 to show captions by default
    color?: "red" | "white";
    controls?: 0 | 1; // Set to 0 to hide controls
    disablekb?: 0 | 1; // Set to 1 to disable keyboard controls
    enablejsapi?: 0 | 1; // Set to 1 to enable JS API control (required)
    end?: number; // End time in seconds
    fs?: 0 | 1; // Set to 0 to hide fullscreen button
    hl?: string; // Interface language
    iv_load_policy?: 1 | 3; // Set to 3 to hide video annotations by default
    list?: string; // Playlist ID, search query, or user uploads feed
    listType?: "playlist" | "user_uploads" | "search"; // 'search' is deprecated
    loop?: 0 | 1; // Set to 1 to loop a single video or playlist (with 'playlist' param)
    modestbranding?: 1; // Set to 1 to reduce YouTube branding
    origin?: string; // Required for IFrame API
    playlist?: string; // Comma-separated list of video IDs for looping single videos
    playsinline?: 0 | 1; // Set to 1 for inline playback on iOS
    rel?: 0 | 1; // Set to 0 to show related videos from the same channel
    showinfo?: 0 | 1; // Deprecated: Set to 0 to hide video title and uploader info
    start?: number; // Start time in seconds
    widget_referrer?: string;
  }

  // https://developers.google.com/youtube/iframe_api_reference#Events
  // Define interfaces for event objects
  interface PlayerEvent {
    target: Player;
    data?: any; // State change data, error code, etc. The type depends on the event.
  }

  // https://developers.google.com/youtube/iframe_api_reference#onStateChange
  interface OnStateChangeEvent extends PlayerEvent {
    data: PlayerState; // The state change is a numeric value from YT.PlayerState
  }

  // https://developers.google.com/youtube/iframe_api_reference#onPlaybackQualityChange
  interface OnPlaybackQualityChangeEvent extends PlayerEvent {
    data: string; // Playback quality string (e.g., 'highres', 'hd1080', 'small')
  }

  // https://developers.google.com/youtube/iframe_api_reference#onPlaybackRateChange
  interface OnPlaybackRateChangeEvent extends PlayerEvent {
    data: number; // Playback rate (e.g., 1, 1.5, 2)
  }

  // https://developers.google.com/youtube/iframe_api_reference#onError
  interface OnErrorEvent extends PlayerEvent {
    data: PlayerError; // Error code from YT.PlayerError
  }

  // https://developers.google.com/youtube/iframe_api_reference#Loading_a_video_player
  // Define the PlayerOptions interface for the constructor options
  interface PlayerOptions {
    height?: string | number;
    width?: string | number;
    videoId?: string; // Initial video ID to load
    playerVars?: PlayerVars; // Player parameters
    events?: Events; // Event handlers
    /**
     * This parameter provides an extra security measure for the IFrame API and is supported only for IFrame embeds.
     * If you are using the IFrame API, which means you are setting the enablejsapi parameter to 1,
     * you should always specify your domain as the origin parameter's value.
     */
    origin?: string;
  }

  // https://developers.google.com/youtube/iframe_api_reference#Events
  // Define the Events interface for event handlers
  interface Events {
    onReady?: (event: PlayerEvent) => void;
    onStateChange?: (event: OnStateChangeEvent) => void;
    onPlaybackQualityChange?: (event: OnPlaybackQualityChangeEvent) => void;
    onPlaybackRateChange?: (event: OnPlaybackRateChangeEvent) => void;
    onError?: (event: OnErrorEvent) => void;
  }

  // https://developers.google.com/youtube/iframe_api_reference#The_Player_object
  // Declare the Player as a class
  export class Player {
    // Constructor
    constructor(elementId: string | HTMLElement, options: PlayerOptions);

    // Queueing functions
    // https://developers.google.com/youtube/iframe_api_reference#Queueing_functions
    cueVideoById(
      videoId: string,
      startSeconds?: number,
      suggestedQuality?: string,
    ): void;
    cueVideoById(options: {
      videoId: string;
      startSeconds?: number;
      endSeconds?: number;
      suggestedQuality?: string;
    }): void;
    cueVideoByUrl(
      mediaContentUrl: string,
      startSeconds?: number,
      suggestedQuality?: string,
    ): void;
    cueVideoByUrl(options: {
      mediaContentUrl: string;
      startSeconds?: number;
      endSeconds?: number;
      suggestedQuality?: string;
    }): void;
    cuePlaylist(
      playlist: string | string[],
      index?: number,
      startSeconds?: number,
      suggestedQuality?: string,
    ): void;
    cuePlaylist(options: {
      list: string | string[];
      listType?: "playlist" | "user_uploads";
      index?: number;
      startSeconds?: number;
      suggestedQuality?: string;
    }): void;

    // Loading functions
    // https://developers.google.com/youtube/iframe_api_reference#Loading_functions
    loadVideoById(
      videoId: string,
      startSeconds?: number,
      suggestedQuality?: string,
    ): void;
    loadVideoById(options: {
      videoId: string;
      startSeconds?: number;
      endSeconds?: number;
      suggestedQuality?: string;
    }): void;
    loadVideoByUrl(
      mediaContentUrl: string,
      startSeconds?: number,
      suggestedQuality?: string,
    ): void;
    loadVideoByUrl(options: {
      mediaContentUrl: string;
      startSeconds?: number;
      endSeconds?: number;
      suggestedQuality?: string;
    }): void;
    loadPlaylist(
      playlist: string | string[],
      index?: number,
      startSeconds?: number,
      suggestedQuality?: string,
    ): void;
    loadPlaylist(options: {
      list: string | string[];
      listType?: "playlist" | "user_uploads";
      index?: number;
      startSeconds?: number;
      suggestedQuality?: string;
    }): void;

    // Playback control
    // https://developers.google.com/youtube/iframe_api_reference#Playback_controls
    playVideo(): void;
    pauseVideo(): void;
    stopVideo(): void;
    seekTo(seconds: number, allowSeekAhead: boolean): void; // allowSeekAhead was deprecated

    // Playback status
    // https://developers.google.com/youtube/iframe_api_reference#Playback_status
    getPlayerState(): PlayerState;
    getCurrentTime(): number;
    getDuration(): number;
    getVideoUrl(): string;
    getVideoEmbedCode(): string;
    getPlaybackQuality(): string;
    setPlaybackQuality(suggestedQuality: string): void;
    getAvailablePlaybackQualities(): string[];
    getPlaybackRate(): number;
    setPlaybackRate(suggestedRate: number): void;
    getAvailablePlaybackRates(): number[];
    getVideoLoadedFraction(): number;

    // Playlist methods
    // https://developers.google.com/youtube/iframe_api_reference#Playlist_methods
    getPlaylist(): string[]; // Returns an array of video IDs
    getPlaylistIndex(): number; // Returns the index of the currently playing video in the playlist
    nextVideo(): void;
    previousVideo(): void;
    playVideoAt(index: number): void;

    // Volume control
    // https://developers.google.com/youtube/iframe_api_reference#Volume_controls
    mute(): void;
    unMute(): void;
    isMuted(): boolean;
    setVolume(volume: number): void; // volume is an integer between 0 and 100
    getVolume(): number; // Returns an integer between 0 and 100

    // Sizing
    // https://developers.google.com/youtube/iframe_api_reference#Setting_the_video_size
    setSize(width: number, height: number): void;

    // DOM
    // https://developers.google.com/youtube/iframe_api_reference#Accessing_and_Modifying_DOM_Nodes
    getIframe(): HTMLIFrameElement;
    destroy(): void;

    // Deprecated methods (might still work but not recommended)
    // https://developers.google.com/youtube/iframe_api_reference#Deprecated_methods
    // getVideoBytesLoaded(): number;
    // getVideoBytesTotal(): number;
    // getBytesLoaded(): number;
    // getBytesTotal(): number;
    // getVideoData(): any; // Use with caution, reported as inconsistent/removed
  }
}

// Declare the onYouTubeIframeAPIReady function which the API calls when it is fully loaded.
// This function should be defined in the global scope of your JavaScript/TypeScript code.
declare function onYouTubeIframeAPIReady(): void;
