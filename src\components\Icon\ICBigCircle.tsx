import {SVGProps} from "react";

export default function ICBigCircle({
  width = 134,
  height = 105,
  ...props
}: SVGProps<SVGSVGElement>): JSX.Element {
  return (
    <svg
      width={width}
      height={height}
      viewBox={`0 0 ${width} ${height}`}
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <circle
        cx="95.5377"
        cy="9.80429"
        r="95.025"
        fill="white"
        fillOpacity="0.13"
      />
    </svg>
  );
}
