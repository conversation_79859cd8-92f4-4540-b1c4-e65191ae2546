import {useTranslation} from "react-i18next";
import {useNavigate} from "react-router-dom";
import IconMusicNote3 from "@components/Icon/IconMusicNote3";
import {IArtist} from "src/types";
import {shortenNumber} from "src/utils/numberUtils";
import FavoriteArtistButtonAuth from "@components/AuthButton/FavoriteArtistButton";
import {useEffect, useState} from "react";
import ApiArtistDetail from "@api/ApiArtistDetail";
import {useMutation} from "@tanstack/react-query";
import {toast} from "react-toastify";
import {logEvent} from "src/utils/firebase";
import {useSelector} from "react-redux";
import {IRootState} from "@redux/store";

interface IArtistProps {
  className?: string;
  data: IArtist;
}

export default function CommonArtistCard({
  className,
  data,
}: IArtistProps): JSX.Element {
  const {t} = useTranslation();
  const navigate = useNavigate();
  const {currentSong} = useSelector((state: IRootState) => state?.player);
  const [isLike, setIsLike] = useState<boolean>(data?.isLiked);
  const [numberFavorite, setNumberFavorite] = useState<number>(
    data?.totalLikes,
  );

  useEffect(() => {
    setIsLike(data?.isLiked);
    setNumberFavorite(data?.totalLikes);
  }, [data]);

  const goToDetailArtist = (item: IArtist) => {
    navigate(`/artist/${item.urlSlug}`);
  };

  const likeArtistMutate = useMutation({
    mutationFn: ApiArtistDetail.likeArtist,
    onSuccess: (response) => {
      logEvent("favorite_artist", {
        artist_id: data?.id,
        artist_name: data?.name,
        current_song_id: currentSong?.id,
        current_song_name: currentSong?.name,
        current_song_artist: currentSong?.artists
          ?.map((artist) => artist?.stageName ?? artist?.name)
          .join(", "),
      });
      setIsLike(response?.isLiked);
      setNumberFavorite(response?.totalLikes);
    },
    onError: () => {
      setIsLike(false);
      toast.error(t("common.operation_failed"));
    },
  });

  const likeArtist = (artistId: string) => {
    if (isLike) goToDetailArtist(data);
    else {
      likeArtistMutate.mutateAsync(artistId);
    }
  };

  return (
    <div
      className={`relative flex-shrink-0 w-full group rounded-lg p-3 hover:bg-[#FFFFFF0F] cursor-pointer ${className}`}
      onClick={() => goToDetailArtist(data)}
    >
      <div
        className={`relative border p-1 overflow-hidden rounded-full ${
          isLike ? "border-[#FF4319CC]" : "border-transparent"
        }`}
      >
        <div className="relative overflow-hidden rounded-full">
          <img
            src={
              data?.images?.DEFAULT ||
              data?.images?.SMALL ||
              "/image/default-avatar.png"
            }
            alt={data?.name}
            className="w-full aspect-square rounded-full object-cover transform transition-transform duration-700 ease-in-out group-hover:scale-110"
          />
        </div>
      </div>
      <div className="relative flex flex-col w-full justify-center items-center data-center mt-2 gap-2">
        <span className="text-base flex items-center justify-center font-normal text-[#FFFFFFED] whitespace-nowrap w-full overflow-hidden text-ellipsis">
          {data?.stageName || data?.name}
        </span>
        <div className="flex flex-col ">
          <span className="text-xs font-normal text-[#FFFFFFED] opacity-50">
            {shortenNumber(data.totalSongs)} {t("common.songs")}
          </span>
          <span className="text-xs font-normal text-[#FFFFFFED] opacity-50">
            {shortenNumber(numberFavorite)} {t("common.favorite")}
          </span>
        </div>
        <div
          onClick={(e) => {
            e.stopPropagation();
          }}
        >
          {isLike ? (
            <button
              onClick={() => goToDetailArtist(data)}
              className="border border-solid border-[#FFFFFF33] rounded-2xl text-white text-sm gap-[5px] flex items-center justify-center p-2 bg-transparent"
            >
              <IconMusicNote3 />
              <div className="truncate">{t("common.playlists")}</div>
            </button>
          ) : (
            <FavoriteArtistButtonAuth
              action={() => {
                likeArtist(data?.id);
              }}
              loading={likeArtistMutate.isPending}
            />
          )}
        </div>
      </div>
    </div>
  );
}
