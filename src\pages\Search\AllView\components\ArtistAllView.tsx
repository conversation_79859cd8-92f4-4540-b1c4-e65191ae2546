import Slider from "@components/Slider";
import {SwiperSlide} from "swiper/react";
import CommonArtistCard from "@components/CommonArtistCard";
import {IArtist} from "src/types";

interface IAllViewArtistClickProp {
  data: IArtist[];
}

export function ArtistAllView({data}: IAllViewArtistClickProp) {
  return (
    <Slider slidesPerView={5} spaceBetween={16}>
      {data?.map((item, index) => {
        return (
          <SwiperSlide
            key={`search_artist_all_${item.id}`}
            virtualIndex={index}
          >
            <CommonArtistCard data={item} />
          </SwiperSlide>
        );
      })}
    </Slider>
  );
}
