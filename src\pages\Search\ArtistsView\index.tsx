import {useTranslation} from "react-i18next";
import IconNoData from "@components/Icon/IconNoData";
import {useInfiniteQuery} from "@tanstack/react-query";
import QUERY_KEY from "@api/QueryKey";
import ApiSearch from "@api/ApiSearch";
import {useEffect, useRef} from "react";
import {IDataWithMeta} from "@api/Fetcher";
import {IArtist} from "src/types";
import ArtistCardSkeleton from "@components/ArtistCardSkeleton";
import CommonArtistCard from "@components/CommonArtistCard";
import {Grid} from "@mui/material";
import {useLocation} from "react-router-dom";
import Subtitle from "@components/Subtitle";

export function ArtistsView() {
  const {t} = useTranslation();
  const location = useLocation();
  const query = new URLSearchParams(location.search);
  const searchValue = query.get("q");
  const observer = useRef<IntersectionObserver | null>(null);
  const lastElementRef = useRef<HTMLDivElement | null>(null);
  const {data, fetchNextPage, isLoading, isError, hasNextPage} =
    useInfiniteQuery<IDataWithMeta<IArtist[]>, Error>({
      queryKey: [QUERY_KEY.SEARCH.GET_SEARCH_ARTISTS, searchValue],
      queryFn: ({pageParam = 0}) =>
        ApiSearch.searchArtists({
          keyword: searchValue || "",
          pageSize: 15,
          page: pageParam as number,
        }),
      getNextPageParam: (lastPage) =>
        lastPage?.meta?.totalPage - 1 > lastPage?.meta?.currentPage
          ? (lastPage?.meta?.currentPage || 0) + 1
          : undefined,
      initialPageParam: 0,
    });

  useEffect(() => {
    if (isLoading || isError) return;

    if (observer.current) observer.current.disconnect();

    observer.current = new IntersectionObserver((entries) => {
      if (entries[0].isIntersecting && hasNextPage) {
        fetchNextPage();
      }
    });

    if (lastElementRef?.current) {
      observer.current.observe(lastElementRef.current);
    }

    return () => {
      if (observer.current) {
        observer.current.disconnect();
      }
    };
  }, [isLoading, isError, data, hasNextPage]);

  const isEmpty =
    !data || !data.pages || data.pages.every((page) => page.data.length === 0);

  return (
    <div className="w-full flex flex-col gap-1 sm:gap-2 md:gap-3 lg:gap-4">
      <Subtitle subtitle={t("common.artist")} seeMore={false} />
      {isLoading && !data && (
        <Grid
          container
          spacing={{xs: 2, md: 3}}
          columns={{xs: 2, sm: 6, md: 12, lg: 15}}
        >
          {[...Array(5)].map((_, index) => (
            <Grid item xs={1} sm={2} md={3} lg={3} key={index}>
              <ArtistCardSkeleton />
            </Grid>
          ))}
        </Grid>
      )}
      {!isLoading && (isError || isEmpty) && (
        <div className="flex justify-center items-center flex-col lg:gap-2.5 gap-1">
          <IconNoData />
          <span className="text-[#E3E3E3] w-full text-base font-normal flex justify-center">
            {t("common.artist_list_not_found")}
          </span>
        </div>
      )}
      <div>
        <Grid
          container
          spacing={{xs: 2, md: 3}}
          columns={{xs: 2, sm: 6, md: 12, lg: 15}}
        >
          {!isLoading &&
            !(isError || isEmpty) &&
            data?.pages?.map((page) =>
              page?.data?.map((artist, index) => (
                <Grid item xs={1} sm={2} md={3} lg={3} key={index}>
                  <CommonArtistCard data={artist} />
                </Grid>
              )),
            )}
        </Grid>
        {hasNextPage && (
          <Grid
            container
            spacing={{xs: 2, md: 3}}
            columns={{xs: 2, sm: 6, md: 12, lg: 15}}
          >
            {[...Array(5)].map((_, index) => (
              <Grid item xs={1} sm={2} md={3} lg={3} key={index}>
                <ArtistCardSkeleton />
              </Grid>
            ))}
          </Grid>
        )}
      </div>
      {!isLoading && <div ref={lastElementRef} className="h-1"></div>}
    </div>
  );
}
