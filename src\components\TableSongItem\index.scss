
@use "../../styles/global.scss" as globals;

.song-item {
  .song-action {
    display: none;

    &:has(.liked) {
      display: block;
    }

    &:hover {
      background-color: rgba(255, 255, 255, 0.2);
    }
  }

  @media screen and (max-width: globals.$sm) {
    .song-action {
      display: flex;
    }
    .song-duration {
      display: none;
    }
  }

  @media screen and (min-width: globals.$sm) {
    &:hover {
      .song-duration {
        display: none;
      }
      .song-action {
        display: flex;
      }
    }
  }
}

