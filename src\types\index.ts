export enum AudioQualityTypeEnum {
  KBPS_128 = 0,
  KBPS_320 = 1,
}

export enum CountryEnum {
  VIETNAM = "VIETNAM",
  UNITED_STATES = "UNITED_STATES",
  CANADA = "CANADA",
  UNITED_KINGDOM = "UNITED_KINGDOM",
  FRANCE = "FRANCE",
  GERMANY = "GERMANY",
  JAPAN = "JAP<PERSON>",
  SOUTH_KOREA = "SOUTH_KOREA",
  CHINA = "CHINA",
  INDIA = "INDIA",
  AUSTRALIA = "AUSTRALIA",
  BRAZIL = "BRAZIL",
  RUSSIA = "RUSSIA",
  ITALY = "ITALY",
  SPAIN = "SPAIN",
  MEXICO = "MEXICO",
  INDONESIA = "INDONESIA",
  THAILAND = "THAILAND",
  MALAYSIA = "MALAYSIA",
  PHILIPPINES = "PHILIPPINES",
  SOUTH_AFRICA = "SOUTH_AFRICA",
}

export enum Role {
  ADMIN = 0,
  ARTIST = 1,
  END_USER = 2,
}

export enum AccountType {
  FREE = 0,
  PREMIUM = 1,
  PRO = 2,
}

export enum GenderEnum {
  MALE = 0,
  FEMALE = 1,
  UNKNOWN = 2,
}

export enum EGender {
  MALE = "MALE",
  FEMALE = "FEMALE",
  UNKNOWN = "UNKNOWN",
}

export enum EThemeAndGenreType {
  THEME = 1,
  GENRE = 0,
}

export enum InteractionTypeEnum {
  LISTEN = 0,
  LIKE = 1,
  SHARE = 2,
  DOWNLOAD = 3,
  ADD = 4,
}

export enum PlaylistType {
  PLAYLIST = 0,
  ALBUM = 1,
  TOP_100 = 2,
}

export enum PlayMode {
  NORMAL,
  REPEAT,
  REPEAT_ONE,
}

export enum ERanking {
  WEEKLY = 0,
  MONTHLY = 1,
  YEARLY = 2,
}

export enum EArtistType {
  SINGER = 0,
  MUSICIAN = 1,
  SINGER_MUSICIAN = 2,
}

export enum EActivityStatus {
  ACTIVE = 0,
  INACTIVE = 1,
}

export enum ESharePlatform {
  FACEBOOK = "FACEBOOK",
  ZALO = "ZALO",
  NONE = "NONE",
}

export interface IUserLogin {
  id: string;
  email: string;
  language: string;
  username: string;
  phone: string;
  avatar: string;
  gender: string;
  birthday: string;
  role: string;
}

export interface IParamsDefault {
  fromDate?: string;
  toDate?: string;
  updatedAt?: string;
  sortField?: string;
  sortDirection?: string;
  hashTag?: string;
  page?: number;
  pageSize?: number;
  order?: string;
  direction?: string;
  keyword?: string;
  type?: number;
  language?: string;
}

export interface IParamsQueue extends IParamsDefault {
  songIds?: (string | undefined)[];
}

export interface IAccountInfo {
  refreshToken?: string;
  accessToken?: string;
  userInfo?: IUserInfo;
}

export interface IUserInfo {
  id?: string;
  createdAt?: string | null;
  email?: {email: string | null; primary: boolean | null}[];
  fullName?: string | null;
  language?: string;
  username?: string | null;
  phone?: string | null;
  identificationNumber?: string | null;
  avatar?: string | null;
  status?: string | null;
  gender?: GenderEnum | string;
  birthday?: string | null;
  lastLoggedIn?: string | null;
  device?: string | null;
  report?: string | null;
  violent?: string | null;
  role?: Role;
  accountType?: AccountType;
  artistId?: string | null;
  type?: EArtistType | null;
  updatedAt?: string | null;
  country?: CountryEnum | null;
  dateOfBirth?: string;
  countryName?: CountryEnum;
  lastName?: string;
  firstName?: string;
  phoneNumber?: {phoneNumber: string | null; primary: boolean | null}[];
  province?: string | null;
}

export interface IAudio {
  id?: string;
  type?: AudioQualityTypeEnum;
  url?: string;
}

export type ImageVariants = {
  SMALL?: string;
  DEFAULT?: string;
};

export interface ISong {
  artists?: IArtist[];
  audios?: IAudio[];
  createdAt?: string;
  duration?: number;
  genres?: IThemeAndGenre[];
  themes?: IThemeAndGenre[];
  id: string;
  images?: ImageVariants;
  isLiked?: boolean;
  license?: string;
  lrcLyrics?: string;
  name?: string;
  playlists?: IPlaylist[];
  album?: IPlaylist[];
  releaseDate?: string;
  textLyrics?: string;
  totalAddedToPlaylists?: number;
  totalDownloads?: number;
  position?: number;
  prevPosition?: number;
  totalInteractions?: number;
  keyColor?: string;
  totalLikes?: number;
  totalListens?: number;
  totalShares?: number;
  updatedAt?: string;
  urlSlug?: string;
  ageLimit?: number;
  type: ESongType;
  language: string;
}

export interface IPlaylist {
  artists: IArtist[];
  createdAt?: string;
  id: string;
  urlSlug?: string;
  isLiked?: boolean;
  name?: string;
  isPublic?: boolean;
  releaseDate?: string;
  totalDownloads?: number;
  totalListens?: number;
  totalSongs?: number;
  totalShares?: number;
  totalInteractions?: number;
  type?: PlaylistType;
  updatedAt?: string;
  description?: string;
  genres?: IThemeAndGenre[];
  totalLikes?: number;
  totalDurations?: number;
  themes?: IThemeAndGenre[];
  user?: IUserInfo;
  images?: ImageVariants;
  songs?: ISong[];
  language: string;
}

export interface IArtist {
  id: string;
  images?: ImageVariants;
  name: string;
  avatar?: string;
  biography?: string;
  debutDate?: string;
  urlSlug?: string;
  company?: string;
  deletedAt?: string;
  type: EArtistType;
  isLiked: boolean;
  totalLikes: number;
  totalSongs: number;
  totalPlaylists: number;
  totalAlbums: number;
  user?: IUserInfo;
  gender: GenderEnum;
  isVerify: boolean;
  createdAt: string;
  updatedAt?: string;
  stageName?: string;
}

export interface IThemeAndGenre {
  id: string;
  name: string;
  images?: ImageVariants;
  urlSlug?: string;
  type: EThemeAndGenreType;
  createdAt?: string;
  updatedAt?: string;
  description?: string;
  totalListen?: number;
  totalPlaylist?: number;
  totalSong?: number;
  parentId?: string;
  parentGenre?: IThemeAndGenre;
  subGenresDto?: IThemeAndGenre[];
  parentGenreDto?: IThemeAndGenre;
  nameVi?: string;
  nameLo?: string;
  nameEn?: string;
  playlists?: IPlaylist[];
}

export interface IPlaylistParam {
  page: number;
  pageSize: number;
  order?: string;
  direction?: string;
  keyword?: string;
  type?: number;
}

export enum ESongType {
  SONG,
  YOUTUBE,
}

export enum ESearchType {
  KEYWORD,
  SONG,
  ARTIST,
}

export type ISearchItem =
  | {type: ESearchType.KEYWORD; value: string}
  | {type: ESearchType.SONG; value: ISong}
  | {type: ESearchType.ARTIST; value: IArtist};

export enum EStatisticsDateRangeType {
  DAY,
  MONTH,
  YEAR,
}

export enum EMusicPackageType {
  DAY,
  WEEK,
  MONTH,
  YEAR,
}
export interface IMusicPackage {
  id: string;
  type: EMusicPackageType;
  price: number;
  specialPrice?: number;
  exprireTime?: number;
  features: string[];
  priceUnit: string;
}
