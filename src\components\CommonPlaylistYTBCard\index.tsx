import ApiPlaylistDetail from "@api/ApiPlaylistDetail";
import {IRootState} from "@redux/store";
import clsx from "clsx";
import {useState} from "react";
import {useSelector} from "react-redux";
import {useNavigate} from "react-router-dom";
import {IPlaylist} from "src/types";

interface IPlaylistYTBCardProps {
  className?: string;
  data: IPlaylist | any;
  haveLayer?: boolean;
}

export default function CommonPlaylistYTBCard({
  className,
  data,
  haveLayer = true,
}: IPlaylistYTBCardProps): JSX.Element {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);

  const currentPlaylistId = useSelector(
    (state: IRootState) => state.player.currentPlaylistId,
  );
  const isPaused = useSelector((state: IRootState) => state.player.paused);
  const isCurrentPlaylist = data.id === currentPlaylistId;
  const isPlaying = isCurrentPlaylist && !isPaused;

  const goToDetailPlaylist = async (item: IPlaylist) => {
    if (!item.urlSlug) return;
    setLoading(true);
    try {
      await ApiPlaylistDetail.importYoutubePlaylist(item.urlSlug);
      navigate(`/playlist/${item.urlSlug}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div
      className={`${className} relative w-full aspect-video group rounded-lg p-3 hover:bg-[#FFFFFF0F] cursor-pointer flex flex-col gap-4 ${haveLayer ? "pt-[8%]" : ""} ${isPlaying ? "bg-[#FFFFFF0F]" : ""}`}
      onClick={() => goToDetailPlaylist(data)}
    >
      <div className="relative rounded-lg flex">
        {haveLayer && (
          <>
            <div className="bg-[#D9D9D91A] absolute left-1/2 bottom-0 -translate-x-1/2 rounded-lg w-4/5 h-[110%]" />
            <div className="bg-[#D9D9D94D] absolute rounded-lg w-[90%] left-1/2 z-5 h-[105%] translate-x-[-50%] bottom-0" />
          </>
        )}
        <div
          className={clsx(
            "w-full h-full aspect-video overflow-hidden rounded-lg",
            loading && "animate-pulse",
          )}
        >
          <img
            src={
              data?.images?.DEFAULT ||
              data?.images?.SMALL ||
              "/image/default-music.png"
            }
            alt={data?.name}
            className="w-full h-full object-cover rounded-lg transform transition-transform duration-700 ease-in-out group-hover:scale-110"
          />
        </div>
      </div>
      <div className="flex gap-2 items-center justify-center w-full">
        <img
          src={
            data?.artists?.[0]?.images?.SMALL ||
            data?.artists?.[0]?.images?.DEFAULT ||
            "/image/default-avatar.png"
          }
          className="aspect-square rounded-full object-cover w-[13%]"
        />
        <div className="flex flex-col gap-0.5 justify-start w-[87%]">
          <span className="text-base font-medium text-white line-clamp-1">
            {data?.name}
          </span>
          <span className="text-sm font-normal text-[#FFFFFF80] line-clamp-1">
            {data?.artists?.[0]?.name}
          </span>
        </div>
      </div>
    </div>
  );
}
