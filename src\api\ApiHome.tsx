import {fetcherWithMetadata, IDataWithMeta} from "./Fetcher";
import {
  IArtist,
  IParamsDefault,
  ISong,
  IPlaylist,
  PlaylistType,
  IThemeAndGenre,
} from "src/types";

export interface IGetTopGenreParam {
  page: number;
  pageSize: number;
  playlistType: PlaylistType;
}

const path = {
  home: "app/home",
  songsRecommended: "songs/recommended",
  top100: "playlists/top100",
  topGenre: "genres/top-genres-playlists",
  topTrendingYoutube: "songs/music-trending-ytb",
  topArtistFavourite: "artists/top-favourite",
};

function getSongsRecommended(
  params: IParamsDefault,
): Promise<IDataWithMeta<ISong[]>> {
  return fetcherWithMetadata<ISong[]>(
    {
      url: path.songsRecommended,
      method: "get",
      params,
    },
    {
      displayError: false,
      withMetadata: true,
    },
  );
}

function getTop100(
  params: IParamsDefault,
): Promise<IDataWithMeta<IPlaylist[]>> {
  return fetcherWithMetadata<IPlaylist[]>(
    {
      url: path.top100,
      method: "get",
      params,
    },
    {
      displayError: false,
      withMetadata: true,
    },
  );
}

function getTopGenre(
  params: IGetTopGenreParam,
): Promise<IDataWithMeta<IThemeAndGenre[]>> {
  return fetcherWithMetadata<IThemeAndGenre[]>(
    {
      url: path.topGenre,
      method: "get",
      params,
    },
    {
      displayError: false,
      withMetadata: true,
    },
  );
}

function getTopTrendingYoutube(): Promise<IDataWithMeta<ISong[]>> {
  return fetcherWithMetadata<ISong[]>(
    {
      url: path.topTrendingYoutube,
      method: "get",
    },
    {
      displayError: false,
      withMetadata: true,
    },
  );
}

function getTopArtistFavourite(
  params: IParamsDefault,
): Promise<IDataWithMeta<IArtist[]>> {
  return fetcherWithMetadata<IArtist[]>(
    {
      url: path.topArtistFavourite,
      method: "get",
      params,
    },
    {
      displayError: false,
      withMetadata: true,
    },
  );
}

export default {
  getSongsRecommended,
  getTop100,
  getTopGenre,
  getTopArtistFavourite,
  getTopTrendingYoutube,
};
