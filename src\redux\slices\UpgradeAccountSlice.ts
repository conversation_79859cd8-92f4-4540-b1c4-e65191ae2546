import {createSlice} from "@reduxjs/toolkit";

interface IGlobalUpgradeModalState {
  isModalOpened: boolean;
  shouldViewUpgradePage: boolean;
}

const initialState: IGlobalUpgradeModalState = {
  isModalOpened: false,
  shouldViewUpgradePage: false,
};

const UpgradeAccountSlice = createSlice({
  name: "upgradeModal",
  initialState,
  reducers: {
    showUpgradeModal(state) {
      state.isModalOpened = true;
    },
    hiddenUpgradeModal(state) {
      state.isModalOpened = false;
    },
    setShouldViewUpgradePage(state) {
      state.shouldViewUpgradePage = true;
    },
  },
});

// Action creators are generated for each case reducer function
export const {showUpgradeModal, hiddenUpgradeModal, setShouldViewUpgradePage} =
  UpgradeAccountSlice.actions;

export default UpgradeAccountSlice.reducer;
