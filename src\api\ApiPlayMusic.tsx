import {IParamsQueue, ISong} from "src/types";
import {fetcher} from "./Fetcher";

export interface IDownLoadQuality {
  audio320Kps: string;
  audio1280Kps: string;
}

const path = {
  getSuggestSongs: "play-music",
  getListSong: "play-music/song-queue",
};

function getListSong(body: IParamsQueue): Promise<ISong[]> {
  return fetcher(
    {
      url: path.getListSong,
      method: "post",
      data: body,
    },
    {
      displayError: false,
    },
  );
}

function getSuggestSongs(urlSlug: string, params: IParamsQueue) {
  return fetcher<ISong[]>({
    url: `${path.getSuggestSongs}/${urlSlug}/suggest-songs`,
    method: "get",
    params,
  });
}

export default {
  getListSong,
  getSuggestSongs,
};
