import ApiLibrary from "@api/ApiLibrary";
import {IDataWithMeta} from "@api/Fetcher";
import QUERY_KEY from "@api/QueryKey";
import IconNoData from "@components/Icon/IconNoData";
import SongItem from "@components/SongItem";
import SongItemSkeleton from "@components/SongItemSkeleton";
import {playSongFromList} from "@redux/slices/PlayerSlice";
import {useInfiniteQuery} from "@tanstack/react-query";
import {useEffect, useMemo, useRef} from "react";
import {useTranslation} from "react-i18next";
import {useDispatch} from "react-redux";
import {ESongType, ISong} from "src/types";

export default function FavoriteSongs(): JSX.Element {
  const {t} = useTranslation();
  const dispatch = useDispatch();
  const {data, isLoading, fetchNextPage, isError, hasNextPage} =
    useInfiniteQuery<IDataWithMeta<ISong[]>, Error>({
      queryKey: [QUERY_KEY.LIBRARY.GET_MY_FAVORITE_SONGS],
      queryFn: ({pageParam = 0}) =>
        ApiLibrary.getMyFavoriteSong({
          pageSize: 10,
          page: pageParam as number,
          type: ESongType.SONG,
        }),
      getNextPageParam: (lastPage) =>
        lastPage?.meta?.totalPage - 1 > lastPage?.meta?.currentPage
          ? (lastPage?.meta?.currentPage || 0) + 1
          : undefined,
      initialPageParam: 0,
    });

  const songList = useMemo(() => {
    return data?.pages.flatMap((page) => page.data) || [];
  }, [data]);

  const observer = useRef<IntersectionObserver | null>(null);
  const lastElementRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    if (isLoading || isError) return;

    if (observer?.current) observer?.current?.disconnect();

    observer.current = new IntersectionObserver((entries) => {
      if (entries[0]?.isIntersecting && hasNextPage) {
        fetchNextPage();
      }
    });

    if (lastElementRef?.current)
      observer?.current?.observe(lastElementRef?.current);

    return () => {
      if (observer?.current) {
        observer.current?.disconnect();
      }
    };
  }, [isLoading, isError, data, hasNextPage]);

  if (isError || data?.pages[0].meta.totalItems === 0) {
    return (
      <div className="text-white my-[5vh] w-full h-fit flex-col gap-4 flex justify-center items-center text-lg">
        <IconNoData />
        {t("common.list_empty")}
      </div>
    );
  }

  return (
    <div className="flex flex-col">
      {isLoading ? (
        <div className="flex flex-col">
          {[...Array(5)].map((_, index) => (
            <div key={index}>
              <SongItemSkeleton />
            </div>
          ))}
        </div>
      ) : songList?.length ? (
        songList?.map((song, index) => (
          <SongItem
            song={song}
            key={`library_favourite_${index}`}
            left={
              <div className="flex gap-x-6 items-center mr-6">
                <div className="text-base text-center w-10">{index + 1}</div>
              </div>
            }
            handlePlayMusic={() => {
              dispatch(playSongFromList({song, songList}));
            }}
          />
        ))
      ) : (
        <div className="text-white w-full h-fit my-[5vh] flex-col gap-4 flex justify-center items-center text-lg">
          <IconNoData />
          {t("common.list_empty")}
        </div>
      )}
      {hasNextPage && (
        <div className="flex flex-col">
          {[...Array(1)].map((_, index) => (
            <div key={index}>
              <SongItemSkeleton />
            </div>
          ))}
        </div>
      )}
      {!isLoading && <div ref={lastElementRef} className="h-1"></div>}
    </div>
  );
}
