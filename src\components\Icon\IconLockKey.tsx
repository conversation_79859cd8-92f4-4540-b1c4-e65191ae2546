import {SVGProps} from "react";

function IconLockKey({
  width = "24",
  height = "25",
  ...props
}: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox={`0 0 ${width} ${height}`}
      className={props.className}
      fill="none"
      {...props}
    >
      <g clipPath="url(#clip0_7346_469)">
        <path
          d="M12.0002 12.0647C9.65317 12.0655 8.18713 14.6067 9.36133 16.6389C9.75076 17.3129 10.3832 17.8124 11.1291 18.035V19.9047C11.1291 20.5752 11.855 20.9944 12.4358 20.6591C12.7053 20.5035 12.8713 20.2159 12.8713 19.9047V18.035C15.1203 17.3637 15.7991 14.5095 14.0932 12.8975C13.5274 12.3629 12.7786 12.0649 12.0002 12.0647ZM12.0002 16.4202C10.9943 16.4202 10.3657 15.3313 10.8686 14.4602C11.3716 13.5891 12.6289 13.5891 13.1318 14.4603C13.2465 14.6589 13.3069 14.8842 13.3069 15.1136C13.3069 15.8352 12.7219 16.4203 12.0002 16.4202ZM20.7113 8.58022H17.2269V5.96689C17.2269 1.9434 12.8713 -0.571285 9.38687 1.44046C7.76974 2.37412 6.77354 4.09958 6.77354 5.96689V8.58022H3.2891C2.32687 8.58018 1.54688 9.36021 1.54688 10.3224V22.518C1.54688 23.4802 2.32687 24.2603 3.2891 24.2602H20.7113C21.6735 24.2602 22.4535 23.4802 22.4535 22.518V10.3224C22.4535 9.36027 21.6735 8.58026 20.7113 8.58022ZM8.51576 5.96689C8.51715 3.28456 11.4217 1.60961 13.744 2.95198C14.8206 3.57433 15.484 4.7233 15.4846 5.96689V8.58022H8.51576V5.96689ZM20.7113 22.518H3.2891V10.3224H20.7113V22.518Z"
          fill="#303030"
        />
      </g>
      <defs>
        <clipPath id="clip0_7346_469">
          <rect
            width="24"
            height="24"
            fill="white"
            transform="translate(0 0.5)"
          />
        </clipPath>
      </defs>
    </svg>
  );
}

export default IconLockKey;
