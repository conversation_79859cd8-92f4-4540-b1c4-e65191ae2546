import {useTranslation} from "react-i18next";
import "./index.scss";
import Metric, {MetricCardProps} from "./components/Metric";
import ListenOverTime from "./components/ListenOverTime";
import VisitOverTime from "./components/VisitOverTime";
import SongPlayStatsByPlaylist from "./components/SongPlayStatsByPlaylist";
import {useQuery} from "@tanstack/react-query";
import ApiCMSStatistics from "@api/ApiCMSStatistics";
import QUERY_KEY from "@api/QueryKey";

export default function CmsStatistical() {
  const {t} = useTranslation();
  const {data: dataTotal} = useQuery({
    queryKey: [QUERY_KEY.STATISTICS.GET_TOTAL],
    queryFn: ApiCMSStatistics.getTotal,
  });

  const data: MetricCardProps[] = [
    {
      title: t("cms.dashboard.title.total_accounts"),
      value: dataTotal?.totalUsers || 0,
    },
    {
      title: t("cms.dashboard.title.total_accounts_online"),
      value: dataTotal?.totalUsersOnline || 0,
    },
    {
      title: t("cms.dashboard.title.total_songs"),
      value: dataTotal?.totalSongs || 0,
    },
    {
      title: t("cms.dashboard.title.total_playlists"),
      value: dataTotal?.totalPlaylists || 0,
    },
  ];

  return (
    <div>
      <Metric metrics={data} col={4} gap={30} />
      <div className="grid grid-cols-1 gap-5 mt-5">
        <VisitOverTime />
        <ListenOverTime />
        <SongPlayStatsByPlaylist />
        {/* <MusicSpeedDownload />
        <NewSongs /> */}
      </div>
    </div>
  );
}
