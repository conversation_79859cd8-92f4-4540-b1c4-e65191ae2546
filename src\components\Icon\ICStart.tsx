import {SVGProps} from "react";

export default function ICStart({
  width = 21,
  height = 23,
  ...props
}: SVGProps<SVGSVGElement>): JSX.Element {
  return (
    <svg
      width={width}
      height={height}
      viewBox={`0 0 ${width} ${height}`}
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M20.8333 11.25C10.4167 11.25 10.4167 11.25 10.4167 22.5C10.4167 11.25 10.4167 11.25 0 11.25C10.4167 11.25 10.4167 11.25 10.4167 1.43688e-06C10.4167 11.25 10.4167 11.25 20.8333 11.25Z"
        fill="white"
      />
    </svg>
  );
}
