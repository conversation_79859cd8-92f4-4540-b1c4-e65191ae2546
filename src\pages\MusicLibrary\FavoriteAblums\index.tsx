import ApiLibrary from "@api/ApiLibrary";
import {IDataWithMeta} from "@api/Fetcher";
import QUERY_KEY from "@api/QueryKey";
import AlbumCardSkeleton from "@components/AlbumCardSkeleton";
import CommonAlbumCard from "@components/CommonAlbumCard";
import IconNoData from "@components/Icon/IconNoData";
import {Grid} from "@mui/material";
import {useInfiniteQuery} from "@tanstack/react-query";
import {useEffect, useRef} from "react";
import {useTranslation} from "react-i18next";
import {IPlaylist, PlaylistType} from "src/types";

export default function FavoriteAlbums(): JSX.Element {
  const {t} = useTranslation();
  const {data, fetchNextPage, isLoading, isError, hasNextPage} =
    useInfiniteQuery<IDataWithMeta<IPlaylist[]>, Error>({
      queryKey: [QUERY_KEY.LIBRARY.GET_MY_FAVORITE_ALBUMS],
      queryFn: ({pageParam = 0}) =>
        ApiLibrary?.getMyFavoritePlaylist({
          pageSize: 30,
          page: pageParam as number,
          type: PlaylistType.ALBUM,
        }),
      getNextPageParam: (lastPage) =>
        lastPage?.meta?.totalPage - 1 > lastPage?.meta?.currentPage
          ? (lastPage?.meta?.currentPage || 0) + 1
          : undefined,
      initialPageParam: 0,
    });
  const observer = useRef<IntersectionObserver | null>(null);
  const lastElementRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    if (isLoading || isError) return;

    if (observer?.current) observer?.current?.disconnect();

    observer.current = new IntersectionObserver((entries) => {
      if (entries[0]?.isIntersecting && hasNextPage) {
        fetchNextPage();
      }
    });

    if (lastElementRef?.current)
      observer?.current?.observe(lastElementRef?.current);

    return () => {
      if (observer?.current) {
        observer.current?.disconnect();
      }
    };
  }, [isLoading, isError, data, hasNextPage]);

  if (isLoading) {
    return (
      <Grid
        container
        spacing={{xs: 2, md: 3}}
        columns={{xs: 4, sm: 8, md: 12, lg: 20}}
      >
        {[...Array(30)].map((_, index) => (
          <Grid item xs={2} sm={4} md={4} lg={4} key={index}>
            <AlbumCardSkeleton key={`skeleton_album_${index}`} />
          </Grid>
        ))}
      </Grid>
    );
  }

  if (isError || data?.pages[0].meta.totalItems === 0) {
    return (
      <div className="text-white my-[5vh] w-full h-fit flex-col gap-4 flex justify-center items-center text-lg">
        <IconNoData />
        {t("common.list_empty")}
      </div>
    );
  }

  return (
    <>
      <Grid
        container
        spacing={{xs: 2, md: 3}}
        columns={{xs: 4, sm: 8, md: 12, lg: 20}}
      >
        {data?.pages?.map((page) =>
          page?.data?.map((album, index) => (
            <Grid item xs={2} sm={4} md={4} lg={4} key={index}>
              <CommonAlbumCard
                key={`album_${album?.urlSlug}`}
                data={album}
                haveLayer={true}
              />
            </Grid>
          )),
        )}
      </Grid>
      {hasNextPage && (
        <Grid
          container
          spacing={{xs: 2, md: 3}}
          columns={{xs: 4, sm: 8, md: 12, lg: 20}}
        >
          {[...Array(30)].map((_, index) => (
            <Grid item xs={2} sm={4} md={4} lg={4} key={index}>
              <AlbumCardSkeleton key={`skeleton_album_${index}`} />
            </Grid>
          ))}
        </Grid>
      )}
      {!isLoading && <div ref={lastElementRef} className="h-1"></div>}
    </>
  );
}
