import {Modal} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import {useTranslation} from "react-i18next";
import {useState} from "react";

type ModalAppInstallProps = {
  open: boolean;
  onClose: () => void;
};

const ModalAppInstall = ({open, onClose}: ModalAppInstallProps) => {
  const [os, setOS] = useState<"ios" | "android">("ios");

  const {t} = useTranslation();
  return (
    <>
      <Modal
        open={open}
        onClose={onClose}
        aria-labelledby="modal-title"
        aria-describedby="modal-description"
      >
        <div className="hidden lg:block absolute top-2/4 left-2/4 -translate-x-2/4 -translate-y-2/4 w-[752px] h-fit bg-[url('/image/background_login.png')]  bg-cover p-6 rounded-xl text-center min-w-fit border border-[rgba(255,255,255,0.3)] shadow-[0_8px_32px_rgba(255,255,255,0.25)]">
          <div className="w-full flex justify-end">
            <div className="rounded-[50%] bg-[rgba(255,255,255,0.28)] p-2 flex items-center justify-center">
              <CloseIcon
                className="text-white cursor-pointer"
                onClick={onClose}
                fontSize="small"
              />
            </div>
          </div>
          <div className="flex flex-col items-center justify-center gap-6 h-fit pb-7">
            <img
              className="w-[278px]"
              src="/image/logo.png"
              alt="Tina Music Logo"
            />
            <div className="flex flex-col gap-2 items-center justify-center">
              <p className="text-white text-[26px] font-bold">
                {t("common.title_modal_install")}
              </p>
              <p className="text-[#CFCFCF] text-[14px]">
                {t("common.subtitle_modal_install")}
              </p>
            </div>
            <div className="rounded-lg border border-[rgba(255,255,255,0.1)] w-[228px] h-fit p-[2px] flex items-center justify-center gap-[1px]">
              <button
                className={`w-full h-full rounded-lg text-[14px] text-white px-4 py-2 ${
                  os === "ios" ? "bg-[rgba(255,255,255,0.1)]" : ""
                }`}
                onClick={() => setOS("ios")}
              >
                IOS
              </button>
              <button
                className={`w-full h-full rounded-lg text-[14px] text-white px-4 py-2 ${
                  os === "android" ? "bg-[rgba(255,255,255,0.1)]" : ""
                }`}
                onClick={() => setOS("android")}
              >
                Android
              </button>
            </div>
            <div className="flex flex-col items-center justify-center gap-7">
              <div className="p-[14.42px] w-fit border-[1.2px] border-[rgba(255,255,255,0.1)] rounded-[19.23px] flex ">
                <img
                  className="rounded-[14.42px]"
                  src={
                    os === "ios" ? "/image/QRIOS.png" : "/image/QRAndroid.png"
                  }
                  alt="download qr"
                />
              </div>
            </div>
          </div>
        </div>
      </Modal>
    </>
  );
};
export default ModalAppInstall;
