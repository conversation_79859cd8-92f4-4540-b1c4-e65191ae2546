import {IRootState} from "@redux/store";
import {t} from "i18next";
import {useSelector} from "react-redux";
import {
  AudioQualityTypeEnum,
  CountryEnum,
  EActivityStatus,
  EArtistType,
  EGender,
  GenderEnum,
  Role,
} from "src/types";

interface PackageInfo {
  text: string;
  color: string;
}

interface StatusInfo {
  colorStatus: string;
  textStatus: string;
}

type ShareType = "artist" | "song" | "playlist";

interface ShareLinkParams {
  type: ShareType;
  data: any;
}

export function generateShareLink({type, data}: ShareLinkParams): string {
  const origin = window.location.origin;

  switch (type) {
    case "artist":
      return `${origin}/artist/${data?.urlSlug}`;

    case "song":
      return `${origin}/playlist/${data?.playlists?.[0]?.urlSlug}?songId=${data?.id}`;

    case "playlist":
      return `${origin}/playlist/${data?.urlSlug}`;

    default:
      return origin;
  }
}

export const useAudioQualityOptions = () => {
  return [
    {value: AudioQualityTypeEnum.KBPS_128, label: "128 kb"},
    {value: AudioQualityTypeEnum.KBPS_320, label: "320 kb"},
  ];
};

export const useFullName = () => {
  const {userInfo} = useSelector((state: IRootState) => state.user);
  return `${userInfo?.firstName ?? ""} ${userInfo?.lastName ?? ""}`;
};

export const useArtistTypeOptions = () => {
  return [
    {value: EArtistType.SINGER, label: t("cms.artist.singer")},
    {value: EArtistType.MUSICIAN, label: t("cms.artist.musician")},
    {
      value: EArtistType.SINGER_MUSICIAN,
      label: t("cms.artist.singer_musician"),
    },
  ];
};

export const useStatusArtistOptions = () => {
  return [
    {value: EActivityStatus.ACTIVE, label: t("common.active")},
    {value: EActivityStatus.INACTIVE, label: t("common.deactivate")},
  ];
};

export const getStatusArtist = (status: number): StatusInfo => {
  switch (status) {
    case EActivityStatus.ACTIVE:
      return {colorStatus: "#039C00", textStatus: t("common.active")};
    case EActivityStatus.INACTIVE:
      return {colorStatus: "#EF3948", textStatus: t("common.deactivate")};
    default:
      return {colorStatus: "#111111", textStatus: t("common.unknown")};
  }
};

export const getPackageInfo = (value: number): PackageInfo => {
  switch (value) {
    case 1:
      return {text: t("cms.premium.experience_package"), color: "#68D2B6"};
    case 2:
      return {text: t("cms.premium.year_package"), color: "#FF4319"};
    case 3:
      return {text: t("cms.premium.promotion_package"), color: "#0F96FF"};
    default:
      return {text: t("common.unknown"), color: "#111111"};
  }
};

export const getStatusInfo = (status: string): StatusInfo => {
  switch (status) {
    case "ACTIVE":
      return {colorStatus: "#039C00", textStatus: t("common.active")};
    case "PENDING":
      return {colorStatus: "#EF3948", textStatus: t("common.deactivate")};
    default:
      return {colorStatus: "#111111", textStatus: t("common.locked")};
  }
};

export const useLanguageOptions = () => {
  return [
    {value: "vi", label: "Tiếng Việt"},
    {value: "en", label: "English"},
    {value: "lo", label: "ພາສາລາວ"},
  ];
};

export const useRoleOptions = () => {
  return [
    {value: Role.ADMIN, label: t("common.admin")},
    {value: Role.ARTIST, label: t("common.singer")},
    {value: Role.END_USER, label: t("common.user")},
  ];
};

export const useGenderOptions = () => {
  return [
    {value: GenderEnum.MALE, label: t("common.male")},
    {value: GenderEnum.FEMALE, label: t("common.female")},
    {value: GenderEnum.UNKNOWN, label: t("common.unknown")},
  ];
};

export const useGender = () => {
  return [
    {value: EGender.MALE, label: t("common.male")},
    {value: EGender.FEMALE, label: t("common.female")},
    {value: EGender.UNKNOWN, label: t("common.unknown")},
  ];
};

export const useCountryOptions = () => {
  return [
    {value: CountryEnum.VIETNAM, label: t("common.country_name.vietnam")},
    {
      value: CountryEnum.UNITED_STATES,
      label: t("common.country_name.united_states"),
    },
    {value: CountryEnum.CANADA, label: t("common.country_name.canada")},
    {
      value: CountryEnum.UNITED_KINGDOM,
      label: t("common.country_name.united_kingdom"),
    },
    {value: CountryEnum.FRANCE, label: t("common.country_name.france")},
    {value: CountryEnum.GERMANY, label: t("common.country_name.germany")},
    {value: CountryEnum.JAPAN, label: t("common.country_name.japan")},
    {
      value: CountryEnum.SOUTH_KOREA,
      label: t("common.country_name.south_korea"),
    },
    {value: CountryEnum.CHINA, label: t("common.country_name.china")},
    {value: CountryEnum.INDIA, label: t("common.country_name.india")},
    {value: CountryEnum.AUSTRALIA, label: t("common.country_name.australia")},
    {value: CountryEnum.BRAZIL, label: t("common.country_name.brazil")},
    {value: CountryEnum.RUSSIA, label: t("common.country_name.russia")},
    {value: CountryEnum.ITALY, label: t("common.country_name.italy")},
    {value: CountryEnum.SPAIN, label: t("common.country_name.spain")},
    {value: CountryEnum.MEXICO, label: t("common.country_name.mexico")},
    {value: CountryEnum.INDONESIA, label: t("common.country_name.indonesia")},
    {value: CountryEnum.THAILAND, label: t("common.country_name.thailand")},
    {value: CountryEnum.MALAYSIA, label: t("common.country_name.malaysia")},
    {
      value: CountryEnum.PHILIPPINES,
      label: t("common.country_name.philippines"),
    },
    {
      value: CountryEnum.SOUTH_AFRICA,
      label: t("common.country_name.south_africa"),
    },
  ];
};
