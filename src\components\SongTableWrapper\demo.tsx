import React from "react";
import SongTableWrapper from "./index";
import { ISong } from "../../types/song";

// Demo data
const demoSongs: ISong[] = [
  {
    id: "1",
    name: "Demo Song 1",
    duration: 180000, // 3 minutes in milliseconds
    artists: [
      {
        id: "artist1",
        name: "Demo Artist 1",
        stageName: "Demo Artist 1",
        urlSlug: "demo-artist-1"
      }
    ],
    images: {
      SMALL: "/image/default-music.png",
      DEFAULT: "/image/default-music.png"
    },
    playlists: [
      {
        id: "playlist1",
        name: "Demo Album 1",
        urlSlug: "demo-album-1"
      }
    ],
    releaseDate: "2024-01-01",
    isLiked: false,
    type: "SONG" as any
  },
  {
    id: "2", 
    name: "Demo Song 2",
    duration: 240000, // 4 minutes
    artists: [
      {
        id: "artist2",
        name: "Demo Artist 2", 
        stageName: "Demo Artist 2",
        urlSlug: "demo-artist-2"
      }
    ],
    images: {
      SMALL: "/image/default-music.png",
      DEFAULT: "/image/default-music.png"
    },
    playlists: [
      {
        id: "playlist2",
        name: "Demo Album 2",
        urlSlug: "demo-album-2"
      }
    ],
    releaseDate: "2024-02-01",
    isLiked: true,
    type: "SONG" as any
  }
];

export default function SongTableWrapperDemo() {
  const handlePlaySong = (song: ISong) => {
    console.log("Playing song:", song.name);
  };

  return (
    <div className="p-4 bg-gray-900 min-h-screen">
      <h1 className="text-white text-2xl mb-4">SongItem Table Layout Demo</h1>
      
      <div className="mb-8">
        <h2 className="text-white text-xl mb-2">Table Layout</h2>
        <SongTableWrapper
          songs={demoSongs}
          onPlaySong={handlePlaySong}
          showNumber={true}
          showDuration={true}
          showAlbumInfo={true}
          showReleaseDate={true}
        />
      </div>

      <div className="mb-8">
        <h2 className="text-white text-xl mb-2">Regular Flex Layout</h2>
        <div className="space-y-2">
          {demoSongs.map((song, index) => (
            <SongItem
              key={song.id}
              song={song}
              tableLayout={false}
              handlePlayMusic={() => handlePlaySong(song)}
              showDuration={true}
              showAlbumInfo={true}
              showReleaseDate={true}
            />
          ))}
        </div>
      </div>
    </div>
  );
}
