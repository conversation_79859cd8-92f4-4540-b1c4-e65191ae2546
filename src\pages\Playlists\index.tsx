import ApiPlaylistDetail from "@api/ApiPlaylistDetail";
import QUERY_KEY from "@api/QueryKey";
import AlbumCardSkeleton from "@components/AlbumCardSkeleton";
import ArtistCardSkeleton from "@components/ArtistCardSkeleton";
import LikeButton from "@components/AuthButton/LikeButton";
import CommonAlbumCard from "@components/CommonAlbumCard";
import CommonArtistCard from "@components/CommonArtistCard";
import IconNoData from "@components/Icon/IconNoData";
import Slider from "@components/Slider";
import {
  DeleteOutlineRounded,
  PauseCircle,
  PlayCircle,
} from "@mui/icons-material";
import {CircularProgress, Skeleton} from "@mui/material";
import SongCardSkeleton from "@pages/MusicLibrary/components/SongCardSkeleton";
import {
  updateCurrentSong,
  playSongFromList,
  setCurrentSong,
} from "@redux/slices/PlayerSlice";
import {IRootState} from "@redux/store";
import {
  keepPreviousData,
  useMutation,
  useQuery,
  useQueryClient,
} from "@tanstack/react-query";
import clsx from "clsx";
import {useEffect, useRef, useState} from "react";
import {useTranslation} from "react-i18next";
import {useDispatch, useSelector} from "react-redux";
import {useNavigate, useParams, useSearchParams} from "react-router-dom";
import {toast} from "react-toastify";
import {IParamsDefault, ISong, PlaylistType} from "src/types";
import {SwiperSlide} from "swiper/react";
import SubTitleSkeleton from "@components/SubTitleSkeleton";
import IconEdit from "@components/Icon/IconEdit";
import ModalUpdateMyPlaylist from "@pages/MusicLibrary/MyPlaylist/components/ModalUpdateMyPlaylist";
import ModalConfirmClient from "@components/ModalConfirmClient";
import ApiSong from "@api/ApiSong";
import IconShare from "@components/Icon/IconShare";
import ModalShare from "@components/ModalShare";
import {generateShareLink} from "src/utils/global";
import PlayerUtil from "src/core/PlayerUtil";
import HeaderTitle from "@components/HeaderTitle";
import IconTrash from "@components/Icon/IconTrash";
import TableSongItem from "@components/TableSongItem";

export default function Playlists(): JSX.Element {
  const [param] = useSearchParams();
  const {t} = useTranslation();
  const dispatch = useDispatch();
  const {paused, currentPlaylistId} = useSelector(
    (state: IRootState) => state?.player,
  );
  const {userInfo} = useSelector((state: IRootState) => state.user);

  const navigate = useNavigate();
  const {urlSlug} = useParams<{urlSlug: string}>();
  const containerRef = useRef<HTMLDivElement | null>(null);
  const songId = param.get("songId");
  const [openUpdateModal, setOpenUpdateModal] = useState(false);
  const [openConfirmDelete, setOpenConfirmDelete] = useState(false);
  const [openModalShare, setOpenModalShare] = useState<boolean>(false);
  const queryClient = useQueryClient();

  const handleOpenUpdateModal = () => {
    setOpenUpdateModal(true);
  };

  const handleCloseUpdateModal = () => {
    setOpenUpdateModal(false);
  };

  const handleOpenConfirmDelete = () => {
    setOpenConfirmDelete(true);
  };

  const handleCloseConfirmDelete = () => {
    setOpenConfirmDelete(false);
  };
  const params: IParamsDefault = {
    page: 0,
    pageSize: 200,
  };
  const {
    data: albumsData,
    isLoading: isAlbumLoading,
    refetch: refetchAlbumData,
  } = useQuery({
    queryKey: [QUERY_KEY.PLAYLIST.GET_PLAYLIST_SONGS, urlSlug],
    queryFn: () => ApiPlaylistDetail.getPlaylistDetail(urlSlug || ""),
    enabled: !!urlSlug,
  });

  const [isLike, setIsLike] = useState(albumsData?.data?.isLiked);
  useEffect(() => {
    setIsLike(albumsData?.data?.isLiked);
  }, [albumsData?.data]);
  const {
    data: songData,
    isLoading: isSongsLoading,
    isError: isSongsError,
  } = useQuery({
    queryKey: [QUERY_KEY.PLAYLIST.GET_PLAYLIST_SONGS, params, urlSlug],
    placeholderData: keepPreviousData,
    queryFn: () => ApiPlaylistDetail.getPlaylistSongs(urlSlug || "", params),
    enabled: !!urlSlug,
  });
  const {data: artistsData, isLoading: isArtistLoading} = useQuery({
    queryKey: [QUERY_KEY.PLAYLIST.GET_ARTISTS_LIST, urlSlug],
    placeholderData: keepPreviousData,
    queryFn: () =>
      ApiPlaylistDetail.getArtistsList(urlSlug || "", {page: 0, pageSize: 10}),
    enabled: !!urlSlug,
  });
  const {data: relevantAlbumData, isLoading: isRelevantAlbumDataLoading} =
    useQuery({
      queryKey: [QUERY_KEY.PLAYLIST.GET_RELEVANT_PLAYLISTS, urlSlug],
      placeholderData: keepPreviousData,
      queryFn: () =>
        ApiPlaylistDetail.getRecommendedPlaylists({
          page: 0,
          pageSize: 10,
          type: PlaylistType.PLAYLIST,
        }),
      enabled: !!urlSlug,
    });
  const listenPlaylistMutate = useMutation({
    mutationFn: ApiPlaylistDetail.listenPlaylist,
  });

  const sharePlaylistMutate = useMutation({
    mutationFn: ApiPlaylistDetail.sharePlaylist,
    onSuccess: () => {
      const link = generateShareLink({
        type: "playlist",
        data: albumsData?.data,
      });
      navigator.clipboard.writeText(link).then(() => {
        toast.success(t("common.copy_link_success"));
      });
    },
    onError: () => {
      toast.error(t("common.copy_link_failed"));
    },
  });

  const removeSongMutate = useMutation({
    mutationFn: ({songId, playlistId}: {songId: string; playlistId: string}) =>
      ApiSong.addAndRemoveSongToPlaylist({songId, playlistId}),
  });

  const handlePlayAlbum = (song?: ISong) => {
    if (!albumsData?.data || !songData?.data) return;
    if (!paused && currentPlaylistId === albumsData?.data?.id && !song) {
      PlayerUtil.instance.pause();
      return;
    }

    if (!song) {
      if (currentPlaylistId === albumsData?.data?.id) {
        PlayerUtil.instance.play();
      } else {
        listenPlaylistMutate.mutateAsync(albumsData?.data?.id ?? "", {
          onSuccess: (res) => {
            if (res) {
              dispatch(
                playSongFromList({
                  songList: songData?.data,
                  playlistId: albumsData?.data?.id,
                }),
              );
            }
          },
        });
      }
    } else {
      if (currentPlaylistId === albumsData?.data?.id) {
        dispatch(updateCurrentSong(song));
      } else {
        listenPlaylistMutate.mutateAsync(albumsData?.data?.id ?? "", {
          onSuccess: (res) => {
            if (res) {
              dispatch(
                playSongFromList({
                  song: song,
                  songList: songData?.data,
                  playlistId: albumsData?.data?.id,
                }),
              );
            }
          },
        });
      }
    }
  };

  useEffect(() => {
    if (containerRef?.current) {
      containerRef?.current?.scrollIntoView({
        behavior: "smooth",
        block: "start",
      });
    }
  }, [urlSlug]);

  const likePlaylistMutate = useMutation({
    mutationFn: ApiPlaylistDetail.likePlaylist,
    onSuccess: (res) => {
      setIsLike((prev) => !prev);
      toast.success(
        res.isLiked
          ? t("common.like_playlist_successfully")
          : t("common.dislike_playlist_successfully"),
      );
    },
    onError: () => {
      toast.error(t("common.operation_failed"));
    },
  });

  const likeOrDislikePlaylist = (playlistId: string) => {
    likePlaylistMutate.mutateAsync(playlistId);
  };

  const {data: querySong} = useQuery({
    queryKey: [QUERY_KEY.SONG.GET_SONG_BY_ID, songId],
    queryFn: () => ApiSong.getSongById(songId ?? ""),
    enabled: !!songId,
  });

  useEffect(() => {
    if (!querySong) return;
    if (querySong) {
      dispatch(setCurrentSong(querySong));
    }

    if (urlSlug === "undefined") {
      navigate("/");
    }
  }, [urlSlug, navigate, querySong, dispatch]);

  const deleteMyPlaylistMutation = useMutation({
    mutationFn: ApiPlaylistDetail.deletePlaylist,
  });

  const handleDeletePlaylist = (urlSlug: string) => {
    deleteMyPlaylistMutation.mutate(urlSlug, {
      onSuccess: () => {
        queryClient.invalidateQueries({
          queryKey: [QUERY_KEY.PLAYLIST.DELETE_MY_PLAYLIST],
        });
        navigate(-1);
        toast.success(t("common.delete_successfully"));
      },
    });
  };

  const handleSharePlaylist = () => {
    sharePlaylistMutate.mutateAsync(albumsData?.data?.id || "");
  };

  const handleDeleteSongInPlaylist = (songId: string, playlistId: string) => {
    removeSongMutate.mutate(
      {songId, playlistId},
      {
        onSuccess: () => {
          queryClient.invalidateQueries({
            queryKey: [QUERY_KEY.PLAYLIST.GET_PLAYLIST_SONGS],
          });
          toast.success(t("common.remove_from_playlist_successfully"));
        },
        onError: () => {
          toast.error(t("common.action_error"));
        },
      },
    );
  };

  const handleOpenModalShare = () => {
    setOpenModalShare(true);
  };

  const handleCloseModalShare = () => {
    setOpenModalShare(false);
  };

  return (
    <div className="mb-[5vh]" ref={containerRef}>
      <HeaderTitle title={t("common.playlist")} name={albumsData?.data.name} />
      <div className="flex flex-col gap-3 sm:gap-4 md:gap-5 lg:gap-6 w-full">
        <div className="album-detail flex lg:flex-row flex-col sm:px-6 md:px-8 pb-2 mt-10 lg:gap-8 md:gap-6 sm:gap-4 gap-3">
          <div
            className="flex flex-col lg:gap-6 md:gap-5 sm:gap-4 gap-3 lg:w-[300px] w-[250px] mx-auto items-center 
             lg:sticky top-[40px] self-start"
          >
            {!isAlbumLoading ? (
              <img
                src={
                  albumsData?.data?.images?.DEFAULT ||
                  albumsData?.data?.images?.SMALL ||
                  "/image/default-music.png"
                }
                alt="cover"
                className="aspect-square object-cover rounded-xl items-center w-full"
              />
            ) : (
              <Skeleton
                variant="rounded"
                sx={{
                  height: {lg: "300px", xs: "250px"},
                  objectFit: "cover",
                  borderRadius: "12px",
                  display: "flex",
                  alignItems: "center",
                  bgcolor: "#752121CC",
                  maxWidth: "100%",
                  width: "100%",
                }}
              />
            )}
            {isAlbumLoading ? (
              <div className="flex items-center justify-center w-full h-[10vh] flex-col gap-2">
                <Skeleton
                  variant="text"
                  sx={{bgcolor: "#752121CC", width: "60%", height: "34px"}} // Giữ màu đồng nhất, chiều cao gần giống text-[28px]
                />
                <Skeleton
                  variant="text"
                  sx={{bgcolor: "#752121CC", width: "40%", height: "20px"}}
                />
              </div>
            ) : (
              <div className="bottom-img justify-center items-center flex flex-col gap-2">
                <div className="flex items-center gap-2">
                  <div className="text-white lg:text-[28px] md:text-2xl sm:text-xl text-lg font-bold text-center">
                    {albumsData?.data?.name || t("common.playlist")}
                  </div>
                  {userInfo?.id === albumsData?.data?.user?.id && (
                    <IconEdit
                      stroke="#FFFFFF80"
                      className="w-5 h-5 cursor-pointer"
                      onClick={handleOpenUpdateModal}
                    />
                  )}
                </div>
                <div className="flex flex-col gap-1 lg:gap-2 w-full">
                  <div className="bottom-text flex gap-2 w-full justify-center truncate">
                    <div
                      className={`text-neutral-400 lg:text-base text-sm line-clamp-1`}
                    >
                      {albumsData?.data?.type === PlaylistType.PLAYLIST ? (
                        <>{albumsData?.data?.user?.username}</>
                      ) : (
                        <>
                          {albumsData?.data &&
                          albumsData?.data?.artists?.length > 1 ? (
                            t("playlist.many_singers")
                          ) : albumsData?.data?.artists[0]?.name ? (
                            <span
                              onClick={() =>
                                navigate(
                                  `/artist/${albumsData?.data?.artists[0]?.urlSlug}`,
                                )
                              }
                              className="cursor-pointer hover:text-sky-500 hover:underline"
                            >
                              {albumsData?.data?.artists[0]?.name}
                            </span>
                          ) : (
                            <span>{t("common.not_info_artist")}</span>
                          )}
                        </>
                      )}
                    </div>
                    <div className="text-neutral-400 font-bold lg:text-base text-sm">
                      -
                    </div>
                    <div className="text-neutral-400 lg:text-base text-sm">
                      {`${
                        albumsData?.data?.totalSongs
                          ? albumsData?.data?.totalSongs
                          : "0"
                      } ${t("playlist.songs")}`}
                    </div>
                  </div>
                  {albumsData?.data?.type === PlaylistType.PLAYLIST && (
                    <span className="text-[#FFFFFF80] lg:text-base text-sm flex w-full justify-center">
                      {albumsData?.data?.isPublic
                        ? t("playlist.public")
                        : t("playlist.private")}
                    </span>
                  )}
                </div>
              </div>
            )}
            <div className="flex sm:flex-col flex-row lg:gap-6 md:gap-5 sm:gap-4 gap-3 items-center justify-center w-full">
              <div className="album-button">
                {!isAlbumLoading ? (
                  <button
                    className={`bg-[#FF4319] border-transparent border rounded-full min-w-[116px] text-white lg:text-lg md:text-base text-sm lg:py-2 md:py-1.5 py-1 lg:gap-3 md:gap-2 gap-1 flex items-center justify-center lg:px-4 md:px-3 px-2 ${
                      albumsData?.data?.totalSongs === 0
                        ? "opacity-50 cursor-not-allowed"
                        : ""
                    }`}
                    onClick={() => {
                      handlePlayAlbum();
                    }}
                    disabled={albumsData?.data?.totalSongs === 0}
                  >
                    {albumsData?.data?.totalSongs === 0 ? (
                      t("playlist.no_song")
                    ) : (albumsData?.data &&
                        albumsData?.data?.id === currentPlaylistId &&
                        !paused) ||
                      listenPlaylistMutate.isPending ? (
                      !listenPlaylistMutate.isPending ? (
                        <>
                          <PauseCircle
                            sx={{
                              fontSize: {
                                xs: "22px",
                                sm: "26px",
                                md: "30px",
                                lg: "34px",
                              },
                            }}
                          />
                          {t("common.pending")}
                        </>
                      ) : (
                        <CircularProgress size="25px" sx={{color: "white"}} />
                      )
                    ) : (
                      <>
                        <PlayCircle
                          sx={{
                            fontSize: {
                              xs: "22px",
                              sm: "26px",
                              md: "30px",
                              lg: "34px",
                            },
                          }}
                        />
                        {t("playlist.play_all")}
                      </>
                    )}
                  </button>
                ) : (
                  <Skeleton
                    variant="rounded"
                    sx={{
                      bgcolor: "#752121CC",
                      border: "transparent",
                      borderRadius: "999px",
                      height: "48px",
                      width: "200px",
                      maxWidth: "100%",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      gap: "12px",
                    }}
                  />
                )}
              </div>
              <div
                className={clsx(
                  "action flex flex-row gap-2 items-center",
                  isAlbumLoading && "!hidden",
                )}
              >
                <LikeButton
                  isLiked={isLike}
                  action={() =>
                    likeOrDislikePlaylist(albumsData?.data?.id ?? "")
                  }
                  loading={likePlaylistMutate.isPending}
                />
                <IconShare
                  className="cursor-pointer"
                  width={22}
                  height={22}
                  onClick={handleOpenModalShare}
                />

                {userInfo?.id === albumsData?.data?.user?.id && (
                  <IconTrash
                    className="cursor-pointer text-[#ffffffcc] w-[26px] h-[26px]"
                    onClick={handleOpenConfirmDelete}
                  />
                )}
              </div>
            </div>
          </div>
          <div className="lg:w-3/4 w-full">
            <div className="flex flex-col">
              {(isAlbumLoading || isSongsLoading) && (
                <div className="relative flex flex-col">
                  {Array.from({length: 5}).map((_, index) => (
                    <SongCardSkeleton key={`skeleton_song_${index}`} />
                  ))}
                </div>
              )}

              {isSongsError && (
                <div className="text-white my-[5vh] w-full h-fit flex-col gap-4 flex justify-center items-center text-lg">
                  <IconNoData />
                  {t("common.list_empty")}
                </div>
              )}

              {!isSongsLoading &&
                !isAlbumLoading &&
                songData?.data &&
                songData?.data?.length > 0 && (
                  <div className="flex flex-col px-10 max-lg:px-8 max-md:px-6 max-sm:px-4 pb-2">
                    <TableSongItem
                      songs={songData?.data}
                      showAlbumInfo={false}
                      showReleaseDate={false}
                      showNumber={false}
                      onPlaySong={handlePlayAlbum}
                      extraMenuActions={
                        userInfo?.id === albumsData?.data?.user?.id
                          ? [
                              {
                                icon: <DeleteOutlineRounded />,
                                label: t("common.delete"),
                                action: (song) =>
                                  handleDeleteSongInPlaylist(
                                    song.id as string,
                                    albumsData?.data.id as string,
                                  ),
                              },
                            ]
                          : []
                      }
                    />
                  </div>
                )}
            </div>
          </div>
        </div>

        <div className="extend text-white flex flex-col gap-2 sm:gap-3 md:gap-4 lg:gap-5 px-10 max-lg:px-8 max-md:px-6 max-sm:px-4 pb-2">
          {isArtistLoading ? (
            <>
              <SubTitleSkeleton />
              <Slider slidesPerView={5.5}>
                {[...Array(6)].map((_, index) => (
                  <SwiperSlide key={index} virtualIndex={index}>
                    <ArtistCardSkeleton />
                  </SwiperSlide>
                ))}
              </Slider>
            </>
          ) : (
            (artistsData?.data?.length ?? 0) > 0 && (
              <>
                <div className="max-sm:text-[18px] font-bold text-white text-[22px]">
                  {t("playlist.participating_artists")}
                </div>
                <Slider slidesPerView={6}>
                  {artistsData?.data?.map((artist, index) => (
                    <SwiperSlide key={index} virtualIndex={index}>
                      <CommonArtistCard data={artist} />
                    </SwiperSlide>
                  ))}
                </Slider>
              </>
            )
          )}

          {isRelevantAlbumDataLoading ? (
            <>
              <SubTitleSkeleton />
              <Slider slidesPerView={5.5}>
                {[...Array(6)].map((_, index) => (
                  <SwiperSlide key={index} virtualIndex={index}>
                    <AlbumCardSkeleton />
                  </SwiperSlide>
                ))}
              </Slider>
            </>
          ) : (
            (relevantAlbumData?.data?.length ?? 0) > 0 && (
              <>
                <div className="title max-sm:text-[18px] flex flex-row text-white gap-3 items-center font-bold text-[22px]">
                  {t("playlist.you_may_like")}
                </div>
                <Slider slidesPerView={5.5}>
                  {relevantAlbumData?.data?.map((album, index) => (
                    <SwiperSlide key={index} virtualIndex={index}>
                      <CommonAlbumCard data={album} />
                    </SwiperSlide>
                  ))}
                </Slider>
              </>
            )
          )}
        </div>
      </div>
      {albumsData?.data && (
        <ModalUpdateMyPlaylist
          open={openUpdateModal}
          onClose={handleCloseUpdateModal}
          data={albumsData?.data}
          refetchAlbumData={refetchAlbumData}
        />
      )}

      <ModalConfirmClient
        open={openConfirmDelete}
        onClose={handleCloseConfirmDelete}
        onConfirm={() => {
          handleDeletePlaylist(albumsData?.data?.urlSlug ?? "");
          handleCloseConfirmDelete();
        }}
        title={t("playlist.delete_playlist")}
        loading={deleteMyPlaylistMutation.isPending}
      >
        <span className="text-[#E3E3E3] text-xl">
          {albumsData?.data?.name} {t("playlist.confirm_delete_playlist")}
        </span>
      </ModalConfirmClient>
      <ModalShare
        open={openModalShare}
        onCancel={handleCloseModalShare}
        handleCopyLink={handleSharePlaylist}
        image={
          albumsData?.data?.images?.SMALL || albumsData?.data?.images?.DEFAULT
        }
        name={albumsData?.data?.name}
        shareUrl={generateShareLink({type: "playlist", data: albumsData?.data})}
      />
    </div>
  );
}
