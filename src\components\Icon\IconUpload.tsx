import {SVGProps} from "react";

function IconUpload(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={18}
      height={18}
      fill="none"
      viewBox="0 0 18 18"
      {...props}
    >
      <g clipPath="url(#a)">
        <path
          stroke="#FF4319"
          strokeLinecap="round"
          strokeLinejoin="round"
          d="M12.876 11.25a4.463 4.463 0 0 1-7.752 0H2.25a1.125 1.125 0 0 0-1.125 1.125v3.375a1.125 1.125 0 0 0 1.125 1.125h13.5a1.125 1.125 0 0 0 1.125-1.125v-3.375a1.125 1.125 0 0 0-1.125-1.125h-2.874ZM9 1.125V9m2.813-5.625L9 1.125l-2.813 2.25"
        />
      </g>
      <defs>
        <clipPath id="a">
          <path fill="#fff" d="M0 0h18v18H0z" />
        </clipPath>
      </defs>
    </svg>
  );
}

export default IconUpload;
