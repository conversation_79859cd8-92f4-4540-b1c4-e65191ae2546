import {defineConfig} from "vite";
import react from "@vitejs/plugin-react";
import tsconfigPaths from "vite-tsconfig-paths";

// https://vite.dev/config/
export default defineConfig({
  base: "./",
  root: "./",
  plugins: [react(), tsconfigPaths()],
  build: {
    outDir: "mini_app_dist",
    minify: false,
    emptyOutDir: true,
  },
  server: {
    allowedHosts: [
      "mobile-sdk.itpro.vn", // Thêm host cần cho phép
    ],
    port: 3000,
    open: true,
  },
  preview: {
    port: 3000,
  },
  css: {
    preprocessorOptions: {
      scss: {
        api: "modern-compiler",
      },
    },
  },
});
