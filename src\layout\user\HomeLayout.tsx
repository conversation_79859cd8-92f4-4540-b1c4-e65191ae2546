import {IRootState} from "@redux/store";
import clsx from "clsx";
import {useEffect, useState} from "react";
import {useSelector} from "react-redux";
import AuthModal from "../components/AuthModal";
import BottomPlayer from "../components/BottomPlayer";
import HomeSearchbar from "../components/HomeSearchbar";
import HomeSidebar from "../components/HomeSidebar";
import {MusicPlayDetailsModal} from "../components/MusicPlayDetailsModal";
import MusicQueue from "../components/MusicQueue";
import "./index.scss";
import {useLocation} from "react-router-dom";
import {useNetworkStatus, useWindowWidth} from "src/utils/hooks";
import DownloadSnackbar from "../components/DownloadSnackbar";
import NoInternet from "./NoInternet";
import NoInternetSnackbar from "../components/NoInternetSnackbar";
import ModalUpgrade from "@components/ModalUpgrade";

interface HomeLayoutProps {
  children: JSX.Element;
}

export default function HomeLayout({children}: HomeLayoutProps) {
  const {currentSong} = useSelector((state: IRootState) => state?.player);
  const {isOnline} = useNetworkStatus();
  const openWishList = useSelector(
    (state: IRootState) => state.wishlist.isOpen,
  );
  const [open, setOpen] = useState(false);

  const {pathname} = useLocation();

  const width = useWindowWidth();

  function toggleDrawer(newOpen: boolean) {
    setOpen(newOpen);
  }

  useEffect(() => {
    const container = document.querySelector("#layout-container");
    if (container) {
      container.scrollTo({top: 0, behavior: "smooth"});
    }
  }, [pathname]);

  return (
    <div
      className="relative"
      onContextMenu={(e) => {
        if (import.meta.env.PROD) {
          e.preventDefault();
        }
      }}
    >
      <div className="flex">
        <HomeSidebar isDrawerOpen={open} toggleDrawer={toggleDrawer} />
        <div
          id="layout-container"
          className={clsx(
            "overflow-y-auto h-svh flex-1 bg-[linear-gradient(#340707,#1B0606_20%,#090404)] hide-scrollbar flex flex-col",
            currentSong ? "max-[568px]:pb-[68px] pb-[96px]" : "",
          )}
        >
          <HomeSearchbar
            className={clsx("sticky top-0 z-10 home-searchbar", {
              "bg-[rgb(45,7,7)]": location.pathname !== "/upgrade",
            })}
            toggleDrawer={toggleDrawer}
          />
          <div className="flex-1">{isOnline ? children : <NoInternet />}</div>
          {width > 924 && isOnline && (
            <MusicQueue
              className={clsx(
                "fixed z-20 bottom-[96px] right-0 w-[380px] h-[calc(100vh-164px)] bg-[linear-gradient(#250707,#0A0505)] shadow-[0_4px_29.6px_0p_#FFFFFF12]",
                !openWishList && "translate-x-full",
              )}
            />
          )}
        </div>
        {isOnline && (
          <BottomPlayer className="fixed bottom-0 h-[96px] max-[568px]:h-[68px]" />
        )}
      </div>
      <MusicPlayDetailsModal />
      <AuthModal />
      <DownloadSnackbar />
      <NoInternetSnackbar className="fixed bottom-0 h-[96px] max-[568px]:h-[68px] z-40" />
      <ModalUpgrade />
    </div>
  );
}
