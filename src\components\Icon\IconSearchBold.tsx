import {SVGProps} from "react";

function IconSearchBold(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={24}
      height={24}
      fill="none"
      {...props}
    >
      <path
        fill={props.fill || "currentColor"}
        fillRule="evenodd"
        d="M3.752 10.582c0-3.882 3.158-7.039 7.039-7.039 3.88 0 7.038 3.157 7.038 7.038 0 3.882-3.158 7.04-7.038 7.04a7.047 7.047 0 0 1-7.04-7.04ZM22.247 21.04l-4.599-4.588a8.99 8.99 0 0 0 2.181-5.871c0-4.984-4.055-9.039-9.038-9.039-4.984 0-9.04 4.055-9.04 9.039.001 4.983 4.056 9.038 9.04 9.038a8.973 8.973 0 0 0 5.392-1.803l4.652 4.64 1.412-1.416Z"
        clipRule="evenodd"
      />
      <path
        fill={props.fill || "currentColor"}
        fillRule="evenodd"
        d="M10.807 4.778a5.717 5.717 0 0 1 5.717 5.717h-3.177a2.54 2.54 0 0 0-5.081 0H5.09a5.717 5.717 0 0 1 5.717-5.717Zm1.27 5.717H9.534a1.27 1.27 0 0 1 2.541 0Z"
        clipRule="evenodd"
      />
    </svg>
  );
}

export default IconSearchBold;
