import IconCancel from "@components/Icon/IconCancel";
import {IRootState} from "@redux/store";
import {useState} from "react";
import {useTranslation} from "react-i18next";
import {useSelector} from "react-redux";

export default function DownloadSnackbar() {
  const {t} = useTranslation();
  const {currentSong} = useSelector((state: IRootState) => state.player);
  const [showLinkToApp, setShowLinkToApp] = useState(true);

  const handleLinkToApp = () => {
    setShowLinkToApp(false);
    const userAgent =
      navigator.userAgent || navigator.vendor || (window as any).opera;
    const playStoreUrl =
      "https://play.google.com/store/apps/details?id=com.tinasoft.tinamusic";
    const appStoreUrl = "https://apps.apple.com/lo/app/laomusic/id6741507765";

    if (/android/i.test(userAgent)) {
      window.open(playStoreUrl, "_blank");
    } else if (
      /iPad|iPhone|iPod/.test(userAgent) &&
      typeof (window as any).MSStream === "undefined"
    ) {
      window.open(appStoreUrl, "_blank");
    }
  };

  return showLinkToApp ? (
    <div
      className={`lg:hidden flex px-4 py-2 w-[calc(100%-3rem)] md:w-[calc(100%-4rem)] cursor-pointer items-center justify-center gap-3 rounded-lg backdrop-blur-[15px] shadow-[0px_4px_4px_0px_#00000040] bg-[linear-gradient(95.26deg,_rgba(67,37,37,0.7)_0.47%,_rgba(38,33,34,0.7)_23.98%,_rgba(51,44,45,0.7)_48.97%,_rgba(79,58,55,0.7)_76.41%,_rgba(43,44,39,0.7)_98.45%)] fixed left-1/2 -translate-x-1/2 h-[50px] max-w-[420px]:h-[54px] sm:h-[58px] z-50 ${currentSong ? "bottom-[110px] max-[568px]:bottom-[80px]" : "bottom-3"}`}
      onClick={handleLinkToApp}
    >
      <img
        src="/favicon.ico"
        className="w-[30px] max-w-[420px]:w-[34px] sm:w-[38px] object-cover rounded-xl"
      />
      <span className="text-white text-xs max-w-[420px]:text-sm sm:text-base">
        {t("common.link_to_app")}
      </span>
      <button
        className="absolute -top-[0.625rem] -right-[0.625rem] text-white bg-[#280C0C] rounded-full w-5 h-5 flex items-center justify-center"
        onClick={(e) => {
          e.stopPropagation();
          setShowLinkToApp(false);
        }}
      >
        <IconCancel className="w-3 h-3" />
      </button>
    </div>
  ) : null;
}
