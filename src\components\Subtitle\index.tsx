import {useTranslation} from "react-i18next";
import IconArrowRight2 from "@components/Icon/IconArrowRight2.tsx";
import RefreshIcon from "@mui/icons-material/Refresh";
interface ISubtitleProps {
  subtitle: string;
  seeMore?: boolean;
  refresh?: boolean;
  handleClick?: () => void;
}

export default function Subtitle({
  subtitle,
  seeMore = true,
  refresh = false,
  handleClick,
}: ISubtitleProps) {
  const {t} = useTranslation();

  return (
    <div className="flex justify-between items-center gap-2">
      <p className="font-semibold text-[16px] sm:text-[18px] md:text-[20px] lg:text-[22px] text-white line-clamp-2">
        {subtitle}
      </p>
      {seeMore && !refresh && (
        <button
          className="flex justify-end md:gap-2 gap-1 sm:gap-1.5 text-[#FF4319] text-xs md:text-sm lg:text-base font-normal items-center"
          onClick={handleClick}
        >
          {t("common.see_more")} <IconArrowRight2 />
        </button>
      )}
      {refresh && !seeMore && (
        <button
          className="rounded-[16px] bg-orange-500 text-white py-2 px-[14px] flex gap-1 text-[14px] md:gap-2 sm:gap-1.5 font-normal items-center justify-center"
          onClick={handleClick}
        >
          <RefreshIcon sx={{fontSize: 16}} />
          {t("common.renew")}
        </button>
      )}
    </div>
  );
}
