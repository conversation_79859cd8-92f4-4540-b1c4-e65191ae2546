import {IPlaylist} from "src/types";
import Slider from "@components/Slider";
import {SwiperSlide} from "swiper/react";
import CommonPlaylistYTBCard from "@components/CommonPlaylistYTBCard";

interface IAllViewMVProp {
  data?: IPlaylist[];
}

export function YoutubePlaylistAllView({data}: IAllViewMVProp) {
  return (
    <Slider slidesPerView={5} spaceBetween={16}>
      {data?.map((item, index) => {
        return (
          <SwiperSlide
            key={`search_youtube_playlist_all_${item.id}`}
            virtualIndex={index}
          >
            <CommonPlaylistYTBCard data={item} />
          </SwiperSlide>
        );
      })}
    </Slider>
  );
}
