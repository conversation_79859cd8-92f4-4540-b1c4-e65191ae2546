import {createSlice, PayloadAction} from "@reduxjs/toolkit";
import PlayerUtil from "src/core/PlayerUtil";
import {EPlayerType} from "src/core/types";
import {ISong, PlayMode} from "src/types";

interface IPlayerState {
  currentSong?: ISong;
  currentPlaylistId?: string;
  currentArtistId?: string;
  queueList: ISong[];
  originalQueueList: ISong[];
  suggestedSongs?: ISong[];
  playMode: PlayMode;
  isShuffle: boolean;
  paused: boolean;
  isOpenLyrics: boolean;
  isSuggestionEnabled: boolean;
  playerType: EPlayerType;
}

const initialState: IPlayerState = {
  playMode: PlayMode.NORMAL,
  isShuffle: false,
  suggestedSongs: [],
  originalQueueList: [],
  queueList: [],
  isOpenLyrics: false,
  paused: true,
  isSuggestionEnabled: true,
  currentPlaylistId: undefined,
  currentArtistId: undefined,
  playerType: EPlayerType.CORE,
};

const PlayerSlice = createSlice({
  name: "player",
  initialState,
  reducers: {
    updatePlayMode(state, action: PayloadAction<PlayMode>) {
      if (state.playMode === action?.payload) {
        state.playMode = PlayMode.NORMAL;
      } else {
        state.playMode = action?.payload;
        state.isSuggestionEnabled = false;
      }
    },
    playSongFromList: (
      state,
      action: PayloadAction<{
        song?: ISong;
        songList?: ISong[];
        playlistId?: string;
        artistId?: string;
      }>,
    ) => {
      if (!action?.payload?.songList) {
        state.currentPlaylistId = undefined;
        state.currentArtistId = undefined;
        state.currentSong = undefined;
        state.queueList = [];
        state.paused = true;
        return;
      }

      const index = action?.payload?.song
        ? action?.payload?.songList?.findIndex(
            (s) => s?.id === action?.payload?.song?.id,
          )
        : 0;
      state.currentSong =
        index === -1
          ? action?.payload?.songList[0]
          : action?.payload?.songList[index];
      state.queueList = action?.payload?.songList;
      state.originalQueueList = action?.payload?.songList;
      if (state.currentPlaylistId !== action.payload.playlistId) {
        state.currentPlaylistId = action.payload.playlistId;
      }
      if (state.currentArtistId !== action.payload.artistId) {
        state.currentArtistId = action.payload.artistId;
      }
      state.paused = false;
    },
    addSongsToQueue: (state, action: PayloadAction<ISong[] | undefined>) => {
      const newSongs = action?.payload;

      if (!newSongs) return;

      state.queueList = [...state.queueList, ...newSongs];
      state.originalQueueList = [...state.originalQueueList, ...newSongs];
    },
    playSingleSong: (state, action: PayloadAction<ISong | undefined>) => {
      state.currentPlaylistId = undefined;
      state.currentArtistId = undefined;
      const song = action?.payload;
      if (!song) {
        state.currentSong = undefined;
        state.queueList = [];
        state.paused = true;
        return;
      }

      state.currentSong = song;
      state.queueList = [song];
      state.originalQueueList = [song];
      state.paused = false;
    },
    handleNextSong: (state, action: PayloadAction<{isManual: boolean}>) => {
      if (!state?.queueList || state?.queueList.length === 0) {
        state.currentSong = undefined;
        return;
      }

      const currentIndex = state?.queueList?.findIndex(
        (song) => song?.id === state?.currentSong?.id,
      );
      let nextSong = state.currentSong;
      // 2 cases occur
      if (action?.payload?.isManual) {
        // case 1: click into next btn
        if (currentIndex < state?.queueList.length - 1) {
          // if index of current song < length of queue list
          nextSong = state?.queueList[currentIndex + 1]; // next song not special case
        } else if (
          // if index of current song equal length of queue list
          state?.suggestedSongs && // if suggest turn on and have value
          state.isSuggestionEnabled &&
          state?.suggestedSongs?.length > 0 &&
          state?.queueList.length > 0
        ) {
          state.queueList = [...state.queueList, ...state.suggestedSongs]; //handle connect suggest list into queue list
          state.originalQueueList = [
            ...state.originalQueueList,
            ...state.suggestedSongs,
          ]; // if suggest turn on connect suggest list into original queue list
          state.suggestedSongs = []; // set suggest list is empty, later handle query new suggest list on another file
          nextSong = state?.queueList[currentIndex + 1]; // later assign current song to first element of suggest list
        } else {
          // if above conditions not true
          nextSong = state?.queueList[0]; // back to first element and continue play song of queue list
        }
      } else {
        // case 2: handle auto for ending audio of song
        switch (
          state.playMode // care 3 cases for case 2
        ) {
          case PlayMode.REPEAT_ONE: // case 1: repeat one of song
            nextSong = state.currentSong;
            break;
          case PlayMode.REPEAT: // case 2: repeat all of album
            if (currentIndex < state?.queueList.length - 1) {
              // if index of song < length of queue list
              nextSong = state?.queueList[currentIndex + 1]; // next song
            } else {
              // if first condition is false
              nextSong = state?.queueList[0]; // back to first song and play
            }
            break;
          default: // case 3: normal
            if (currentIndex < state?.queueList.length - 1) {
              // all this case same as comment on above
              nextSong = state?.queueList[currentIndex + 1]; // same as .....
            } else if (
              // same as .....
              state?.suggestedSongs &&
              state.isSuggestionEnabled &&
              state?.suggestedSongs?.length > 0 &&
              state?.queueList.length > 0
            ) {
              state.queueList = [...state.queueList, ...state.suggestedSongs];
              state.originalQueueList = [
                ...state.originalQueueList,
                ...state.suggestedSongs,
              ];
              state.suggestedSongs = [];
              nextSong = state?.queueList[currentIndex + 1];
            } else {
              // this case for not manual, so pause if above conditions is false
              nextSong = state?.queueList[0];
              state.paused = true;
              return;
            }
        }
      }
      if (nextSong?.id !== state.currentSong?.id) {
        state.currentSong = nextSong;
        state.paused = false; // handle play music, if function go to this line
      } else {
        if (state.playMode !== PlayMode.NORMAL) {
          PlayerUtil.instance.seek(0);
          if (PlayerUtil.instance.paused) PlayerUtil.instance.play();
          state.paused = false;
        } else {
          if (PlayerUtil.instance.paused) state.paused = true;
        }
      }
    },
    handlePrevSong: (state) => {
      if (!state?.queueList || state?.queueList.length === 0) return;

      let currentIndex = state.queueList.findIndex(
        (song) => song?.id === state?.currentSong?.id,
      );

      if (currentIndex > 0) {
        currentIndex -= 1;
      } else {
        return;
      }

      state.currentSong = state?.queueList[currentIndex];
      state.paused = false;
    },
    updateCurrentSong(state, action: PayloadAction<ISong>) {
      if (state.currentSong?.id !== action?.payload.id) {
        state.currentSong = action?.payload;
        state.paused = false;
      }
    },
    setCurrentSong(state, action: PayloadAction<ISong>) {
      if (state.currentSong?.id !== action?.payload.id) {
        state.currentSong = action?.payload;
        state.paused = true;
      }
    },
    toggleLyrics(state) {
      state.isOpenLyrics = !state.isOpenLyrics;
    },
    togglePlayMusic: (state) => {
      state.paused = !state.paused;
    },
    pauseMusic: (state) => {
      state.paused = true;
    },
    playMusic: (state) => {
      state.paused = false;
    },
    toggleSuggest: (state, action: PayloadAction<boolean>) => {
      state.isSuggestionEnabled = action?.payload;

      if (action?.payload) {
        state.playMode = PlayMode.NORMAL;
      }
    },
    shuffleQueue: (state) => {
      if (!state?.queueList || state?.queueList.length <= 1) return;

      const currentIndex = state?.queueList.findIndex(
        (song) => song?.id === state.currentSong?.id,
      );
      const currentSong = state?.queueList[currentIndex];
      state.originalQueueList = [...state.queueList];
      const shuffledSongs = [...state.queueList];
      shuffledSongs?.splice(currentIndex, 1);

      for (let i = shuffledSongs?.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [shuffledSongs[i], shuffledSongs[j]] = [
          shuffledSongs[j],
          shuffledSongs[i],
        ];
      }

      state.queueList = [currentSong, ...shuffledSongs];
      state.isShuffle = true;
    },
    unShuffleQueue: (state) => {
      if (!state?.originalQueueList || state?.originalQueueList.length === 0)
        return;

      const index = state?.originalQueueList.findIndex(
        (song) => song?.id === state?.currentSong?.id,
      );

      if (index !== -1) {
        state.queueList = [...state.originalQueueList];
      }

      state.isShuffle = false;
    },
    setSuggestList: (state, action: PayloadAction<ISong[] | undefined>) => {
      if (!action?.payload) return;

      state.suggestedSongs = action?.payload;
    },
    removeSongFromQueue: (state, action: PayloadAction<string>) => {
      const songId = action?.payload;
      const currentIndex = state.queueList?.findIndex(
        (song) => song?.id === state?.currentSong?.id,
      );
      state.suggestedSongs = state?.suggestedSongs?.filter(
        (song) => song?.id !== songId,
      );
      const indexToRemove = state?.queueList?.findIndex(
        (song) => song.id === songId,
      );
      if (indexToRemove === -1) return;

      const isRemovingCurrentSong = currentIndex === indexToRemove;

      state.queueList = state?.queueList?.filter((song) => song.id !== songId);
      state.originalQueueList = state?.originalQueueList?.filter(
        (song) => song?.id !== songId,
      );

      if (isRemovingCurrentSong) {
        if (state?.queueList.length > 0) {
          if (currentIndex < state?.queueList.length) {
            state.currentSong = state?.queueList[currentIndex];
          } else if (currentIndex > 0) {
            state.currentSong = state?.queueList[currentIndex - 1];
          } else if (
            state.isSuggestionEnabled &&
            Boolean(state?.suggestedSongs?.length) &&
            state?.suggestedSongs
          ) {
            state.queueList = [...state.suggestedSongs];
            state.originalQueueList = [...state.suggestedSongs];
            state.suggestedSongs = [];
            state.currentSong = state?.queueList[0];
          } else {
            state.currentSong = undefined;
          }
        } else {
          if (
            state.isSuggestionEnabled &&
            Boolean(state?.suggestedSongs?.length) &&
            state?.suggestedSongs
          ) {
            state.queueList = [...state.suggestedSongs];
            state.suggestedSongs = [];
            state.currentSong = state?.queueList[0];
          } else {
            state.currentSong = undefined;
          }
        }
      } else {
        if (currentIndex > indexToRemove) {
          state.currentSong = state?.queueList[currentIndex - 1];
        }
      }
    },
    changePlayerType: (state, action: PayloadAction<EPlayerType>) => {
      state.playerType = action?.payload;
    },
  },
});

export const {
  updatePlayMode,
  updateCurrentSong,
  setCurrentSong,
  toggleLyrics,
  togglePlayMusic,
  pauseMusic,
  playMusic,
  handleNextSong,
  handlePrevSong,
  addSongsToQueue,
  toggleSuggest,
  shuffleQueue,
  unShuffleQueue,
  setSuggestList,
  removeSongFromQueue,
  playSingleSong,
  playSongFromList,
  changePlayerType,
} = PlayerSlice.actions;

export default PlayerSlice.reducer;
