import {IPlaylist, PlaylistType} from "src/types";
import {fetcher, fetcherWithMetadata, IDataWithMeta} from "./Fetcher";

const path = {
  playlist: "/cms/playlist",
  album: "/cms/playlist/album",
  top100: "/cms/playlist/top100",
};

interface IListPlaylistParams {
  keyword?: string;
  themeId?: string;
  genreId?: string;
  updatedAt?: string;
  page?: number;
  pageSize?: number;
  type?: PlaylistType;
  order?: string;
  direction?: string;
  fromDate?: string;
  toDate?: string;
}

export interface IGetPlaylistSongsParam {
  themeId?: string;
  genreId?: string;
  updatedAt?: string;
  keyword?: string;
  fromDate?: string;
  toDate?: string;
  order?: string;
  direction?: string;
  page: number;
  pageSize: number;
}

export interface IValue {
  id: string;
  label: string;
}

export interface ICreatePlaylist {
  name?: string;
  image?: File;
  songIds?: IValue[];
  themeIds?: IValue[];
  genreIds?: IValue[];
  releaseDate?: string;
  description?: string;
  isPublic?: boolean;
}

function getListPlaylist(
  params: IListPlaylistParams,
): Promise<IDataWithMeta<IPlaylist[]>> {
  return fetcherWithMetadata(
    {
      url: path.playlist,
      method: "get",
      params,
    },
    {
      displayError: false,
    },
  );
}

function getPlaylistSongs(
  id: string,
  params?: IGetPlaylistSongsParam,
): Promise<IDataWithMeta<[]>> {
  return fetcherWithMetadata(
    {
      url: `${path.playlist}/${id}/songs`,
      method: "get",
      params,
    },
    {
      displayError: false,
    },
  );
}

function detailPlaylist(id: string): Promise<IPlaylist> {
  return fetcher(
    {
      url: `${path.playlist}/${id}`,
      method: "get",
    },
    {
      displayError: false,
    },
  );
}

function deletePlaylist(id: string) {
  return fetcher({
    url: `${path.playlist}/${id}`,
    method: "delete",
  });
}

function createPlaylist(body: ICreatePlaylist): Promise<void> {
  return fetcher(
    {
      url: `${path.playlist}`,
      method: "post",
      data: body,
    },
    {
      displayError: false,
      isFormData: true,
    },
  );
}

function updatePlaylist({
  id,
  ...restData
}: ICreatePlaylist & {id: string}): Promise<void> {
  return fetcher(
    {
      url: `${path.playlist}/${id}`,
      method: "patch",
      data: restData,
    },
    {
      displayError: true,
      isFormData: true,
    },
  );
}

function createAlbum(body: ICreatePlaylist): Promise<void> {
  return fetcher(
    {
      url: `${path.playlist}/album`,
      method: "post",
      data: body,
    },
    {
      displayError: false,
      isFormData: true,
    },
  );
}

function createTop100(body: ICreatePlaylist): Promise<void> {
  return fetcher(
    {
      url: path.top100,
      method: "post",
      data: body,
    },
    {
      displayError: false,
      isFormData: true,
    },
  );
}

function updateTop100({
  id,
  ...restData
}: ICreatePlaylist & {id: string}): Promise<void> {
  return fetcher(
    {
      url: `${path.top100}/${id}`,
      method: "patch",
      data: restData,
    },
    {
      displayError: true,
      isFormData: true,
    },
  );
}

export default {
  getListPlaylist,
  detailPlaylist,
  createPlaylist,
  getPlaylistSongs,
  updatePlaylist,
  createTop100,
  updateTop100,
  deletePlaylist,
  createAlbum,
};
