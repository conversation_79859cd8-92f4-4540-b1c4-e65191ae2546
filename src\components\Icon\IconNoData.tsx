import {SVGProps} from "react";

function IconNoData({
  width = "218",
  height = "218",
  ...props
}: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox={`0 0 ${width} ${height}`}
      className={props.className}
      fill="none"
      {...props}
    >
      <path
        d="M163.589 163.232C193.639 133.181 193.639 84.4606 163.589 54.4105C133.539 24.3604 84.818 24.3604 54.7679 54.4105C24.7178 84.4606 24.7178 133.181 54.7679 163.232C84.818 193.282 133.539 193.282 163.589 163.232Z"
        fill="white"
        fillOpacity="0.1"
      />
      <path
        d="M47.6179 53.2357C49.0637 53.2357 50.2357 52.0637 50.2357 50.6179C50.2357 49.1721 49.0637 48 47.6179 48C46.1721 48 45 49.1721 45 50.6179C45 52.0637 46.1721 53.2357 47.6179 53.2357Z"
        fill="white"
        fillOpacity="0.48"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M124.212 141.036C124.212 142.482 123.04 143.654 121.594 143.654C120.149 143.654 118.977 142.482 118.977 141.036C118.977 139.59 120.149 138.418 121.594 138.418C123.04 138.417 124.212 139.59 124.212 141.036Z"
        fill="white"
      />
      <path
        d="M192.618 104.236C194.064 104.236 195.236 103.064 195.236 101.618C195.236 100.172 194.064 99 192.618 99C191.172 99 190 100.172 190 101.618C190 103.064 191.172 104.236 192.618 104.236Z"
        fill="white"
        fillOpacity="0.67"
      />
      <path
        d="M121.764 110.089L117.92 113.198C117.061 113.893 116.928 115.152 117.623 116.011L120.732 119.854C121.426 120.713 122.686 120.846 123.544 120.151L127.388 117.042C128.247 116.347 128.38 115.088 127.685 114.229L124.577 110.386C123.882 109.527 122.623 109.394 121.764 110.089Z"
        fill="white"
        fillOpacity="0.53"
      />
      <path
        d="M47.1721 130.128L43.3284 133.237C42.4696 133.931 42.3366 135.191 43.0312 136.05L46.1397 139.893C46.8344 140.751 48.0937 140.884 48.9525 140.19L52.7962 137.081C53.655 136.386 53.788 135.127 53.0934 134.268L49.9849 130.425C49.2902 129.566 48.0309 129.433 47.1721 130.128Z"
        fill="white"
        fillOpacity="0.21"
      />
      <path
        d="M141.204 42.9638L119.723 50.5241C118.865 50.8261 118.415 51.7662 118.717 52.624C119.018 53.4818 119.959 53.9324 120.816 53.6305L142.297 46.0702C143.155 45.7683 143.606 44.8282 143.304 43.9704C143.002 43.1126 142.062 42.6619 141.204 42.9638Z"
        fill="white"
        fillOpacity="0.53"
      />
      <path
        d="M131.834 25.4245L123.275 37.7479C122.757 38.495 122.942 39.5212 123.689 40.04C124.436 40.5588 125.462 40.3738 125.981 39.6268L134.539 27.3034C135.058 26.5563 134.873 25.5301 134.126 25.0113C133.379 24.4925 132.353 24.6775 131.834 25.4245Z"
        fill="white"
        fillOpacity="0.53"
      />
      <path
        d="M136.298 158.037L122.716 176.316C122.174 177.046 122.326 178.077 123.056 178.62C123.786 179.162 124.817 179.01 125.36 178.28L138.941 160.001C139.483 159.271 139.331 158.24 138.601 157.697C137.871 157.155 136.84 157.307 136.298 158.037Z"
        fill="white"
        fillOpacity="0.13"
      />
      <path
        d="M123.003 157.66L108.697 162.185C107.83 162.459 107.349 163.385 107.624 164.252C107.898 165.119 108.823 165.6 109.691 165.326L123.996 160.8C124.863 160.526 125.344 159.6 125.07 158.733C124.795 157.866 123.87 157.385 123.003 157.66Z"
        fill="white"
        fillOpacity="0.13"
      />
      <path
        d="M158.349 86.359C161.023 93.0899 157.722 100.744 150.991 103.419C144.261 106.094 136.607 102.794 133.931 96.0618C131.255 89.3309 134.557 81.6771 141.287 79.0025C148.019 76.3271 155.672 79.6266 158.349 86.359ZM137.357 94.7009C139.281 99.5434 144.787 101.918 149.629 99.9925C154.472 98.0691 156.845 92.5625 154.921 87.7207C152.996 82.8774 147.491 80.5032 142.649 82.4283C137.806 84.3533 135.433 89.8583 137.357 94.7009Z"
        fill="white"
        fillOpacity="0.43"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M84.5912 176.719C86.4699 176.719 87.993 178.243 87.993 180.121C87.993 182 86.4699 183.523 84.5912 183.523C82.7126 183.523 81.1895 182 81.1895 180.121C81.1895 178.243 82.7118 176.719 84.5912 176.719Z"
        fill="white"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M61.6294 72.2159L62.2596 74.9812C62.4306 75.7316 61.7253 76.3858 60.9898 76.159L58.2787 75.3228L55.5692 74.4869C54.8336 74.26 54.6196 73.3218 55.1839 72.7982L57.2631 70.8694L59.3427 68.9401C59.907 68.4166 60.8263 68.7001 60.9977 69.4505L61.6294 72.2159Z"
        fill="white"
      />
      <path
        d="M107.784 48.8527V34.5272C107.784 31.537 105.048 29.2955 102.116 29.8844L75.3185 35.268C73.1066 35.7123 71.5156 37.6549 71.5156 39.9108V90.4469C67.9212 86.7206 62.6823 84.5926 56.9575 85.2727C49.3229 86.1796 43.0691 92.2344 41.9584 99.8419C40.2786 111.346 49.9318 121.1 61.4069 119.598C70.0604 118.465 76.3725 110.806 76.3725 102.079C76.3725 90.6882 76.3725 71.1869 76.3725 62.9369C76.3725 60.6827 77.9612 58.7496 80.1707 58.3029L103.986 53.4931C106.196 53.048 107.784 51.1062 107.784 48.8527Z"
        fill="#4D4644"
      />
      <path
        d="M172.635 58.4884C172.635 54.5328 169.029 51.5578 165.146 52.3113L102.845 64.3906C99.8865 64.9643 97.7505 67.5542 97.7505 70.5668V130.463C93.8005 126.368 88.0949 123.978 81.8347 124.549C72.2535 125.422 64.6117 133.394 64.113 143.002C63.5225 154.388 72.6634 163.788 83.9579 163.623C94.574 163.469 103.246 154.131 103.246 143.514V96.6384C103.246 93.6249 105.382 91.0351 108.34 90.4621L159.641 80.5157C163.524 79.7629 167.13 82.7372 167.13 86.6919V120.012C163.174 115.945 157.475 113.583 151.231 114.164C141.692 115.052 134.082 122.978 133.553 132.544C132.928 143.863 141.92 153.237 153.103 153.237C163.513 153.237 172.001 145.107 172.622 134.854L172.635 58.4884Z"
        fill="#615654"
      />
    </svg>
  );
}

export default IconNoData;
