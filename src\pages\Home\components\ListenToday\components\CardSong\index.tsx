import CustomTooltip from "@components/CustomTooltip";
import IconPlay from "@components/Icon/IconPlay";
import {playSingleSong} from "@redux/slices/PlayerSlice";
import {IRootState} from "@redux/store";
import {useState} from "react";
import {useDispatch, useSelector} from "react-redux";
import PlayerUtil from "src/core/PlayerUtil";
import {ISong} from "src/types";

interface SongCardProps {
  data: ISong;
  index: number;
}

export default function SongCard({data}: SongCardProps) {
  const dispatch = useDispatch();
  const [hover, setHover] = useState(false);
  const currentSong = useSelector(
    (state: IRootState) => state.player.currentSong,
  );
  const isPaused = useSelector((state: IRootState) => state.player.paused);

  const isPlaying = currentSong?.id === data?.id && !isPaused;

  const handlePlayPause = () => {
    if (currentSong?.id === data?.id) {
      if (isPaused) {
        PlayerUtil.instance.play();
      } else {
        PlayerUtil.instance.pause();
      }
    } else {
      dispatch(playSingleSong(data));
    }
  };

  return (
    <CustomTooltip
      title={
        <div className="relative flex flex-col justify-start mt-2">
          <span className="text-base font-medium text-white line-clamp-1">
            {data?.name}
          </span>
          <span className="text-sm font-normal text-[#FFFFFF80] line-clamp-1">
            {data?.artists
              ?.map((artist) => artist?.stageName ?? artist?.name)
              .slice(0, 2)
              .join(", ")}
          </span>
        </div>
      }
      placement="bottom"
    >
      <div
        className="rounded-lg hover:bg-[#ffffff0f] p-3 w-full cursor-pointer select-none group"
        onMouseEnter={() => setHover(true)}
        onMouseLeave={() => setHover(false)}
        onClick={handlePlayPause}
      >
        <div className="relative overflow-hidden rounded-lg">
          <img
            src={
              data?.images?.DEFAULT ||
              data?.images?.SMALL ||
              "./image/default-music.png"
            }
            alt={data?.name}
            className="object-cover w-full aspect-square transform transition-transform duration-700 ease-in-out group-hover:scale-110"
          />
          {(hover || currentSong?.id === data?.id) && (
            <div className="absolute flex top-0 left-0 w-full h-full justify-center items-center">
              {isPlaying ? (
                <img
                  src="/image/animation_play.gif"
                  style={{filter: "grayscale(100%) brightness(0) invert(1)"}}
                  alt="Playing"
                />
              ) : (
                <IconPlay className="h-9 w-9" />
              )}
            </div>
          )}
        </div>
      </div>
    </CustomTooltip>
  );
}
