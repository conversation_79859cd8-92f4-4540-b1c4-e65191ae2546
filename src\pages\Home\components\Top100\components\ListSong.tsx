import ApiHome from "@api/ApiHome";
import QUERY_KEY from "@api/QueryKey";
import AlbumCardSkeleton from "@components/AlbumCardSkeleton";
import CommonAlbumCard from "@components/CommonAlbumCard";
import Slider from "@components/Slider";
import {useQuery} from "@tanstack/react-query";
import {IParamsDefault} from "src/types";
import {SwiperSlide} from "swiper/react";

interface IListSongProps {
  params: IParamsDefault;
}

export default function ListSong({params}: IListSongProps) {
  const mainParams: IParamsDefault = {
    page: 0,
    pageSize: 10,
    ...params,
  };

  const {
    data: dataGetTop100,
    isLoading,
    isFetching,
    isError,
  } = useQuery({
    queryKey: [QUERY_KEY.ALBUM.GET_TOP_100, mainParams],
    queryFn: () => ApiHome.getTop100(mainParams),
    // staleTime: 5 * 60 * 1000,
  });

  if (isLoading || isFetching || isError) {
    return (
      <div className="flex flex-col gap-2 sm:gap-3 md:gap-4 lg:gap-5">
        <Slider slidesPerView={5.5}>
          {[...Array(7)].map((_, index) => (
            <SwiperSlide key={index} virtualIndex={index}>
              <AlbumCardSkeleton isMultipleInfo={false} />
            </SwiperSlide>
          ))}
        </Slider>
      </div>
    );
  }
  return (
    <div className="flex flex-col gap-2 sm:gap-3 md:gap-4 lg:gap-5">
      <Slider slidesPerView={5.5} spaceBetween={16}>
        {dataGetTop100?.data?.map((item, index) => (
          <SwiperSlide key={`top_100_${item.id}`} virtualIndex={index}>
            <CommonAlbumCard data={item} haveLayer={false} />
          </SwiperSlide>
        ))}
      </Slider>
    </div>
  );
}
