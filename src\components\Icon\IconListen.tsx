import {SVGProps} from "react";

const IconListen = ({
  height = 20,
  width = 20,
  ...restProps
}: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={width}
    height={height}
    viewBox="0 0 16 16"
    fill="none"
    {...restProps}
  >
    <path
      stroke="#fff"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={1.4}
      d="M13.625 11.125V8a5.625 5.625 0 1 0-11.25 0v3.125"
    />
    <path
      stroke="#fff"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={1.4}
      d="M10.5 9.849c0-.32 0-.48.037-.606a.827.827 0 0 1 .581-.584c.795-.199.956.54 1.527.77l.043.017c.62.261.936.924.934 1.597v.223a1.595 1.595 0 0 1-.845 1.42c-.587.303-.783 1.095-1.635.912a.82.82 0 0 1-.593-.552c-.05-.144-.05-.333-.05-.71V9.85Zm-5 2.552c0 .32 0 .48-.037.607a.827.827 0 0 1-.581.583c-.795.2-.956-.54-1.527-.77a4.898 4.898 0 0 1-.039-.015c-.624-.261-.94-.925-.939-1.598v-.228a1.59 1.59 0 0 1 .846-1.416c.587-.303.783-1.095 1.634-.912a.82.82 0 0 1 .594.552c.049.144.049.333.049.71V12.4Z"
    />
  </svg>
);
export default IconListen;
