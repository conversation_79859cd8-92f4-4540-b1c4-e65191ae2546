import {IArtist, ISong, IPlaylist} from "src/types";
import {fetcher, fetcherWithMetadata, IDataWithMeta} from "./Fetcher";

export interface ISearchParams {
  page: number;
  pageSize: number;
  keyword?: string;
  order?: string;
  direction?: string;
  type?: number;
}

interface ISearchSuggestionsRepsonse {
  searchLogs: {keyword: string; type: number}[];
  artists: IArtist[];
  songs: ISong[];
}

const path = {
  searchRecommended: "search/recommended",
  searchSong: "search/songs",
  searchArtists: "search/artists",
  searchPlaylists: "search/playlists",
  searchYoutube: "search/youtube",
  searchYoutubePlaylist: "search/youtube/playlist",
  searchSuggestions: "/search/suggestions",
  searchSaveHistory: "search/save-history",
};

function searchRecommend(
  params: ISearchParams,
): Promise<IDataWithMeta<{data: string[]}>> {
  return fetcherWithMetadata(
    {
      url: path.searchRecommended,
      method: "get",
      params,
    },
    {
      displayError: false,
    },
  );
}

function searchSong(params: ISearchParams): Promise<IDataWithMeta<ISong[]>> {
  return fetcherWithMetadata(
    {
      url: path.searchSong,
      method: "get",
      params,
    },
    {
      displayError: false,
    },
  );
}

function searchArtists(
  params: ISearchParams,
): Promise<IDataWithMeta<IArtist[]>> {
  return fetcherWithMetadata(
    {
      url: path.searchArtists,
      method: "get",
      params,
    },
    {
      displayError: false,
    },
  );
}

function searchPlaylists(
  params: ISearchParams,
): Promise<IDataWithMeta<IPlaylist[]>> {
  return fetcherWithMetadata(
    {
      url: path.searchPlaylists,
      method: "get",
      params,
    },
    {
      displayError: false,
    },
  );
}

function searchYoutube(params: ISearchParams): Promise<IDataWithMeta<ISong[]>> {
  return fetcherWithMetadata(
    {
      url: path.searchYoutube,
      method: "get",
      params,
    },
    {
      displayError: false,
    },
  );
}

function searchYoutubePlaylist(
  params: ISearchParams,
): Promise<IDataWithMeta<IPlaylist[]>> {
  return fetcherWithMetadata(
    {
      url: path.searchYoutubePlaylist,
      method: "get",
      params,
    },
    {
      displayError: false,
    },
  );
}

function searchSuggestions(params: Partial<ISearchParams>) {
  return fetcher<ISearchSuggestionsRepsonse>({
    url: path.searchSuggestions,
    params: params,
    paramsSerializer: (params) => {
      return Object.entries(params)
        .map(([key, value]) => `${key}=${value}`)
        .join("&");
    },
  });
}

function saveSearchSuggestion(keyword: string) {
  return fetcher({
    url: path.searchSaveHistory,
    method: "post",
    params: {
      keyword: keyword,
    },
  });
}

export default {
  searchRecommend,
  searchSong,
  searchArtists,
  searchPlaylists,
  searchYoutube,
  searchYoutubePlaylist,
  searchSuggestions,
  saveSearchSuggestion,
};
