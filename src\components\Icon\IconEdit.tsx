import {SVGProps} from "react";

function IconEdit({
  width = "12",
  height = "13",
  ...props
}: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox={`0 0 ${width} ${height}`}
      fill="none"
      {...props}
    >
      <path
        stroke={props.stroke || "#ffffff"}
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeMiterlimit="10"
        d="M6.7 1.333 2.138 6.161a1.694 1.694 0 0 0-.372.795l-.205 1.8c-.073.65.394 1.094 1.039.983l1.788-.306c.25-.044.6-.227.773-.416l4.56-4.828c.79-.833 1.145-1.783-.083-2.945C8.416.094 7.488.5 6.7 1.334Z"
      />
      <path
        stroke={props.stroke || "#ffffff"}
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeMiterlimit="10"
        d="M5.939 2.139A3.403 3.403 0 0 0 8.967 5M1 11.556h10"
      />
    </svg>
  );
}

export default IconEdit;
