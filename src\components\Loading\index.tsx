import {Box, CircularProgress, Typography} from "@mui/material";
import {styled} from "@mui/system";

interface IProps {
  loading: boolean;
  title?: string;
  color?: string;
}

const Overlay = styled(Box)({
  position: "fixed",
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  backgroundColor: "rgba(0, 0, 0, 0.5)",
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  zIndex: 1300,
});

const BoxContainer = styled(Box)(({theme}) => ({
  display: "flex",
  flexDirection: "column",
  alignItems: "center",
  justifyContent: "center",
  //   backgroundColor: theme.palette.background.paper,
  borderRadius: "12px",
  padding: theme.spacing(3),
  minWidth: "100px",
  minHeight: "100px",
  maxWidth: "66.67vw",
}));

export default function Loading({loading, title, color}: IProps) {
  if (!loading) return null;

  return (
    <Overlay>
      <BoxContainer>
        <CircularProgress size={40} sx={{color: color || "red"}} />
        {title && (
          <Typography
            sx={{
              marginTop: 2,
              paddingX: 2,
              textAlign: "center",
              fontSize: "14px",
              color: color || "red",
              fontWeight: 500,
            }}
          >
            {title}
          </Typography>
        )}
      </BoxContainer>
    </Overlay>
  );
}
