import {useState} from "react";
import {<PERSON><PERSON><PERSON>, Pie, Sector, Cell, Legend} from "recharts";
import ICDevice from "@components/Icon/ICDevice";
import "./index.scss";

const RADIAN = Math.PI / 180;

const renderCustomizedLabel = (props: any) => {
  const {cx, cy, midAngle, innerRadius, outerRadius, percent} = props;
  const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
  const x = cx + radius * Math.cos(-midAngle * RADIAN);
  const y = cy + radius * Math.sin(-midAngle * RADIAN);

  return (
    <text
      x={x}
      y={y}
      fill="#fff"
      textAnchor="middle"
      dominantBaseline="central"
      fontSize={14}
      fontWeight="bold"
    >
      {`${(percent * 100).toFixed(0)}%`}
    </text>
  );
};

const renderActiveShape = (props: any) => {
  const {cx, cy, innerRadius, outerRadius, startAngle, endAngle, fill} = props;

  return (
    <g>
      <Sector
        cx={cx}
        cy={cy}
        innerRadius={innerRadius}
        outerRadius={outerRadius + 10}
        startAngle={startAngle}
        endAngle={endAngle}
        fill={fill}
        style={{
          transition: "all 0.3s ease-in-out",
        }}
      />
    </g>
  );
};

interface CustomPieChartProps {
  data: {name: string; value: number}[];
  colors: string[];
}

function CustomPieChart({data, colors}: CustomPieChartProps) {
  const [activeIndex, setActiveIndex] = useState<number>(0);

  const onPieEnter = (_: any, index: number) => {
    setActiveIndex(index);
  };

  return (
    <div className="w-full relative flex justify-end items-center">
      <Legend
        layout="vertical"
        align="left"
        payload={data.map((item, index) => ({
          id: item.name,
          type: "circle",
          value: item.name,
          color: colors[index],
        }))}
      />
      <div style={{width: 300, height: 300, position: "relative"}}>
        <PieChart width={300} height={300}>
          <Pie
            data={data}
            dataKey="value"
            cx="50%"
            cy="50%"
            innerRadius={70}
            outerRadius={105}
            labelLine={false}
            label={renderCustomizedLabel}
            activeIndex={activeIndex}
            activeShape={renderActiveShape}
            onMouseEnter={onPieEnter}
          >
            {data.map((entry, i) => (
              <Cell key={`cell-${i}`} fill={colors[i]} />
            ))}
          </Pie>
        </PieChart>
        <ICDevice
          style={{
            position: "absolute",
            top: "50%",
            left: "50%",
            width: 50,
            height: 50,
            transform: "translate(-50%, -50%)",
          }}
        />
      </div>
    </div>
  );
}

export default CustomPieChart;
