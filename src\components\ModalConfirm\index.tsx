import CloseIcon from "@mui/icons-material/Close";
import {Dialog, DialogContent, DialogTitle, IconButton} from "@mui/material";
import React from "react";
import {useTranslation} from "react-i18next";

interface ConfirmModalProps {
  open: boolean;
  title?: string;
  children?: React.ReactNode;
  onConfirm: () => void;
  onCancel: () => void;
  loading?: boolean;
}

export default function ModalComfirm({
  open,
  title,
  children,
  onConfirm,
  onCancel,
  loading,
}: ConfirmModalProps) {
  const {t} = useTranslation();
  return (
    <Dialog
      open={open}
      onClose={onCancel}
      maxWidth="sm"
      fullWidth
      classes={{paper: "rounded-lg"}}
    >
      <div className="flex items-center justify-between border-b border-gray-300 pr-4">
        <DialogTitle className="p-0 text-base !font-bold text-gray-800">
          {title ?? t("common.confirm")}
        </DialogTitle>
        <IconButton
          aria-label="close"
          onClick={onCancel}
          className="text-gray-500 hover:text-gray-800"
          size="small"
        >
          <CloseIcon />
        </IconButton>
      </div>

      <DialogContent className="px-6 py-4">{children}</DialogContent>

      <div className="flex justify-end space-x-2 border-t border-gray-300 px-4 py-2">
        <div
          onClick={onCancel}
          className="rounded-lg cursor-pointer select-none px-4 py-2 text-base text-gray-600 hover:bg-gray-300"
        >
          {t("common.cancel")}
        </div>
        <IconButton
          onClick={onConfirm}
          loading={loading}
          disabled={loading}
          className={`!rounded-lg !px-4 !text-base !text-white w-[102px]
            ${loading ? "!bg-gray-400 cursor-not-allowed" : "!bg-orange-500 hover:bg-red-600"}
          `}
        >
          {!loading && t("common.confirm")}
        </IconButton>
      </div>
    </Dialog>
  );
}
