import ImageCropper from "@components/ImageCropper";
import CloseIcon from "@mui/icons-material/Close";
import {
  CircularProgress,
  Dialog,
  DialogContent,
  DialogTitle,
  IconButton,
  MenuItem,
  Select,
} from "@mui/material";
import {useState} from "react";
import {Form, Formik} from "formik";
import * as Yup from "yup";
import {useTranslation} from "react-i18next";
import {useMutation, useQuery} from "@tanstack/react-query";
import ApiArtist from "@api/ApiArtist";
import {toast} from "react-toastify";
import {
  useArtistTypeOptions,
  useCountryOptions,
  useGenderOptions,
} from "src/utils/global";
import {LocalizationProvider} from "@mui/x-date-pickers/LocalizationProvider/LocalizationProvider";
import {AdapterDayjs} from "@mui/x-date-pickers/AdapterDayjs";
import {DatePicker} from "@mui/x-date-pickers/DatePicker";
import dayjs from "dayjs";
import {CountryEnum, EArtistType, GenderEnum} from "src/types";
import TextFieldCustomer from "@components/TextFieldCustomer";

interface ModalEditArtistProps {
  artistId: string | null;
  open: boolean;
  onClose: () => void;
  refetchList: () => void;
}

export default function ModalEditArtist({
  open,
  onClose,
  artistId,
  refetchList,
}: ModalEditArtistProps) {
  const {t} = useTranslation();
  const [loading, setLoading] = useState(false);
  const genderOptions = useGenderOptions();
  const countryOptions = useCountryOptions();
  const artistTypeOptions = useArtistTypeOptions();

  const {data: dataArtist, isFetching} = useQuery({
    queryKey: ["artistDetail", artistId],
    queryFn: () => (artistId ? ApiArtist.getArtist(artistId) : null),
    enabled: !!artistId,
  });

  const validationSchema = Yup.object({
    image: Yup.mixed()
      .required(t("validation.field_is_require", {field: t("common.cover")}))
      .test(
        "fileSize",
        t("validation.file_size_limit_exceed", {size: "10MB"}),
        (file) => {
          if (typeof file === "string") return true;
          return file && (file as File).size <= 10 * 1024 * 1024;
        },
      ),
    name: Yup.string().required(
      t("validation.field_is_require", {field: t("cms.artist.artist_name")}),
    ),
    gender: Yup.string().required(
      t("validation.field_is_require", {field: t("common.gender")}),
    ),
    country: Yup.string().required(
      t("validation.field_is_require", {field: t("common.country")}),
    ),
  });

  const initialValues = {
    image: dataArtist?.images?.DEFAULT ?? undefined,
    name: dataArtist?.name ?? "-",
    stageName: dataArtist?.stageName ?? "-",
    gender: dataArtist?.user?.gender ?? "",
    dateOfBirth: dataArtist?.user?.dateOfBirth ?? "-",
    company: dataArtist?.company ?? "-",
    type: dataArtist?.type ?? "",
    country: dataArtist?.user?.countryName ?? "",
    biography: dataArtist?.biography ?? "-",
  };

  const {mutateAsync: updateArtistMutation} = useMutation({
    mutationFn: ApiArtist.updateArtist,
    onSuccess: () => {
      toast.success(t("common.update_successfully"));
      refetchList();
      onClose();
    },
  });

  const handleSubmit = async (values: typeof initialValues) => {
    try {
      setLoading(true);
      const payload: any = {
        id: artistId!,
        ...values,
        dateOfBirth:
          values.dateOfBirth && dayjs(values.dateOfBirth).isValid()
            ? dayjs(values.dateOfBirth).format("YYYY-MM-DD")
            : "",
        gender: values.gender as unknown as GenderEnum,
        type:
          values.type != null
            ? (Number(values.type) as EArtistType)
            : undefined,
      };

      if (typeof values.image === "string") {
        delete payload.image;
      }

      await updateArtistMutation(payload);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      classes={{paper: "rounded-lg"}}
    >
      <div className="flex items-center justify-between border-b border-gray-300 pr-4">
        <DialogTitle className="p-0 !text-base !font-bold text-gray-800">
          {t("cms.artist.edit_basic_information")}
        </DialogTitle>
        <IconButton
          aria-label="close"
          onClick={onClose}
          className="text-gray-500 hover:text-gray-800"
          size="small"
        >
          <CloseIcon />
        </IconButton>
      </div>

      <DialogContent className="!pb-0">
        {isFetching && (
          <div className="flex items-center justify-center w-full h-[293px]">
            <CircularProgress sx={{color: "blue"}} />
          </div>
        )}
        {!isFetching && dataArtist && (
          <Formik
            enableReinitialize={true}
            initialValues={initialValues}
            validationSchema={validationSchema}
            onSubmit={handleSubmit}
          >
            {({
              values,
              errors,
              touched,
              handleChange,
              handleBlur,
              setFieldValue,
            }) => (
              <Form className="flex flex-col gap-4">
                <div className="flex flex-col gap-[10px]">
                  <label className="text-sm font-bold text-gray-700">
                    {t("common.cover_img")}
                    <span className="text-red-600"> *</span>
                  </label>
                  <div>
                    <div className="flex items-center gap-4">
                      <ImageCropper
                        onChange={(file) => {
                          if (file) {
                            setFieldValue("image", file);
                          }
                        }}
                        initialImageUrl={
                          dataArtist?.images?.DEFAULT ?? undefined
                        }
                        className="h-[120px] w-[120px]  object-cover"
                      />
                    </div>
                    {errors.image && touched.image && (
                      <div className="text-red-500 text-sm">{errors.image}</div>
                    )}
                  </div>
                </div>

                <TextFieldCustomer
                  name="name"
                  required
                  value={values.name}
                  onChange={handleChange("name")}
                  label={t("cms.artist.artist_name")}
                  placeholder={t("common.full_name")}
                />

                <TextFieldCustomer
                  name="stageName"
                  value={values.stageName}
                  onChange={handleChange("stageName")}
                  label={t("cms.artist.stage_name")}
                  placeholder={t("cms.artist.stage_name")}
                />

                <div className="flex gap-4 w-full">
                  <div className="flex flex-col gap-2 w-1/2">
                    <label className="text-sm font-bold text-gray-700">
                      {t("common.birthday")}
                    </label>
                    <LocalizationProvider dateAdapter={AdapterDayjs}>
                      <DatePicker
                        name="dateOfBirth"
                        disableFuture
                        value={
                          values.dateOfBirth ? dayjs(values.dateOfBirth) : null
                        }
                        onChange={(event) => {
                          setFieldValue("dateOfBirth", event);
                        }}
                        slotProps={{
                          textField: {
                            size: "small",
                            placeholder: t("common.birthday"),
                            error: false,
                            helperText: "",
                          },
                          field: {
                            clearable: true,
                          },
                        }}
                      />
                    </LocalizationProvider>
                  </div>
                  <div className="flex flex-col gap-2 w-1/2">
                    <label className="text-sm font-bold text-gray-700">
                      {t("common.gender")}
                      <span className="text-red-600"> *</span>
                    </label>
                    <div>
                      <Select
                        name="gender"
                        value={values.gender}
                        onChange={(event) => {
                          setFieldValue("gender", Number(event.target.value));
                        }}
                        onBlur={handleBlur}
                        size="small"
                        displayEmpty
                        className="rounded text-sm w-full"
                        renderValue={(selected) => {
                          if (selected !== "") {
                            return (
                              <span className="text-sm">
                                {genderOptions.find(
                                  (option) =>
                                    option.value ===
                                    (selected as unknown as GenderEnum),
                                )?.label || selected}
                              </span>
                            );
                          }
                          return (
                            <span className="text-[#BFBFBF] font-normal text-sm">
                              {t("common.select_gender")}
                            </span>
                          );
                        }}
                      >
                        {genderOptions.map((gender) => (
                          <MenuItem key={gender.value} value={gender.value}>
                            {gender.label}
                          </MenuItem>
                        ))}
                      </Select>
                      {errors.gender && touched.gender && (
                        <div className="text-red-500 text-sm">
                          {errors.gender}
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                <div className="flex flex-col gap-[10px]">
                  <label className="text-sm font-bold text-gray-700">
                    {t("cms.artist.artist_type")}
                  </label>
                  <Select
                    name="type"
                    value={values.type}
                    onChange={(event) => {
                      setFieldValue("type", Number(event.target.value));
                    }}
                    displayEmpty
                    size="small"
                    className="w-full text-sm"
                    renderValue={(selected) => {
                      if (
                        selected === "" ||
                        selected === undefined ||
                        selected === null
                      ) {
                        return (
                          <span className="text-[#acb1bc] font-normal text-sm">
                            {t("cms.artist.artist_type")}
                          </span>
                        );
                      }

                      const selectedArray = Array.isArray(selected)
                        ? selected
                        : [selected];

                      const selectedLabels = selectedArray
                        .map(
                          (value) =>
                            artistTypeOptions.find(
                              (option) => option.value === Number(value),
                            )?.label || value,
                        )
                        .join(", ");

                      return <span className="text-sm">{selectedLabels}</span>;
                    }}
                  >
                    {artistTypeOptions.map((option) => (
                      <MenuItem key={option.value} value={option.value}>
                        {option.label}
                      </MenuItem>
                    ))}
                  </Select>
                </div>

                <TextFieldCustomer
                  name="company"
                  value={values.company}
                  onChange={handleChange("company")}
                  label={t("cms.artist.management_company")}
                  placeholder={t("cms.artist.management_company")}
                />

                <div className="flex flex-col gap-2">
                  <label className="text-sm font-bold text-gray-700">
                    {t("common.country")}
                    <span className="text-red-600"> *</span>
                  </label>
                  <div>
                    <Select
                      name="country"
                      value={values.country}
                      onChange={(event) => {
                        setFieldValue("country", event.target.value);
                      }}
                      onBlur={handleBlur}
                      size="small"
                      displayEmpty
                      className="rounded text-sm w-full"
                      renderValue={(selected) => {
                        if (selected !== "" && selected !== undefined) {
                          return (
                            <span className="text-sm">
                              {countryOptions.find(
                                (option) =>
                                  option.value ===
                                  (selected as unknown as CountryEnum),
                              )?.label || selected}
                            </span>
                          );
                        }
                        return (
                          <span className="text-[#BFBFBF] font-normal text-sm">
                            {t("common.country")}
                          </span>
                        );
                      }}
                    >
                      {countryOptions.map((option) => (
                        <MenuItem key={option.value} value={option.value}>
                          {option.label}
                        </MenuItem>
                      ))}
                    </Select>
                    {touched.country && errors.country && (
                      <div className="text-red-500 text-sm">
                        {errors.country}
                      </div>
                    )}
                  </div>
                </div>

                <div className="flex flex-col gap-[10px]">
                  <label className="text-sm font-bold text-gray-700">
                    {t("common.biographical_description")}
                  </label>
                  <textarea
                    name="biography"
                    placeholder={t("common.biographical_description")}
                    value={values.biography}
                    onChange={handleChange}
                    className="border border-solid border-[#D9D9D9] rounded-[4px] py-2 px-3 outline-none"
                  />
                </div>

                <div className="flex justify-end space-x-2 border-t border-gray-300 py-2.5">
                  <button
                    type="button"
                    onClick={onClose}
                    className="rounded-lg cursor-pointer select-none px-4 py-2 text-base text-gray-600 hover:bg-gray-300"
                  >
                    {t("common.cancel")}
                  </button>
                  <IconButton
                    type="submit"
                    loading={loading}
                    disabled={loading}
                    className={`!rounded-lg !px-4 !text-base !text-white w-[102px]
                ${loading ? "!bg-gray-400 cursor-not-allowed" : "!bg-orange-500 hover:bg-red-600"}
              `}
                  >
                    {t("common.confirm")}
                  </IconButton>
                </div>
              </Form>
            )}
          </Formik>
        )}
      </DialogContent>
    </Dialog>
  );
}
