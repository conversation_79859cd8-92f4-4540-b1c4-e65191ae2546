import ModalComfirm from "@components/ModalConfirm";
import CloseIcon from "@mui/icons-material/Close";
import {Dialog, DialogContent, DialogTitle, IconButton} from "@mui/material";
import {useState} from "react";
import {convertDate} from "src/utils/timeUtils";
import {useFormik} from "formik";
import * as Yup from "yup";
import {useTranslation} from "react-i18next";
import {useQuery} from "@tanstack/react-query";
import ApiArtist from "@api/ApiArtist";

interface ConfirmModalProps {
  open: boolean;
  onSubmit: (values: {title: string; message: string}) => void;
  onCancel: () => void;
  artistId: string | null;
}

export default function ModalNotify({
  open,
  onSubmit,
  onCancel,
  artistId,
}: ConfirmModalProps) {
  const {t} = useTranslation();
  const [loading, setLoading] = useState(false);
  const [showConfirmModal, setShowConfirmModal] = useState(false);

  const validationSchema = Yup.object({
    title: Yup.string().required(t("common.mandatory_title")),
    message: Yup.string().required(t("common.mandatory_message")),
  });

  const formik = useFormik({
    initialValues: {
      title: "",
      message: "",
    },
    validationSchema: validationSchema,
    onSubmit: () => {
      setLoading(true);
      setShowConfirmModal(true);
    },
  });

  const handleConfirmSubmit = () => {
    onSubmit(formik.values);
    setShowConfirmModal(false);
  };

  // const today = new Date().toLocaleDateString("en-EN");
  const today = new Date().toISOString().split("T")[0];

  const {
    data: dataArtist,
    // isLoading,
    // error,
  } = useQuery({
    queryKey: ["artistDetail", artistId],
    queryFn: () => (artistId ? ApiArtist.getArtist(artistId) : null),
    enabled: !!artistId,
  });

  return (
    <>
      <Dialog
        open={open}
        onClose={onCancel}
        maxWidth="sm"
        fullWidth
        classes={{paper: "rounded-lg"}}
      >
        <div className="flex items-center justify-between border-b border-gray-300 pr-4">
          <DialogTitle className="p-0 text-base font-bold text-[#000000D9]">
            {t("common.send_notification")}
          </DialogTitle>
          <IconButton
            aria-label="close"
            onClick={onCancel}
            className="text-gray-500 hover:text-[#000000D9]"
            size="small"
          >
            <CloseIcon />
          </IconButton>
        </div>

        <DialogContent className="!px-0 !py-0">
          <div className="flex flex-col">
            <div className="flex flex-col gap-[5px] px-6 py-[10px] bg-[#FFECE8]">
              <span className="font-normal text-sm text-[#393939]">
                {t("common.send_notice_to")}
              </span>
              <div className="flex items-center justify-between">
                <div className="flex gap-3 items-center">
                  <img
                    src={
                      dataArtist?.images?.DEFAULT ?? "/image/default-avatar.png"
                    }
                    className="h-10 w-10 object-cover rounded-full"
                  />
                  <span className="font-semibold text-xl text-[#242728]">
                    {dataArtist?.stageName ??
                      dataArtist?.name ??
                      dataArtist?.user?.username ??
                      "-"}
                  </span>
                </div>
                <span>{convertDate(today)}</span>
              </div>
            </div>
            <form
              onSubmit={formik.handleSubmit}
              className="flex flex-col p-6 gap-4"
            >
              <div className="flex flex-col gap-[10px]">
                <div className="flex items-center gap-[5px]">
                  <div className="font-semibold text-sm text-[#000000D9]">
                    {t("common.title")}
                  </div>
                  <span className="text-red-600">*</span>
                </div>
                <div className="flex flex-col gap-1">
                  <input
                    type="text"
                    name="title"
                    placeholder={t("common.title")}
                    className="border border-solid border-[#D9D9D9] rounded-[4px] py-2 px-3"
                    value={formik.values.title}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                  />
                  {formik.touched.title && formik.errors.title ? (
                    <div className="text-red-500 text-sm">
                      {formik.errors.title}
                    </div>
                  ) : null}
                </div>
              </div>
              <div className="flex flex-col gap-[10px]">
                <div className="flex items-center gap-[5px]">
                  <div className="font-semibold text-sm text-[#000000D9]">
                    {t("common.message")}
                  </div>
                  <span className="text-red-600">*</span>
                </div>
                <div className="flex flex-col gap-1">
                  <textarea
                    name="message"
                    placeholder={t("common.message")}
                    value={formik.values.message}
                    onChange={(e) => {
                      formik.handleChange(e);
                      formik.setFieldTouched("message", true, false);
                    }}
                    onBlur={formik.handleBlur}
                    className="border border-solid border-[#D9D9D9] rounded-[4px] py-2 px-3 w-full h-24 resize-none"
                  />
                  {formik.touched.message && formik.errors.message ? (
                    <div className="text-red-500 text-sm">
                      {formik.errors.message}
                    </div>
                  ) : null}
                </div>
              </div>
              <div className="pt-10">
                <div className="flex justify-end space-x-2 border-t border-gray-300 py-2">
                  <div
                    onClick={onCancel}
                    className="rounded-lg cursor-pointer select-none px-4 py-2 text-base text-gray-600 hover:bg-gray-300"
                  >
                    {t("common.cancel")}
                  </div>
                  <IconButton
                    type="submit"
                    loading={loading}
                    disabled={loading}
                    className={`!rounded-lg !px-4 !text-base !text-white w-[102px]
                      ${loading ? "!bg-gray-400 cursor-not-allowed" : "!bg-orange-500 hover:bg-red-600"}
                    `}
                  >
                    {!loading && t("common.send")}
                  </IconButton>
                </div>
              </div>
            </form>
          </div>
        </DialogContent>
      </Dialog>

      {showConfirmModal && (
        <ModalComfirm
          open={true}
          onCancel={() => setShowConfirmModal(false)}
          onConfirm={handleConfirmSubmit}
          title={t("common.send_notification")}
        >
          <div className="flex items-center justify-center py-6 px-6 gap-1">
            <span className="font-normal text-xl">
              {t("common.confirm_send")}
            </span>
            <img
              src={dataArtist?.images?.DEFAULT ?? "/image/default-avatar.png"}
              className="w-5 h-5 border-[1.5px] border-solid border-[#262626] rounded-full"
            />
            <span className="font-semibold text-base">
              {dataArtist?.stageName ??
                dataArtist?.name ??
                dataArtist?.user?.username ??
                "-"}
            </span>
            <span className="font-normal text-xl">?</span>
          </div>
        </ModalComfirm>
      )}
    </>
  );
}
