import ApiSong from "@api/ApiSong";
import {ISong} from "src/types";
import {logEvent} from "./firebase";
import {t} from "i18next";
import {toast} from "react-toastify";

export const handleLikeSong = (song?: ISong) => {
  if (!song || !song.id) return;
  ApiSong.likeSong(song.id).then((response) => {
    logEvent("like_song", {
      song_id: song.id,
      song_name: song.name,
      song_artist: song.artists?.map((artist) => artist.name).join(", "),
      like_status: response ? "liked" : "disliked",
    });
    toast.success(
      response.isLiked
        ? t("common.like_successfully")
        : t("common.dislike_successfully"),
    );
  });
  const likeButtons = document.getElementsByClassName(
    `like-button song-${song.id}-liking`,
  );
  const isLiked =
    likeButtons.length > 0 && likeButtons[0].classList.contains("liked");
  for (let i = 0; i < likeButtons.length; i++) {
    likeButtons[i].classList.toggle("liked");
  }
  const textLikeButtons = document.getElementsByClassName(
    `text-like-button song-${song.id}-liking`,
  );
  for (let i = 0; i < textLikeButtons.length; i++) {
    textLikeButtons[i].classList.toggle("liked");
    textLikeButtons[i].innerHTML = isLiked
      ? t("common.menu.add_to_playlist_favorite")
      : t("common.menu.remove_from_playlist_favorite");
  }
};

export const handleQueueLike = (queueList: ISong[]) => {
  ApiSong.getLikedById(queueList.map((q) => q.id)).then((res) => {
    for (const item of res) {
      const likeButtons = document.getElementsByClassName(
        `like-button song-${item.id}-liking`,
      );
      for (let i = 0; i < likeButtons.length; i++) {
        if (item.isLiked) {
          likeButtons[i].classList.add("liked");
        } else {
          likeButtons[i].classList.remove("liked");
        }
      }
    }
  });
};

export const clearLike = () => {
  const allLikeButtons = document.getElementsByClassName("like-button");
  for (let i = 0; i < allLikeButtons.length; i++) {
    allLikeButtons[i].classList.remove("liked");
  }
};
