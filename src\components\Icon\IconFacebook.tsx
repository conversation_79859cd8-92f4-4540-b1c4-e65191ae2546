import {SVGProps} from "react";

function IconFacebook(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={29}
      height={29}
      viewBox="0 0 29 29"
      fill="none"
      {...props}
    >
      <circle cx="14.5" cy="14.258" r="12.25" fill="url(#a)" />
      <path
        fill="#fff"
        d="m19.062 18.004.544-3.457h-3.404v-2.243c0-.946.475-1.869 2-1.869h1.548V7.491s-1.405-.233-2.747-.233c-2.805 0-4.636 1.656-4.636 4.654v2.635H9.25v3.457h3.117v8.359a12.66 12.66 0 0 0 1.917.145c.652 0 1.292-.05 1.918-.145v-8.359h2.86Z"
      />
      <defs>
        <linearGradient
          id="a"
          x1="14.5"
          x2="14.5"
          y1="2.008"
          y2="26.435"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#18ACFE" />
          <stop offset="1" stopColor="#0163E0" />
        </linearGradient>
      </defs>
    </svg>
  );
}

export default IconFacebook;
