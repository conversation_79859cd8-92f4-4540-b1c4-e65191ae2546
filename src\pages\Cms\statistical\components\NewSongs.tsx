import clsx from "clsx";
import <PERSON><PERSON><PERSON><PERSON> from "./HeaderChart";
import {useState} from "react";
import <PERSON><PERSON>hart<PERSON>ust<PERSON> from "./BarChartCustom";
import {useTranslation} from "react-i18next";

function NewSongs() {
  const {t} = useTranslation();
  const [activeTab, setActiveTab] = useState<number>(0);

  const data = [
    {name: t("cms.dashboard.mon"), value: 10},
    {name: t("cms.dashboard.tue"), value: 20},
    {name: t("cms.dashboard.wed"), value: 25},
    {name: t("cms.dashboard.thu"), value: 35},
    {name: t("cms.dashboard.fri"), value: 40},
    {name: t("cms.dashboard.sat"), value: 45},
    {name: t("cms.dashboard.sun"), value: 50},
  ];

  const LINEARGRADIENT = {
    id: "linear-gradient-green",
    linear: [
      {offset: "0%", color: "#00E15A"},
      {offset: "100%", color: "#86F2B1"},
    ],
  };

  const labels = {
    x: t("cms.dashboard.day"),
    y: t("cms.dashboard.amount_songs"),
  };

  const handleTabClick = (index: number) => {
    setActiveTab(index);
  };

  return (
    <div className="bg-white rounded-2xl shadow-md p-5">
      <div>
        <div className="flex justify-between items-center">
          <HeaderChart title={t("cms.dashboard.amount_new_songs")} />
          <div className="flex gap-3">
            <button
              className={clsx(
                "text-sm rounded-md px-4 py-2 bg-[#F2F2F2] text-[#707070]",
                {
                  "!text-[#FF4319] !bg-[#FF43191A] font-[600]": activeTab === 0,
                },
              )}
              onClick={() => handleTabClick(0)}
            >
              {t("cms.dashboard.day")}
            </button>
            <button
              className={clsx(
                "text-sm rounded-md px-4 py-2 bg-[#F2F2F2] text-[#707070]",
                {
                  "!text-[#FF4319] !bg-[#FF43191A] font-[600]": activeTab === 1,
                },
              )}
              onClick={() => handleTabClick(1)}
            >
              {t("cms.dashboard.week")}
            </button>
            <button
              className={clsx(
                "text-sm rounded-md px-4 py-2 bg-[#F2F2F2] text-[#707070]",
                {
                  "!text-[#FF4319] !bg-[#FF43191A] font-[600]": activeTab === 2,
                },
              )}
              onClick={() => handleTabClick(2)}
            >
              {t("cms.dashboard.month")}
            </button>
          </div>
        </div>

        <div className="p-5">
          {activeTab === 0 && (
            <BarChartCustom
              data={data}
              linearGradientColor={LINEARGRADIENT}
              labels={labels}
            />
          )}

          {activeTab === 1 && <div>2</div>}
          {activeTab === 2 && <div>3</div>}
        </div>
      </div>
    </div>
  );
}

export default NewSongs;
