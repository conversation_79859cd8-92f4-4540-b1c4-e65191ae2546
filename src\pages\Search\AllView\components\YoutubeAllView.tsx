import CommonYoutubeCard from "@components/CommonYoutubeCard";
import {ISong} from "src/types";
import Slider from "@components/Slider";
import {SwiperSlide} from "swiper/react";
import {useDispatch} from "react-redux";
import {playSingleSong} from "@redux/slices/PlayerSlice";

interface IAllViewMVProp {
  data?: ISong[];
}

export function YoutubeAllView({data}: IAllViewMVProp) {
  const dispatch = useDispatch();
  return (
    <Slider slidesPerView={5} spaceBetween={16}>
      {data?.map((item, index) => {
        return (
          <SwiperSlide
            key={`search_youtube_all_${item.id}`}
            virtualIndex={index}
          >
            <CommonYoutubeCard
              data={item}
              isSearch={true}
              handlePlayMusic={() => dispatch(playSingleSong(item))}
            />
          </SwiperSlide>
        );
      })}
    </Slider>
  );
}
