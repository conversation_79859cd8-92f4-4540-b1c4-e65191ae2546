import {SVGProps} from "react";

function IconArrowRightSlide(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={40}
      height={40}
      fill="none"
      viewBox="0 0 40 40"
      {...props}
    >
      <rect
        width="40"
        height="40"
        rx="20"
        transform="matrix(-1 0 0 1 40 0)"
        fill="white"
        fillOpacity="0.05"
      />
      <path
        d="M17 26L23 20L17 14"
        stroke="white"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}

export default IconArrowRightSlide;
