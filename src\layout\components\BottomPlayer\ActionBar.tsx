import ApiSong from "@api/ApiSong";
import CustomTooltip from "@components/CustomTooltip";
import IconPlaylist2 from "@components/Icon/IconPlaylist2";
import IconPlaylistDuotone from "@components/Icon/IconPlaylistDuotone";
import IconShare from "@components/Icon/IconShare";
import IconVolume from "@components/Icon/IconVolume";
import IconVolume2 from "@components/Icon/IconVolume2";
import IconVolume3 from "@components/Icon/IconVolume3";
import IconVolumeX from "@components/Icon/IconVolumeX";
import ModalShare from "@components/ModalShare";
import {Divider, Slider} from "@mui/material";
import {toggleLyrics} from "@redux/slices/PlayerSlice";
import {closeWishlist, toggleWishlist} from "@redux/slices/WishlistSlice";
import {IRootState} from "@redux/store";
import {useMutation} from "@tanstack/react-query";
import clsx from "clsx";
import {useEffect, useState} from "react";
import {useTranslation} from "react-i18next";
import {useDispatch, useSelector} from "react-redux";
import {toast} from "react-toastify";
import PlayerUtil from "src/core/PlayerUtil";
import {ESongType, ISong} from "src/types";
import {logEvent} from "src/utils/firebase";
import {generateShareLink} from "src/utils/global";
import {useWindowWidth} from "src/utils/hooks";

interface ActionBarProps {
  data?: ISong;
}

export default function ActionBar({data}: ActionBarProps) {
  const {t} = useTranslation();
  const width = useWindowWidth();
  const isOpen = useSelector((state: IRootState) => state?.wishlist?.isOpen);
  const [volume, setVolume] = useState(PlayerUtil.instance.volume);
  const [muted, setMuted] = useState(PlayerUtil.instance.muted);
  const {isOpenLyrics, currentSong} = useSelector(
    (state: IRootState) => state.player,
  );
  const dispatch = useDispatch();
  const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null);
  const [isPlaylist, setIsPlaylist] = useState(false);
  const [isAddList, setIsAddList] = useState(false);
  const open = Boolean(anchorEl);
  const shareMutate = useMutation({
    mutationFn: ApiSong.shareSong,
    onSuccess: () => {
      const link = generateShareLink({type: "song", data});
      navigator.clipboard.writeText(link).then(() => {
        toast.success(t("common.copy_link_success"));
      });
    },
    onError: () => {
      toast.error(t("common.copy_link_failed"));
    },
  });

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget as HTMLButtonElement);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleClickAddList = () => {
    setIsAddList(!isAddList);
  };

  const toggleMute = () => {
    setMuted(!muted);
    PlayerUtil.instance.changeVolume(undefined, !muted);
    logEvent("change_audio_settings", {
      song_id: currentSong?.id,
      song_name: currentSong?.name,
      setting_type: "mute",
    });
  };

  const getVolumeIcon = () => {
    if (muted) return <IconVolumeX />;
    if (volume < 0.33) return <IconVolume />;
    if (volume < 0.66) return <IconVolume2 />;
    return <IconVolume3 />;
  };

  const handleChangeVolume = (_: Event, newValue: number | number[]) => {
    if (muted) {
      if ((newValue as number) > 0) {
        setMuted(false);
      }
    } else {
      if (newValue === 0) {
        setMuted(true);
      }
    }
    setVolume(newValue as number);
    PlayerUtil.instance.changeVolume(volume, newValue === 0);
    logEvent("change_audio_settings", {
      song_id: currentSong?.id,
      song_name: currentSong?.name,
      setting_type: "volume",
      new_value: newValue as number,
    });
  };

  const handleOpenLyrics = () => {
    dispatch(toggleLyrics());
  };

  const handleTogglePlaylist = () => {
    dispatch(toggleWishlist());
    setIsPlaylist(!isPlaylist);
  };

  useEffect(() => {
    if (width < 1024) {
      dispatch(closeWishlist());
    }
  }, [width]);

  const handleCopyLink = () => {
    shareMutate.mutateAsync(data?.id ?? "");
  };

  return (
    <div
      onClick={(e) => e.stopPropagation()}
      className="flex max-[834px]:hidden max-[834px]:min-w-0 gap-x-6 justify-end items-center flex-1 px-3 min-w-[218px]"
    >
      <CustomTooltip title={t("common.lyric_song")}>
        <button
          onClick={handleClickAddList}
          disabled={data?.type === ESongType.YOUTUBE}
        >
          <IconPlaylist2
            onClick={handleOpenLyrics}
            className={`${isOpenLyrics ? "text-[#ff0000]" : ""} ${data?.type === ESongType.YOUTUBE && "text-gray-500"}`}
          />
        </button>
      </CustomTooltip>
      <CustomTooltip title={t("common.share")}>
        <button
          onClick={handleMenuClick}
          className="flex items-center justify-center"
        >
          <IconShare className="cursor-pointer" />
        </button>
      </CustomTooltip>
      <ModalShare
        open={open}
        onCancel={handleMenuClose}
        handleCopyLink={handleCopyLink}
        image={currentSong?.images?.SMALL || currentSong?.images?.DEFAULT}
        name={currentSong?.name}
        artists={currentSong?.artists}
        shareUrl={generateShareLink({type: "song", data})}
      />
      <div className="relative flex group">
        <button onClick={toggleMute}>{getVolumeIcon()}</button>
        <div className="absolute top-[-60px] left-[-30px] items-center justify-center w-20 h-8 flex opacity-0 group-hover:opacity-100 group-active:opacity-100 rounded-lg">
          <Slider
            min={0}
            max={1}
            step={0.05}
            value={volume}
            aria-label="Temperature"
            orientation="vertical"
            onChange={handleChangeVolume}
            sx={{
              "height": "65px",
              "width": "7px",
              "& .MuiSlider-thumb": {
                display: "none",
              },
              "& .MuiSlider-track": {
                background:
                  "linear-gradient(to top, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 1))",
                height: muted ? "0%!important" : undefined,
                border: "none",
                width: "7px",
              },
              "& .MuiSlider-rail": {
                backgroundColor: "rgba(255, 255, 255, 0.3)",
                width: "7px",
              },
            }}
          />
        </div>
      </div>
      <Divider
        orientation="vertical"
        className="bg-[#FFFFFF36] w-0.5"
        sx={{height: 16}}
      />
      <CustomTooltip
        title={
          isOpen ? t("common.menu.close_queue") : t("common.menu.open_queue")
        }
      >
        <div className="relative">
          <IconPlaylistDuotone
            className={clsx(
              "rounded-[4px] cursor-pointer",
              isOpen && "bg-red-600",
            )}
            onClick={handleTogglePlaylist}
          />

          {isOpen && (
            <div className="h-1 w-1 bg-red-600 rounded-full absolute -bottom-2 left-[10px]" />
          )}
        </div>
      </CustomTooltip>
    </div>
  );
}
