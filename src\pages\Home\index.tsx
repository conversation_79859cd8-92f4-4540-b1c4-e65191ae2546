import ListenToday from "./components/ListenToday";
import Top100 from "./components/Top100";
import TopGenres from "@components/TopGenres";
import FollowingArtist from "./components/FollowingArtist";
import {PlaylistType} from "src/types";
import HeaderTitle from "@components/HeaderTitle";
import TrendingYTB from "./components/TrendingYTB";

export default function Home() {
  return (
    <div className="flex flex-col gap-2 sm:gap-4 md:gap-6 lg:gap-8 pt-5 mb-14 px-4 sm:px-6 md:px-8">
      <HeaderTitle />
      <ListenToday />
      <Top100 />
      <TopGenres page={0} pageSize={3} playlistType={PlaylistType.PLAYLIST} />
      <TrendingYTB />
      <FollowingArtist />
    </div>
  );
}
