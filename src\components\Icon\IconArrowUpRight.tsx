import {SVGProps} from "react";

function IconArrowUpRight({
  width = "16",
  height = "16",
  ...props
}: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox={`0 0 ${width} ${height}`}
      className={props.className}
      fill="none"
      {...props}
    >
      <path
        d="M13.625 8.625V12.375C13.625 12.7065 13.4933 13.0245 13.2589 13.2589C13.0245 13.4933 12.7065 13.625 12.375 13.625H3.625C3.29348 13.625 2.97554 13.4933 2.74112 13.2589C2.5067 13.0245 2.375 12.7065 2.375 12.375V3.625C2.375 3.29348 2.5067 2.97554 2.74112 2.74112C2.97554 2.5067 3.29348 2.375 3.625 2.375H7.375"
        stroke={props.stroke || "#E5E5E5"}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M13.625 2.375L8 8"
        stroke={props.stroke || "#E5E5E5"}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M9.875 2.375H13.625V6.125"
        stroke={props.stroke || "#E5E5E5"}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}

export default IconArrowUpRight;
