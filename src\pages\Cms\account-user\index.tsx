import {
  Box,
  MenuItem,
  Select,
  <PERSON>nackbar,
  Switch,
  Tab,
  Tabs,
} from "@mui/material";
import {GridColDef, GridSortModel} from "@mui/x-data-grid";
import React, {useEffect, useState} from "react";
import CmsTable from "../components/CmsTable";
import SearchInput from "../components/SearchInput";
import {keepPreviousData, useMutation, useQuery} from "@tanstack/react-query";
import QUERY_KEY from "@api/QueryKey";
import {IUserInfo} from "src/types";
import ApiCMSUser from "@api/ApiCMSUser";
import {useSelector} from "react-redux";
import {IRootState} from "@redux/store";

export default function CmsAccountUser() {
  const localUserData = useSelector((state: IRootState) => state.user);

  const [page, setPage] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const [value, setValue] = useState(0); // Active tab index
  const [searchText, setSearchText] = useState("");
  const [debouncedSearchText, setDebouncedSearchText] = useState("");
  const [status, setStatus] = useState("");
  const [sortModel, setSortModel] = useState<GridSortModel>([]);

  const updateUserMutation = useMutation({
    mutationFn: ApiCMSUser.updateUser,
  });

  const getCmsUsers = useQuery({
    queryKey: [
      QUERY_KEY.USER.GET_LIST_USER,
      status === "ACTIVE" ? "" : status,
      page,
      pageSize,
      sortModel,
      value,
      debouncedSearchText,
    ],
    placeholderData: keepPreviousData,
    queryFn: () =>
      ApiCMSUser.getUserCms({
        page: page,
        pageSize: pageSize,
        status: status === "ACTIVE" ? "ACTIVE" : status,
        search: debouncedSearchText,
        sortField: sortModel[0]?.field || "",
        sortDirection: sortModel[0]?.sort || "",
        role: value === 0 ? "ADMIN" : "END_USER",
      }),
  });

  const tableRows = getCmsUsers?.data?.data?.map((item: IUserInfo, index) => ({
    id: item?.id, // Using to get id to handle onclick
    ids: index + 1, // Using to show in the table
    username: item?.username,
    email: item?.email,
    status: item?.status,
    device: item?.device,
    report: item?.report,
    violent: item?.violent,
  }));

  const handleStatusChange = async (id: string, currentStatus: string) => {
    updateUserMutation.mutateAsync(
      {id, status: currentStatus},
      {
        onSuccess: () => {
          <Snackbar message="Thay đổi trạng thái tài khoản thành công" />;
        },
      },
    );
  };

  const columns: GridColDef[] = [
    {
      field: "ids",
      headerName: "ID",
      width: 46,
      sortable: false,
      disableColumnMenu: true,
      headerAlign: "center",
      align: "center",
    },
    {
      field: "username",
      headerName: "Tài khoản",
      flex: 1,
      minWidth: 150,
      sortable: false,
    },
    {
      field: "email",
      headerName: "Email",
      flex: 1,
      minWidth: 150,
      sortable: false,
    },
    {
      field: "device",
      headerName: "Thiết bị",
      flex: 1,
      minWidth: 150,
      sortable: false,
    },
    {
      field: "status",
      headerName: "Trạng thái tài khoản",
      flex: 1,
      minWidth: 150,
      sortable: true,
      renderCell: (params) => {
        const statusMap = {
          ACTIVE: {color: "green", text: "Đang hoạt động"},
          LOCKED: {color: "red", text: "Đã khóa"},
          PENDING: {color: "orange", text: "Tạm dừng"},
        };
        const {color, text} =
          statusMap[params.value as keyof typeof statusMap] || {};
        return (
          <div className="font-bold">
            <span style={{color}} className="flex gap-1 items-center">
              <span
                className="rounded-full w-1.5 aspect-square inline-block"
                style={{
                  backgroundColor: color,
                }}
              />
              &ensp;{text}
            </span>
          </div>
        );
      },
    },
    {
      field: "action",
      headerName: "Hành động",
      flex: 1,
      minWidth: 150,
      sortable: false,
      renderCell: (params) => {
        return (
          <div className="flex gap-2">
            <Switch
              disabled={params?.row?.id === localUserData?.userInfo?.id}
              defaultChecked={!!params?.row?.status}
              color="warning"
              onChange={(event) =>
                handleStatusChange(
                  params?.row?.id,
                  event.target.checked ? "ACTIVE" : "LOCKED",
                )
              }
            />
          </div>
        );
      },
    },
  ];

  const columns2: GridColDef[] = [
    ...columns.slice(0, 4),
    {
      field: "report",
      headerName: "Báo cáo",
      flex: 1,
      minWidth: 150,
      sortable: false,
      renderCell: (params) => <div>{params?.value || "-"}</div>,
    },
    {
      field: "violent",
      headerName: "Vi phạm",
      flex: 1,
      minWidth: 150,
      sortable: false,
      renderCell: (params) => <div>{params?.value || "-"}</div>,
    },
    ...columns.slice(4),
  ];

  const handleChange = (_event: React.SyntheticEvent, newValue: number) => {
    setValue(newValue);
  };

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedSearchText(searchText);
    }, 300);

    return () => {
      clearTimeout(handler);
    };
  }, [searchText]);

  return (
    <div className="pt-2 px-5 bg-white">
      <Box sx={{width: "100%"}}>
        <Box sx={{borderBottom: 1, borderColor: "divider"}}>
          <Tabs
            value={value}
            onChange={handleChange}
            sx={{
              "& .MuiTabs-indicator": {
                backgroundColor: "#FF4319",
              },
              "& .MuiTab-root": {
                "color": "#000000",
                "fontWeight": "600",
                "textTransform": "none",
                "&.Mui-selected": {
                  color: "#FF4319",
                },
                "&:hover": {
                  color: "#FF4319",
                },
              },
            }}
          >
            <Tab label="CMS cấp 2" />
            <Tab label="End - user" />
          </Tabs>
        </Box>
        <div className="flex flex-wrap gap-2 my-5">
          <SearchInput
            placeholder="Tài khoản, email, thiết bị ..."
            searchText={searchText}
            className="py-[5px]"
            onChange={(v) => setSearchText(v)}
          />
          <Select
            value={status}
            onChange={(e) => setStatus(e.target.value)}
            size="small"
            displayEmpty
            sx={{borderRadius: "8px", fontWeight: "600", fontSize: "14px"}}
            className="bg-custom-background custom-select h-10 w-[160px]"
            inputProps={{"aria-label": "Without label"}}
            MenuProps={{
              PaperProps: {
                style: {
                  maxHeight: 250,
                },
                sx: {
                  "marginTop": "4px",
                  "borderRadius": "8px",
                  "& .MuiMenuItem-root": {
                    fontSize: "14px",
                  },
                  "& .MuiMenuItem-root.Mui-selected:not(:first-child)": {
                    backgroundColor: "#FFF4F2",
                    fontSize: "14px",
                    fontWeight: "bold",
                  },
                  "& .MuiMenuItem-root.Mui-selected:first-child": {
                    backgroundColor: "#FFF",
                    fontSize: "14px",
                  },
                },
              },
            }}
          >
            <MenuItem value="">Trạng thái</MenuItem>
            <MenuItem value="ACTIVE">Đang hoạt động</MenuItem>
            <MenuItem value="LOCKED">Đã khóa</MenuItem>
            <MenuItem value="PENDING">Tạm dừng</MenuItem>
          </Select>
        </div>
      </Box>
      <CmsTable
        rows={tableRows}
        columns={value === 0 ? columns : columns2}
        loading={getCmsUsers?.isLoading}
        totalItems={getCmsUsers?.data?.meta?.totalItems}
        currentPage={page || 0}
        onPageChange={(page) => setPage(page)}
        rowsPerPage={pageSize || 10}
        onRowsPerPageChange={(rowsPerPage) => setPageSize(rowsPerPage)}
        onSortModelChange={(model) => setSortModel(model)}
        hideFooter
      />
    </div>
  );
}
