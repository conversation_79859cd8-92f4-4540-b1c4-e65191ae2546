import clsx from "clsx";
import "./PlayerSlider.scss";
import PlayerUtil from "src/core/PlayerUtil";
import PlayerListenerHandler from "src/core/PlayerListenerHandler";

interface PlayerSliderProps {
  defaultValue?: number;
  disabled?: boolean;
  className?: string;
  trackClassName?: string;
}

export default function PlayerSlider({
  defaultValue = 0,
  disabled,
  className,
  trackClassName,
}: PlayerSliderProps) {
  return (
    <div
      className={clsx(
        "player-slider-wrapper relative w-full h-1 cursor-pointer bg-[#FFFFFF3D] rounded-lg",
        className,
      )}
    >
      <div
        className={clsx(
          "player-track absolute h-full bg-white z-[1] rounded-lg",
          trackClassName,
        )}
        style={{width: `${defaultValue}%`}}
      />
      <input
        disabled={disabled}
        type="range"
        defaultValue={defaultValue}
        min={0}
        step={0.01}
        max={100}
        className="player-slider"
        onMouseDown={() => {
          PlayerListenerHandler.isSliderDragging = true;
        }}
        onLostPointerCapture={(e) => {
          const time =
            ((Number(e.currentTarget.value) || 0) *
              PlayerUtil.instance.duration) /
            100;
          PlayerUtil.instance.seek(time);
          PlayerListenerHandler.isSliderDragging = false;
        }}
        onTouchStart={(e) => {
          e.stopPropagation();
          PlayerListenerHandler.isSliderDragging = true;
        }}
        onTouchMove={(e) => {
          e.stopPropagation();
        }}
        onChange={(e) => {
          e.currentTarget.previousElementSibling?.setAttribute(
            "style",
            `width:${e.currentTarget.value}%`,
          );
          const position =
            ((Number(e.currentTarget.value) || 0) *
              PlayerUtil.instance.duration) /
            100;
          PlayerUtil.instance.updatePositionText(position);
        }}
      />
    </div>
  );
}
