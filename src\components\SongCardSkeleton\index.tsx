import {Skeleton} from "@mui/material";

interface ISongCardProps {
  className?: string;
}

export default function SongCardSkeleton({
  className,
}: ISongCardProps): JSX.Element {
  return (
    <div className="flex flex-col w-full gap-[10px] hover:bg-[#FFFFFF17] pt-[10px]">
      <Skeleton
        variant="rounded"
        className={`${className} flex flex-row justify-between text-[#FFFFFF80] items-center transition-all transition-300 ease-out w-full px-3 py-[9px]`}
        sx={{
          maxWidth: "100%",
          width: "100%",
          height: "50px",
          bgcolor: "#752121CC",
        }}
      />
    </div>
  );
}
