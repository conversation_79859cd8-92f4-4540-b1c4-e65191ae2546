import ArtistCardSkeleton from "@components/ArtistCardSkeleton";
import CommonArtistCard from "@components/CommonArtistCard";
import Slider from "@components/Slider";
import {useTranslation} from "react-i18next";
import {IArtist} from "src/types";
import {SwiperSlide} from "swiper/react";

interface IArtistsProps {
  className?: string;
  data?: IArtist[];
  isLoading?: boolean;
}
export default function ListArtists({
  className,
  data,
  isLoading,
}: IArtistsProps) {
  const {t} = useTranslation();

  const artistsList = data ?? [];

  return (
    <div className={`${className} mt-2 gap-6 mb-10`}>
      <h2 className="text-xl md:text-2xl font-semibold mb-4 text-white">
        {t("topic.featured_artist")}
      </h2>

      {artistsList.length === 0 ? (
        <div className="text-white w-full h-[10vh] flex justify-center items-center text-xl">
          {t("common.list_is_empty")}
        </div>
      ) : (
        <Slider slidesPerView={6}>
          {artistsList.map((artist, index) => (
            <SwiperSlide key={index} virtualIndex={index}>
              {isLoading ? (
                <ArtistCardSkeleton />
              ) : (
                <CommonArtistCard data={artist} />
              )}
            </SwiperSlide>
          ))}
        </Slider>
      )}
    </div>
  );
}
