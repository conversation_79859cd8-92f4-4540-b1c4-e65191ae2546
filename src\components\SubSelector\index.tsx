import {Button} from "@mui/material";

type SubSelectorItem = {
  label: string;
  value: string;
};
interface SubSelectorProps {
  items: SubSelectorItem[];
  onClick: (item: string) => void;
  selectedItem?: string;
  className?: string;
}

export default function SubSelector({
  items,
  onClick,
  selectedItem,
  className = "",
}: SubSelectorProps) {
  return (
    <div className={`flex flex-wrap gap-2 ${className}`}>
      {items.map((item) => (
        <Button
          variant="contained"
          sx={{
            borderRadius: "20px",
            padding: "8px 24px",
            fontSize: "16px",
            backgroundColor:
              selectedItem === item.value ? "#FF4319" : "#3e1e1e",
            height: "40px",
            textTransform: "capitalize",
          }}
          onClick={() => onClick(item.value)}
        >
          {item.label}
        </Button>
      ))}
    </div>
  );
}
