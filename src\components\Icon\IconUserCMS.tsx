import {SVGProps} from "react";

function IconUserCMS({
  width = "20",
  height = "20",
  ...props
}: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox={`0 0 ${width} ${height}`}
      className={props.className}
      fill="none"
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M5.00002 5.83317C5.00002 4.7281 5.43901 3.66829 6.22041 2.88689C7.00181 2.10549 8.06162 1.6665 9.16669 1.6665C10.2718 1.6665 11.3316 2.10549 12.113 2.88689C12.8944 3.66829 13.3334 4.7281 13.3334 5.83317C13.3334 6.93824 12.8944 7.99805 12.113 8.77945C11.3316 9.56085 10.2718 9.99984 9.16669 9.99984C8.06162 9.99984 7.00181 9.56085 6.22041 8.77945C5.43901 7.99805 5.00002 6.93824 5.00002 5.83317ZM9.16669 3.33317C8.83838 3.33317 8.51329 3.39784 8.20998 3.52347C7.90666 3.64911 7.63107 3.83326 7.39892 4.0654C7.16677 4.29755 6.98263 4.57315 6.85699 4.87646C6.73135 5.17978 6.66669 5.50487 6.66669 5.83317C6.66669 6.16148 6.73135 6.48657 6.85699 6.78988C6.98263 7.09319 7.16677 7.36879 7.39892 7.60094C7.63107 7.83308 7.90666 8.01723 8.20998 8.14287C8.51329 8.26851 8.83838 8.33317 9.16669 8.33317C9.82973 8.33317 10.4656 8.06978 10.9345 7.60094C11.4033 7.1321 11.6667 6.49621 11.6667 5.83317C11.6667 5.17013 11.4033 4.53424 10.9345 4.0654C10.4656 3.59656 9.82973 3.33317 9.16669 3.33317ZM3.67752 14.6673C3.40835 15.009 3.33335 15.2673 3.33335 15.4165C3.33335 15.5148 3.36252 15.6098 3.50419 15.7365C3.66835 15.8832 3.96252 16.0448 4.42919 16.189C5.36252 16.4773 6.72585 16.6232 8.35085 16.6582C8.46029 16.6605 8.5682 16.6843 8.66843 16.7283C8.76865 16.7723 8.85923 16.8356 8.93498 16.9146C9.01074 16.9936 9.0702 17.0868 9.10995 17.1888C9.14971 17.2908 9.16899 17.3996 9.16669 17.509C9.16439 17.6184 9.14056 17.7263 9.09656 17.8266C9.05255 17.9268 8.98924 18.0174 8.91024 18.0931C8.83123 18.1689 8.73807 18.2283 8.63609 18.2681C8.53411 18.3079 8.42529 18.3271 8.31585 18.3248C6.65169 18.289 5.09919 18.1407 3.93752 17.7815C3.35752 17.6015 2.80919 17.3507 2.39419 16.9798C1.95585 16.5882 1.66669 16.0615 1.66669 15.4165C1.66669 14.7607 1.96502 14.1473 2.37002 13.634C2.78169 13.1132 3.35085 12.634 4.01835 12.2257C5.35419 11.4123 7.17085 10.8332 9.16669 10.8332C9.53947 10.8332 9.90502 10.8526 10.2634 10.8915C10.4832 10.9148 10.6847 11.0245 10.8236 11.1964C10.9626 11.3683 11.0275 11.5884 11.0042 11.8082C10.9809 12.028 10.8712 12.2295 10.6993 12.3684C10.5274 12.5074 10.3073 12.5723 10.0875 12.549C9.78697 12.5162 9.48002 12.4998 9.16669 12.4998C7.48085 12.4998 5.96419 12.9915 4.88669 13.649C4.34752 13.9773 3.94002 14.334 3.67752 14.6673ZM17.6775 11.6948C17.4841 11.5013 17.2544 11.3478 17.0016 11.2431C16.7488 11.1384 16.4778 11.0844 16.2042 11.0844C15.9306 11.0844 15.6596 11.1384 15.4068 11.2431C15.154 11.3478 14.9243 11.5013 14.7309 11.6948L11.8825 14.5423C11.6913 14.7337 11.5673 14.982 11.5292 15.2498L11.2542 17.1732C11.2356 17.3015 11.2472 17.4323 11.2881 17.5554C11.329 17.6784 11.398 17.7902 11.4897 17.8819C11.5813 17.9735 11.6931 18.0426 11.8162 18.0834C11.9392 18.1243 12.0701 18.1359 12.1984 18.1173L14.1217 17.8423C14.3895 17.8042 14.6378 17.6802 14.8292 17.489L17.6767 14.6407C18.0673 14.25 18.2867 13.7202 18.2867 13.1678C18.2867 12.6153 18.0681 12.0855 17.6775 11.6948ZM15.9092 12.8732C15.9878 12.7973 16.093 12.7553 16.2023 12.7562C16.3115 12.7572 16.416 12.801 16.4933 12.8782C16.5705 12.9555 16.6144 13.06 16.6153 13.1693C16.6163 13.2785 16.5743 13.3838 16.4984 13.4623L13.7484 16.2123L13.0609 16.3107L13.1592 15.6232L15.9092 12.8732Z"
        fill={props.fill || "currentColor"}
        stroke="white"
        strokeWidth="0.2"
      />
    </svg>
  );
}

export default IconUserCMS;
