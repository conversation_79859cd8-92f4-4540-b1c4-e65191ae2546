@use "../../styles/global.scss" as globals;

.song-item {
  min-width: fit-content;
  .avatar-mask {
    display: none;
  }

  .song-action {
    display: none;

    &:has(.liked) {
      display: block;
    }

    &:hover {
      background-color: rgba(255, 255, 255, 0.2);
    }
  }

  @media screen and (max-width: globals.$sm) {
    .song-action {
      display: flex;
    }
    .song-duration {
      display: none;
    }
  }

  @media screen and (min-width: globals.$sm) {
    &:hover,
    &.focused {
      background-color: rgba(255, 255, 255, 0.1);
      .avatar-mask {
        display: flex;
        justify-content: center;
        align-items: center;
      }
      .song-duration {
        display: none;
      }
      .song-action {
        display: flex;
      }
    }
  }
}
