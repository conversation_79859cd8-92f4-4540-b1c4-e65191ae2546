import {SVGProps} from "react";

function IconPremium({
  width = "20",
  height = "20",
  ...props
}: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox={`0 0 ${width} ${height}`}
      className={props.className}
      fill="none"
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M10.8226 4.78334C11.1428 4.60172 11.3938 4.31919 11.5365 3.97987C11.6793 3.64054 11.7056 3.26351 11.6115 2.90762C11.5174 2.55172 11.3082 2.237 11.0164 2.01257C10.7246 1.78813 10.3667 1.66662 9.99855 1.66699C9.63043 1.66737 9.27279 1.78961 8.98145 2.01463C8.69011 2.23966 8.48147 2.55481 8.38809 2.91089C8.29471 3.26697 8.32185 3.64395 8.46527 3.98299C8.60869 4.32202 8.86032 4.60404 9.18089 4.78501L9.17255 4.80001C8.62672 5.93918 7.86839 7.57085 6.69755 8.18334C5.73589 8.68584 4.35589 8.43335 3.33089 8.24501C3.31444 8.01194 3.233 7.78814 3.09581 7.59901C2.95861 7.40987 2.77115 7.26298 2.5547 7.17499C2.33824 7.08701 2.10146 7.06145 1.87122 7.10122C1.64098 7.14099 1.42649 7.24449 1.25209 7.39999C1.0777 7.55549 0.950376 7.75676 0.884575 7.98096C0.818775 8.20515 0.817129 8.4433 0.879826 8.66839C0.942522 8.89347 1.06705 9.09648 1.23928 9.25437C1.41151 9.41227 1.62455 9.51873 1.85422 9.56168L4.31089 15.9033C4.49307 16.3735 4.81316 16.7776 5.22913 17.0625C5.6451 17.3475 6.13751 17.5 6.64172 17.5H13.3584C13.8626 17.5 14.355 17.3475 14.771 17.0625C15.1869 16.7776 15.507 16.3735 15.6892 15.9033L18.1451 9.56168C18.3733 9.5192 18.5852 9.41401 18.757 9.2579C18.9288 9.10178 19.0537 8.9009 19.1178 8.67777C19.1819 8.45465 19.1825 8.21808 19.1197 7.9946C19.0569 7.77111 18.9331 7.56953 18.7622 7.41244C18.5913 7.25535 18.38 7.14896 18.152 7.1052C17.924 7.06143 17.6884 7.08201 17.4714 7.16464C17.2545 7.24727 17.0648 7.38868 16.9237 7.57302C16.7826 7.75736 16.6957 7.97735 16.6726 8.20835C15.6234 8.34418 14.2709 8.51751 13.3026 8.01168C12.1551 7.41168 11.3859 5.89251 10.8226 4.78334ZM10.0209 6.75585C10.6609 7.79418 11.4284 8.91168 12.5309 9.48918C13.6367 10.0667 14.9976 10.0375 16.2142 9.93334L14.1351 15.3008C14.0744 15.4576 13.9678 15.5923 13.8291 15.6873C13.6905 15.7823 13.5264 15.8333 13.3584 15.8333H6.64172C6.47366 15.8333 6.30957 15.7823 6.17096 15.6873C6.03235 15.5923 5.92572 15.4576 5.86505 15.3008L3.81422 10.0067C5.01339 10.16 6.37005 10.235 7.46922 9.66001C8.61755 9.06001 9.38589 7.85001 10.0209 6.75585Z"
        fill={props.fill || "#ffffff"}
      />
    </svg>
  );
}

export default IconPremium;
