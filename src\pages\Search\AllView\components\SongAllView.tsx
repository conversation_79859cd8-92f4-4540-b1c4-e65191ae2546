import SongItem from "@components/SongItem";
import {useDispatch} from "react-redux";
import {playSingleSong} from "@redux/slices/PlayerSlice";
import {ISong} from "src/types";

interface IAllViewSongClickProp {
  data: ISong[];
}

export function SongAllView({data}: IAllViewSongClickProp) {
  const dispatch = useDispatch();

  return (
    <div className="max-sm:-mx-2">
      {data?.map((song, index) => (
        <SongItem
          song={song}
          key={`search_song_all_${song?.id}`}
          left={
            <div className="flex gap-x-6 items-center mr-6" key={index}>
              <div className="text-base text-center lg:w-10 md:w-8 sm:w-6 w-4 text-white font-normal">
                {index + 1}
              </div>
            </div>
          }
          handlePlayMusic={() => {
            dispatch(playSingleSong(song));
          }}
        />
      ))}
    </div>
  );
}
