import {ICreatePlaylist} from "@api/ApiCMSPlaylist";
import GlobalButton from "@components/ButtonGlobal";
import ImageCropper from "@components/ImageCropper";
import TextFieldCustomer from "@components/TextFieldCustomer";
import {Dialog, DialogActions, DialogContent, DialogTitle} from "@mui/material";
import {useMutation} from "@tanstack/react-query";
import {ErrorMessage, Formik} from "formik";
import {useMemo} from "react";
import {useTranslation} from "react-i18next";
import {toast} from "react-toastify";
import {IPlaylist} from "src/types";
import * as Yup from "yup";

interface AddPlaylistProps {
  selectedItem?: IPlaylist;
  open: boolean;
  onClose: () => void;
  refetch: () => void;
  createApi: (body: ICreatePlaylist) => Promise<void>;
  updateApi: (body: {id: string} & ICreatePlaylist) => Promise<void>;
}

export default function AddPlaylistModal({
  selectedItem,
  open,
  onClose,
  refetch,
  createApi,
  updateApi,
}: AddPlaylistProps) {
  const {t} = useTranslation();

  const initialValues: ICreatePlaylist = useMemo(() => {
    if (selectedItem)
      return {
        name: selectedItem.name,
        genreIds: selectedItem?.genres?.map((genre) => ({
          id: genre.id,
          label: genre.name,
        })),
        themeIds: selectedItem?.themes?.map((theme) => ({
          id: theme.id,
          label: theme.name,
        })),
        releaseDate: selectedItem.releaseDate,
        description: selectedItem.description,
        image: undefined,
      };
    return {
      name: "",
      themeIds: [],
      genreIds: [],
      releaseDate: "",
      description: "",
      image: undefined,
    };
  }, [selectedItem]);

  const validationSchema = Yup.object({
    name: Yup.string().required(
      t("validation.field_is_require", {
        field: t("cms.playlist.playlist_name"),
      }),
    ),
    image: Yup.mixed().test(
      "size",
      t("validation.file_size_limit_exceed", {size: "10MB"}),
      (file) => {
        if (!file) return true;
        return file && (file as File).size < 10 * 1024 * 1024;
      },
    ),
  });
  const isEdit = selectedItem !== undefined;

  const {mutateAsync: createPlaylistMutationAsync, isPending: isCreating} =
    useMutation({
      mutationFn: createApi,
      onSuccess: () => {
        toast.success(t("common.add_successfully"));
        refetch();
        onClose();
      },
    });

  const {mutateAsync: updatePlaylistMuatationAsync, isPending: isUpdating} =
    useMutation({
      mutationFn: updateApi,
      onSuccess: () => {
        toast.success(t("common.update_successfully"));
        refetch();
        onClose();
      },
    });

  const handleSubmit = async (values: ICreatePlaylist) => {
    if (isEdit) {
      await updatePlaylistMuatationAsync({
        id: selectedItem.id,
        ...values,
      });
    } else {
      await createPlaylistMutationAsync(values);
    }
  };

  return (
    <Dialog
      open={open}
      onClose={() => {
        if (!(isCreating || isUpdating)) {
          onClose?.();
        }
      }}
      fullWidth
    >
      <Formik
        initialValues={initialValues}
        onSubmit={handleSubmit}
        validationSchema={validationSchema}
        enableReinitialize
      >
        {({handleChange, values, handleSubmit, setFieldValue}) => {
          return (
            <>
              <DialogTitle>
                {isEdit ? t("common.update") : t("common.add")}
                {t("common.playlist")}
              </DialogTitle>

              <DialogContent className="flex flex-col gap-y-4">
                <div>
                  <label className="block  text-sm font-bold text-gray-700 pb-2 pt-2">
                    {t("cms.playlist.cover")}
                    <span className="text-red-600"> *</span>
                  </label>
                  <ImageCropper
                    onChange={(file) => {
                      setFieldValue("image", file);
                    }}
                    className="w-[120px] aspect-square object-cover"
                    initialImageUrl={selectedItem?.images?.DEFAULT}
                  />
                  <div className="text-red-500 text-sm">
                    <ErrorMessage name="image" />
                  </div>
                </div>

                <div>
                  <TextFieldCustomer
                    name="name"
                    required
                    value={values.name}
                    onChange={handleChange("name")}
                    label={t("cms.playlist.playlist_name")}
                    placeholder={t("cms.playlist.playlist_name")}
                  />

                  <div className="text-red-500 text-sm">
                    <ErrorMessage name="name" />
                  </div>
                </div>

                <div className="flex flex-col gap-2">
                  <label className="text-sm font-bold text-gray-700">
                    {t("cms.playlist.des_playlist")}
                  </label>
                  <textarea
                    name="description"
                    value={values.description}
                    onChange={handleChange}
                    placeholder={t("cms.playlist.des_playlist")}
                    className="border border-gray-300 p-2 text-sm w-full"
                  />
                </div>
              </DialogContent>

              <DialogActions>
                <GlobalButton
                  onClick={onClose}
                  text={t("common.cancel")}
                  color="white"
                  className="w-20"
                  textClassName="text-[#000000D9]"
                  disabled={isCreating || isUpdating}
                />
                <GlobalButton
                  text={t("common.confirm")}
                  className="w-30"
                  onClick={handleSubmit}
                  isLoading={isCreating || isUpdating}
                  disabled={isCreating || isUpdating}
                />
              </DialogActions>
            </>
          );
        }}
      </Formik>
    </Dialog>
  );
}
