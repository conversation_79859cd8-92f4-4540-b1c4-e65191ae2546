import {SVGProps} from "react";

function IconPlaylist(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={20}
      height={20}
      fill="none"
      {...props}
    >
      <g clipPath="url(#a)">
        <path
          fill={props.fill || "currentColor"}
          fillRule="evenodd"
          d="m15.33 2.056 2.986.995a1 1 0 1 1-.632 1.898L16 4.387V15.5a3.5 3.5 0 1 1-2-3.163V3.014c0-.69.675-1.177 1.33-.958ZM12.5 14a1.5 1.5 0 1 0 0 3 1.5 1.5 0 0 0 0-3ZM6 15a1 1 0 0 1 0 2H2a1 1 0 1 1 0-2h4Zm2-6a1 1 0 1 1 0 2H2a1 1 0 1 1 0-2h6Zm3-6a1 1 0 0 1 .117 1.993L11 5H2a1 1 0 0 1-.117-1.993L2 3h9Z"
          clipRule="evenodd"
        />
      </g>
      <defs>
        <clipPath id="a">
          <path fill={props.fill || "currentColor"} d="M0 0h20v20H0z" />
        </clipPath>
      </defs>
    </svg>
  );
}

export default IconPlaylist;
