import {SVGProps} from "react";

interface IconArrowDownProps extends SVGProps<SVGSVGElement> {
  color?: string;
}
const ICArrowDown = ({color, ...props}: IconArrowDownProps) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    className="w-4 h-4"
    viewBox="0 0 24 24"
    fill="none"
    {...props}
  >
    <path
      fill={color || "red"}
      d="m19.707 14.707-6.999 7a1 1 0 0 1-1.416 0l-6.999-7a.999.999 0 1 1 1.414-1.414L11 18.586V3a1 1 0 1 1 2 0v15.586l5.293-5.293a.999.999 0 1 1 1.414 1.414Z"
    />
  </svg>
);
export default ICArrowDown;
