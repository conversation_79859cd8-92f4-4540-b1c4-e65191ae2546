import store from "@redux/store";
import {IBasePlayer, EPlayerType} from "./types";
import {t} from "i18next";
import {changePlayerType, setSuggestList} from "@redux/slices/PlayerSlice";
import {convertPlayerTime} from "src/utils/timeUtils";
import {ESongType, ISong} from "src/types";
import ApiPlayMusic from "@api/ApiPlayMusic";

type IPlayerLocalStorage = {
  PLAYER_VOLUME: {
    volume: number;
    muted: boolean;
  };
  PLAYER_POSITION: {
    position: number;
    player: EPlayerType;
  };
};

type IPlayerLocalStorageKey = keyof IPlayerLocalStorage;
type IPlayerLocalStorageValue<T extends IPlayerLocalStorageKey> =
  IPlayerLocalStorage[T];

export default class PlayerUtil {
  static getLocalValue<T extends IPlayerLocalStorageKey>(
    key: T,
  ): IPlayerLocalStorageValue<T> {
    const value = localStorage.getItem(key);
    if (value) {
      return JSON.parse(value);
    }
    return {} as IPlayerLocalStorageValue<T>;
  }

  static setLocalValue<T extends IPlayerLocalStorageKey>(
    key: T,
    value: IPlayerLocalStorageValue<T>,
  ) {
    return localStorage.setItem(key, JSON.stringify(value));
  }

  private static _instance: PlayerUtil | null = null;
  private players: IBasePlayer[];
  private currentPlayer: IBasePlayer;
  lyricTimeLines: number[] = [];

  static get instance(): PlayerUtil {
    if (this._instance === null)
      throw new Error("Audio instance has not been initialized!");
    return this._instance;
  }

  private constructor(players: IBasePlayer[]) {
    this.players = players;
    const currentPlayerType = store.getState().player.playerType;
    this.currentPlayer = players[0];
    this.changePlayer(currentPlayerType);
    const {volume = 0.5, muted = false} =
      PlayerUtil.getLocalValue("PLAYER_VOLUME");
    for (const player of players) {
      player.setVolume(volume);
      if (muted) {
        player.mute();
      } else {
        player.unMute();
      }
    }

    setInterval(() => {
      PlayerUtil.setLocalValue("PLAYER_POSITION", {
        position: this.currentPlayer.currentTime,
        player: this.currentPlayer.type,
      });
    }, 3000);
  }

  static initialize(players: IBasePlayer[]) {
    this._instance = new PlayerUtil(players);
  }

  private changePlayer(playerType: EPlayerType) {
    const player = this.players.find((player) => player.type === playerType);
    if (player && player.type !== this.currentPlayer.type) {
      if (!this.currentPlayer.paused) {
        this.currentPlayer.pause();
      }
      this.currentPlayer.clear();
      this.currentPlayer = player;
    }
    store.dispatch(changePlayerType(playerType));
  }

  changeVolume(volume?: number, muted?: boolean) {
    for (const player of this.players) {
      if (volume !== undefined) {
        player.setVolume(volume);
      }
      if (muted !== undefined) {
        if (muted) player.mute();
        else player.unMute();
      }
    }
    const {volume: oldVolume, muted: oldMuted} =
      PlayerUtil.getLocalValue("PLAYER_VOLUME");
    PlayerUtil.setLocalValue("PLAYER_VOLUME", {
      volume: volume !== undefined ? volume : oldVolume,
      muted: muted !== undefined ? muted : oldMuted,
    });
  }

  get ready() {
    return this.currentPlayer.ready;
  }

  get type() {
    return this.currentPlayer.type;
  }

  get currentTime() {
    return this.currentPlayer.currentTime;
  }

  get duration() {
    return this.currentPlayer.duration;
  }

  get volume() {
    return this.currentPlayer.volume;
  }

  get muted() {
    return this.currentPlayer.muted;
  }

  get paused() {
    return this.currentPlayer.paused;
  }

  loadSong(songData: ISong) {
    const playerType =
      songData.type === ESongType.YOUTUBE
        ? EPlayerType.YOUTUBE
        : EPlayerType.CORE;

    if (this.currentPlayer.type !== playerType) {
      this.changePlayer(playerType);
    }
    this.currentPlayer.loadSong(songData);
    ApiPlayMusic.getSuggestSongs(songData?.urlSlug ?? "", {
      page: 0,
      pageSize: 10,
    }).then((songs) => {
      store.dispatch(setSuggestList(songs));
    });
  }

  isSongPlaying(data: ISong) {
    return this.currentPlayer.isSongPlaying(data);
  }

  seek(time: number) {
    this.currentPlayer.seek(time);
  }
  play() {
    this.currentPlayer.play();
  }
  pause() {
    this.currentPlayer.pause();
  }

  clear() {
    this.currentPlayer.clear();
  }

  private setLyricsWithTimeline(lrcUrl: string) {
    fetch(lrcUrl)
      .then((res) => res.text())
      .then((lyricsWithTimeLine) => {
        if (lyricsWithTimeLine) {
          const lyricLines = lyricsWithTimeLine
            .split(/\n/)
            .map((str) => str.trim())
            .map((str) => {
              const matches = str.match(
                /\[(\d{2}):(\d{2}\.\d{2})\]\s*([^\n]+)/,
              );
              if (matches) {
                return {
                  start: Number(matches[1]) * 60 + Number(matches[2]),
                  text: matches[3],
                };
              }
              return null;
            })
            .filter((line) => line !== null);

          const lyricHtml = lyricLines
            .map(
              (line, index) =>
                `<span id="lyric-index-${index}">${line.text}</span>`,
            )
            .join("<br>");
          this.lyricTimeLines = lyricLines.map((l) => l.start);
          const lyricContainer = document.getElementById("lyric-line-wrapper");
          if (lyricContainer) {
            lyricContainer.innerHTML = lyricHtml;
          }
        }
      });
  }

  updateLyrics() {
    const currentSong = store.getState().player.currentSong;
    const lyricContainer = document.getElementById("lyric-line-wrapper");
    const notFoundText = t("common.lyrics_not_found");

    if (lyricContainer && currentSong) {
      if (currentSong.textLyrics) {
        lyricContainer.innerText = currentSong.textLyrics;
      } else {
        lyricContainer.innerHTML = notFoundText;
      }
      if (currentSong.lrcLyrics) {
        this.setLyricsWithTimeline(currentSong.lrcLyrics);
      }
    }
  }

  updatePositionText(_pos?: number) {
    const position = _pos || this.currentTime;
    const newPositionString = convertPlayerTime(position);
    const positionElements = document.querySelectorAll(".player-position");
    positionElements.forEach(
      (element) => (element.innerHTML = newPositionString),
    );
  }

  updateSlider() {
    const newSliderValue = Math.min(
      (this.currentTime / (this.duration || 1)) * 100,
      100,
    );
    const playerSliders: NodeListOf<HTMLInputElement> =
      document.querySelectorAll("input.player-slider");
    playerSliders.forEach((input) => {
      input.value = newSliderValue.toString();
      input.previousElementSibling?.setAttribute(
        "style",
        `width:${newSliderValue}%`,
      );
    });
  }
}
