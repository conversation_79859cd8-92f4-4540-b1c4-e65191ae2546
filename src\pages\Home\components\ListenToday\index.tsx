import {useTranslation} from "react-i18next";
import {SwiperSlide} from "swiper/react";
import ApiHome from "@api/ApiHome";
import Slider from "@components/Slider";
import SubTitleSkeleton from "@components/SubTitleSkeleton";
import Subtitle from "@components/Subtitle";
import QUERY_KEY from "@api/QueryKey";
import {useQuery} from "@tanstack/react-query";
import {IParamsDefault} from "src/types";
import SongCard from "./components/CardSong";
import SongCardHomeSkeleton from "@components/SongCardHomeSkeleton";

export default function ListenToday() {
  const {t} = useTranslation();

  const params: IParamsDefault = {
    page: 0,
    pageSize: 10,
  };

  const {
    data: dataSongs,
    isLoading,
    refetch,
  } = useQuery({
    queryKey: [QUERY_KEY.SONG.GET_SONGS_RECOMMENDED, params],
    queryFn: () => ApiHome.getSongsRecommended(params),
  });

  return (
    <>
      {isLoading && (
        <div className="flex flex-col gap-2 sm:gap-3 md:gap-4 lg:gap-5">
          <SubTitleSkeleton />
          <Slider slidesPerView={6.5}>
            {[...Array(7)].map((_, index) => (
              <SwiperSlide key={index} virtualIndex={index}>
                <SongCardHomeSkeleton />
              </SwiperSlide>
            ))}
          </Slider>
        </div>
      )}
      {dataSongs?.data && dataSongs?.data?.length > 0 && (
        <div className="flex flex-col gap-2 sm:gap-3 md:gap-4 lg:gap-5">
          <Subtitle
            subtitle={t("home.listen_today")}
            seeMore={false}
            refresh={true}
            handleClick={refetch}
          />
          <Slider slidesPerView={6.5} spaceBetween={16}>
            {dataSongs?.data?.map((item, index) => (
              <SwiperSlide
                key={`songs_recommended_${item.id}`}
                virtualIndex={index}
              >
                <SongCard data={item} index={index} />
              </SwiperSlide>
            ))}
          </Slider>
        </div>
      )}
    </>
  );
}
