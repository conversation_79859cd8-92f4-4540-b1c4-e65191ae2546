import {FormControl, RadioGroup, Radio, Box, Typography} from "@mui/material";
import {useTranslation} from "react-i18next";
import i18n from "@i18n/index";
import {useLanguageOptions} from "src/utils/global";
import {logEvent} from "src/utils/firebase";
import {IRootState} from "@redux/store";
import {useDispatch, useSelector} from "react-redux";
import {setLanguage} from "@redux/slices/GlobalSettingSlice";
import {useEffect} from "react";

export default function MultipleLanguage() {
  const {t} = useTranslation();
  const languageOptions = useLanguageOptions();
  const dispatch = useDispatch();
  const selectedLanguage = useSelector(
    (state: IRootState) => state.settings.language,
  );

  useEffect(() => {
    if (selectedLanguage !== i18n.language) {
      dispatch(setLanguage(i18n.language));
    }
  }, [dispatch, selectedLanguage]);

  const handleLanguageChange = (language: string) => {
    if (language !== selectedLanguage) {
      dispatch(setLanguage(language));
      i18n.changeLanguage(language);
      logEvent("change_language_settings", {
        setting_type: "language",
        new_value: language,
      });
    }
  };

  return (
    <div className="flex flex-col gap-[15px] items-start text-white">
      <span className="text-base font-medium">
        {t("common.setting.select_language")}
      </span>
      <Box
        sx={{
          width: "100%",
          color: "#fff",
          borderRadius: "4px",
          overflow: "hidden",
        }}
      >
        <FormControl fullWidth>
          <RadioGroup
            value={selectedLanguage || i18n.language}
            sx={{
              display: "flex",
              flexDirection: "column",
              gap: "2px",
            }}
          >
            {languageOptions.map((item, index) => (
              <Box
                key={index}
                sx={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between",
                  padding: "10px 15px",
                  borderRadius: "4px",
                  cursor: "pointer",
                  backgroundColor: "#FFFFFF12",
                }}
                onClick={() => handleLanguageChange(item.value)}
              >
                <Typography
                  sx={{
                    fontSize: "14px",
                    fontWeight: 400,
                    color: "#FFFFFF",
                  }}
                >
                  {item.label}
                </Typography>
                <Radio
                  value={item.value}
                  checked={selectedLanguage === item.value}
                  sx={{
                    "& .MuiSvgIcon-root": {
                      fontSize: 24,
                    },
                    "color": "#545454",
                    "&.Mui-checked": {
                      color: "#FF4319",
                    },
                  }}
                />
              </Box>
            ))}
          </RadioGroup>
        </FormControl>
      </Box>
    </div>
  );
}
