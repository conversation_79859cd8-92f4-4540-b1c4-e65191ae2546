import {Modal} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import {Trans, useTranslation} from "react-i18next";
import {useNavigate} from "react-router-dom";
import {IRootState} from "@redux/store";
import {useDispatch, useSelector} from "react-redux";
import {showModal} from "@redux/slices/GlobalModalSlice";
import {EGlobalModal} from "@redux/slices/GlobalModalSlice";
import {AccountType} from "../../types";
import {useEffect} from "react";
import {
  hiddenUpgradeModal,
  showUpgradeModal,
} from "@redux/slices/UpgradeAccountSlice";
import {setShouldViewUpgradePage} from "@redux/slices/UpgradeAccountSlice";

const ModalUpgrade = (): JSX.Element => {
  const {t} = useTranslation();
  const navigate = useNavigate();
  const {accessToken, userInfo} = useSelector(
    (state: IRootState) => state.user,
  );
  const {isModalOpened, shouldViewUpgradePage} = useSelector(
    (state: IRootState) => state.accType,
  );
  const dispatch = useDispatch();

  const handleUpgradeClick = () => {
    //khi ấn được button là chưa login hoặc chưa premium
    dispatch(hiddenUpgradeModal());
    if (!accessToken) {
      dispatch(showModal(EGlobalModal.AUTH_MODAL));
      //nếu chưa login -> bắt login và tạo một biến cho biết chuyển hướng /upgrade sau login
      dispatch(setShouldViewUpgradePage());
      return;
    }
    //nếu đã login nhưng chưa premium thì chuyển hướng đến trang upgrade
    navigate("/upgrade");
  };
  useEffect(() => {
    //nếu chưa login hoặc chưa premium thì bật
    if (
      !shouldViewUpgradePage &&
      (!accessToken || userInfo?.accountType === AccountType.FREE)
    ) {
      dispatch(showUpgradeModal());
    }
  }, [accessToken, userInfo?.accountType]);
  return (
    <>
      <Modal
        open={isModalOpened} //mặc định tắt
        onClose={() => {
          dispatch(hiddenUpgradeModal());
        }}
        aria-labelledby="modal-title"
        aria-describedby="modal-description"
      >
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 flex flex-col items-center justify-between sm:w-[444px] w-[90%] h-fit rounded-2xl outline-none border border-white border-opacity-30 bg-[linear-gradient(360deg,_#F58B00_-21.72%,_#FF4319_110.59%)]">
          <div className="relative">
            <div className="absolute top-6 right-6 rounded-[50%] bg-[rgba(255,255,255,0.28)] p-2 flex items-center justify-center">
              <CloseIcon
                className="text-white cursor-pointer"
                onClick={() => dispatch(hiddenUpgradeModal())}
                fontSize="small"
              />
            </div>
            <div className="p-4 sm:p-6" onClick={handleUpgradeClick}>
              <div className="flex items-center justify-center w-full">
                <img
                  className="h-[60px] object-contain"
                  src="/image/logo.png"
                  alt="Logo"
                />
              </div>
              <div className="flex items-center justify-center w-full h-fit">
                <img src="/image/Upgrade.png" alt="Upgrade now" />
              </div>
              <div className="flex flex-col items-center justify-center w-full gap-6 h-fit font-opensans">
                <h1 className="sm:text-[26px] text-[20px] font-bold text-white leading-9 text-center">
                  {t("common.upgrade_modal.title")}
                </h1>
                <div className="w-full">
                  <p className="sm:text-sm text-xs  text-center text-wrap text-white leading-[22px]">
                    <Trans
                      i18nKey="common.upgrade_modal.description"
                      components={{bold: <strong className="font-bold" />}}
                    />
                  </p>
                  <p className="sm:text-sm text-xs text-center text-wrap text-white leading-[22px]">
                    {t("common.upgrade_modal.note")}
                  </p>
                </div>

                <button className="sm:w-[366px] w-[90%] h-[60px] sm:px-7 px-1 py-1 bg-white text-[20px] text-[#FF4319] rounded-lg leading-6 text-center font-bold">
                  {t("common.upgrade_modal.button")}
                </button>
              </div>
            </div>
          </div>
        </div>
      </Modal>
    </>
  );
};
export default ModalUpgrade;
