interface IPropsInputOTP {
  values: {otp: string};
  setFieldValue: (field: string, value: any, shouldValidate?: boolean) => void;
  className?: string;
}

export default function InputOTP({
  values,
  setFieldValue,
  className,
}: IPropsInputOTP): JSX.Element {
  return (
    <div className="flex justify-center gap-4">
      {[0, 1, 2, 3].map((index) => (
        <input
          key={index}
          type="text"
          maxLength={1}
          value={values.otp[index] || ""}
          onChange={(e) => {
            const value = e.target.value.replace(/\D/g, "");
            const otpArray = values.otp.split("");
            otpArray[index] = value;
            setFieldValue("otp", otpArray.join(""));
          }}
          onKeyUp={(e) => {
            if (e.key === "Backspace" && index > 0 && !values.otp[index]) {
              const previousInput = document.getElementById(`otp-${index - 1}`);
              previousInput?.focus();
            } else if (index < 3 && values.otp[index]) {
              const nextInput = document.getElementById(`otp-${index + 1}`);
              nextInput?.focus();
            }
          }}
          id={`otp-${index}`}
          className={className}
        />
      ))}
    </div>
  );
}
