import IconHeart from "@components/Icon/IconHeart";
import clsx from "clsx";
import AuthButtonWrapper from "../AuthButtonWrapper";
import {IconButton} from "@mui/material";
import "./index.scss";
import {Mouse<PERSON>ventHandler} from "react";

interface ILikeButton {
  isLiked: boolean | undefined;
  songId?: string;
  action: MouseEventHandler;
  className?: string;
  loading?: boolean;
  classNameIcon?: string;
}

export default function LikeButton({
  songId,
  isLiked,
  action,
  className,
  loading,
  classNameIcon,
}: ILikeButton): JSX.Element {
  return (
    <AuthButtonWrapper action={action} className={className}>
      <IconButton loading={loading} disabled={loading} className="!p-1">
        <IconHeart
          className={clsx(
            "cursor-pointer like-button",
            songId && `song-${songId}-liking`,
            isLiked && "liked", // initial state
            classNameIcon,
          )}
        />
      </IconButton>
    </AuthButtonWrapper>
  );
}
