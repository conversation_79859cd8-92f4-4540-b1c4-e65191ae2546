import QUERY_KEY from "@api/QueryKey";
import {<PERSON><PERSON>, Dialog} from "@mui/material";
import {useQuery} from "@tanstack/react-query";
import {MouseEventHandler} from "react";
import {useTranslation} from "react-i18next";

interface ModalShareProps {
  open: boolean;
  onCancel: MouseEventHandler;
  lrcUrl: string | undefined;
}

export default function ModalLyric({open, onCancel, lrcUrl}: ModalShareProps) {
  const {t} = useTranslation();
  const fetchLyricData = (lrcUrl: string) => {
    return new Promise<string[]>((resolve, reject) => {
      fetch(lrcUrl)
        .then((response) => {
          if (!response.ok) {
            reject(new Error("Failed to fetch lyrics"));
          }
          return response.text();
        })
        .then((data) => {
          const lyrics = data
            .split(/\n/)
            .map((str) => str.trim())
            .map((str) => {
              const matches = str.match(
                /\[(\d{2}):(\d{2}\.\d{2})\]\s*([^\n]+)/,
              );
              if (matches) {
                return matches[3];
              }
              return null;
            })
            .filter((line) => line !== null);
          resolve(lyrics);
        });
    });
  };
  const {
    data: lyricData,
    isLoading,
    isFetching,
    isError,
  } = useQuery({
    queryKey: [QUERY_KEY.SONG.GET_LYRIC_SONG, lrcUrl],
    queryFn: () => fetchLyricData(lrcUrl as string),
    enabled: !!lrcUrl && open,
  });
  return (
    <Dialog
      open={open}
      onClose={onCancel}
      maxWidth="md"
      fullWidth
      classes={{paper: "rounded-lg"}}
      PaperProps={{
        style: {
          background: "#1C1717",
          color: "white",
          boxSizing: "border-box",
          borderRadius: "12px",
          width: "540px",
          boxShadow: "0px_10px_15px_0px_#1B28361A",
        },
      }}
    >
      <div className="flex flex-col p-[20px] gap-[15px]">
        <p className="text-[20px] font-bold uppercase">{t("common.lyric")}</p>
        <div className="h-[250px] px-[14px] py-3 border border-solid border-[rgba(255,255,255,0.1)] rounded-[4px] overflow-y-auto">
          {isLoading || isFetching ? (
            <div className="flex items-center gap-2">
              <p>{t("common.loading")}</p>

              <div className="w-5 h-5 border-2 border-white border-t-transparent border-solid rounded-full animate-spin"></div>
            </div>
          ) : isError ? (
            <p>{t("home.error_fetching")}</p>
          ) : lyricData ? (
            lyricData.map((line, index) => (
              <p key={index} className="text-[14px] text-white leading-[28px]">
                {line.split("]")[1] || line}
              </p>
            ))
          ) : (
            <p>{t("common.lyrics_not_found")}</p>
          )}
        </div>
        <div className="flex justify-between items-center">
          <p className="cursor-pointer">{t("common.contribute_lyric")}</p>
          <Button
            variant="contained"
            sx={{
              borderRadius: "999px",
              padding: "9px 24px",
              backgroundColor: "rgba(255, 255, 255, 0.3)",
            }}
            onClick={onCancel}
          >
            {t("common.close")}
          </Button>
        </div>
      </div>
    </Dialog>
  );
}
