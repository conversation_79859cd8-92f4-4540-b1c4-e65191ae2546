import IconMp3 from "@components/Icon/IconMp3";
import {Link} from "@mui/material";
import {useTranslation} from "react-i18next";
import {AudioQualityTypeEnum, ISong} from "src/types";
import {useAudioQualityOptions} from "src/utils/global";

export default function OtherInfo({songData}: {songData: ISong | undefined}) {
  const {t} = useTranslation();

  const songHq = songData?.audios?.filter(
    (item) => item?.type === AudioQualityTypeEnum.KBPS_320,
  );

  const songNormal = songData?.audios?.filter(
    (item) => item?.type === AudioQualityTypeEnum.KBPS_128,
  );

  const audioQualityOptions = useAudioQualityOptions();

  const qualityLabel128 =
    audioQualityOptions.find((item) => item.value === songNormal![0]?.type)
      ?.label || "";

  const qualityLabel320 =
    audioQualityOptions.find((item) => item.value === songHq![0]?.type)
      ?.label || "";

  return (
    <div className="flex justify-between py-2.5 px-4 bg-[#F6F6F6] border border-[#E5E5E5] rounded-xl">
      <div className="flex flex-col gap-2.5 w-full">
        <div className="flex gap-10">
          <span className="text-[#656565] text-sm min-w-44">
            {t("cms.song.age_limit")}
          </span>
          <span className="text-sm font-semibold text-gray-default">
            {songData?.ageLimit || "-"}
          </span>
        </div>
        <div className="flex gap-10">
          <span className="text-[#656565] text-sm min-w-44">
            {t("cms.song.song_lyrics")}
          </span>
          <div
            className="text-sm text-gray-default max-h-52 overflow-y-auto italic w-full"
            dangerouslySetInnerHTML={{
              __html: songData?.textLyrics?.replace(/\n/g, "<br />") || "-",
            }}
          />
        </div>
        <div className="flex gap-10">
          <span className="text-[#656565] text-sm min-w-44">
            {t("cms.song.song_file")}
          </span>
          {(songNormal?.length ?? 0 > 0) ? (
            <Link
              href={songNormal && songNormal[0]?.url}
              className="flex gap-3 items-center w-full"
              target="_blank"
              sx={{
                paddingY: "8px",
                paddingX: "12px",
                fontSize: "14px",
                fontWeight: 600,
                textDecoration: "none",
                backgroundColor: "#FFF",
                borderRadius: "8px",
                boxShadow: "0px 2px 12px 0px #0000001A",
              }}
            >
              <IconMp3 className="flex-shrink-0" />
              <div className="flex flex-col">
                <span className="text-gray-default">
                  {songNormal![0]?.url?.split("/")?.pop()}
                </span>
                <span className="text-sm font-normal text-[#B0B0B0]">
                  {qualityLabel128}
                </span>
              </div>
            </Link>
          ) : (
            "-"
          )}
        </div>
        <div className="flex gap-10">
          <span className="text-[#656565] text-sm min-w-44">
            {t("cms.song.song_file_high_quality")}
          </span>
          {(songHq?.length ?? 0 > 0) ? (
            <Link
              href={songHq && songHq[0]?.url}
              className="flex gap-3 items-center w-full"
              target="_blank"
              sx={{
                paddingY: "8px",
                paddingX: "12px",
                fontSize: "14px",
                fontWeight: 600,
                textDecoration: "none",
                backgroundColor: "#FFF",
                borderRadius: "8px",
                boxShadow: "0px 2px 12px 0px #0000001A",
              }}
            >
              <IconMp3 className="flex-shrink-0" />
              <div className="flex flex-col">
                <span className="text-gray-default">
                  {songHq![0]?.url?.split("/")?.pop()}
                </span>
                <span className="text-sm font-normal text-[#B0B0B0]">
                  {qualityLabel320}
                </span>
              </div>
            </Link>
          ) : (
            "-"
          )}
        </div>
      </div>
    </div>
  );
}
