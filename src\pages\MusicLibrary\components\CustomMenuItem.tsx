import ModalAddToPlaylist from "@components/ModalAddToPlaylist";
import {MenuItem} from "@mui/material";
import {useState} from "react";
import {useTranslation} from "react-i18next";
import {ISong} from "src/types";

interface ICustomMenuItemProps {
  data: ISong;
}

export default function CustomMenuItem({
  data,
}: ICustomMenuItemProps): JSX.Element {
  const {t} = useTranslation();
  const [openAddToPlaylist, setOpenAddToPlaylist] = useState(false);

  const MENU_ITEMS = [
    {
      label: t("playlist.add_to_playlist"),
      onClick: () => {},
    },
    {
      label: t("playlist.next_play"),
      onClick: () => {},
    },
    {
      label: t("playlist.play_similar_content"),
      onClick: () => {},
    },
    {
      label: t("playlist.set_up_ringtone"),
      onClick: () => {},
    },
    {
      label: t("common.menu.add_to_playlist"),
      onClick: () => {
        setOpenAddToPlaylist(true);
      },
    },
    {
      label: t("common.copy_link"),
      onClick: () => {},
    },
    {
      label: t("common.share"),
      onClick: () => {},
    },
  ];

  return (
    <div>
      {MENU_ITEMS.map((item, index) => (
        <MenuItem
          key={index}
          style={{marginBottom: 8}}
          onClick={() => item.onClick()}
        >
          {item.label}
        </MenuItem>
      ))}
      <ModalAddToPlaylist
        open={openAddToPlaylist}
        onClose={() => setOpenAddToPlaylist(false)}
        songData={data}
      />
    </div>
  );
}
