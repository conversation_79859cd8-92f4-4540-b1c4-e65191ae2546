import ICMusicalNote from "@components/Icon/ICMusicalNote";
import {IRootState} from "@redux/store";
import {useEffect} from "react";
import {useSelector} from "react-redux";
import "./index.scss";
import {useTranslation} from "react-i18next";
import PlayerUtil from "src/core/PlayerUtil";
import Player<PERSON><PERSON>enerHandler from "src/core/PlayerListenerHandler";

interface ILyricsSong {
  className?: string;
}

export default function LyricsSong({className}: ILyricsSong): JSX.Element {
  const {t} = useTranslation();
  const {currentSong} = useSelector((state: IRootState) => state.player);

  useEffect(() => {
    PlayerUtil.instance.updateLyrics();
  }, [currentSong]);

  useEffect(() => {
    PlayerListenerHandler.setUpFollowLyrics();
  }, []);

  return (
    <div
      className={`text-3xl ${className} hide-scrollbar`}
      id="lyrics-container"
    >
      <div className="hidden md:flex text-lg md:text-2xl font-semibold text-white opacity-80 flex-row gap-2 justify-start items-center pt-2 pb-3 max-[834px]:justify-center">
        <ICMusicalNote /> {t("common.lyrics")}:
      </div>
      <div
        id="lyric-line-wrapper"
        className="text-lg leading-7 md:text-[28px] md:leading-[62px] text-[#FFFFFF63]"
      ></div>
    </div>
  );
}
