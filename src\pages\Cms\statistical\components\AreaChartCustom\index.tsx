import {
  AreaChart,
  Area,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianG<PERSON>,
  <PERSON><PERSON><PERSON>,
  ResponsiveContainer,
} from "recharts";
import {shortenNumber} from "src/utils/numberUtils";

interface AreaChartCustomProps {
  data: {
    name: string;
    value: number;
  }[];
  linearGradientColor?: {
    id: string;
    linear: {offset: string; color: string; stopOpacity?: number}[];
  };
  labels: {
    x: string;
    y: string;
  };
}

function AreaChartCustom({
  data,
  linearGradientColor,
  labels,
}: AreaChartCustomProps) {
  const customTickOfXAxis = ({
    x = 0,
    y = 0,
    payload = {} as {value?: string},
    index = 0,
  }) => {
    const value = payload.value || "";
    const length = data.length;
    const maxLength = length <= 10 ? 10 : length <= 20 ? 5 : 2;
    const name = value.length > maxLength ? value.slice(0, maxLength) : value;

    return (
      <g
        key={`tick-${value}-${index}`}
        transform={`translate(${x}, ${y + 10})`}
      >
        <title>{value}</title>
        <text textAnchor="middle" fill="#707070" fontSize={12}>
          {name}
        </text>
      </g>
    );
  };

  return (
    <div style={{width: "100%", height: 500}}>
      <ResponsiveContainer>
        <AreaChart
          data={data}
          margin={{top: 20, right: 20, left: 20, bottom: 30}}
        >
          <defs>
            <linearGradient
              id={linearGradientColor?.id}
              x1="0"
              y1="0"
              x2="0"
              y2="1"
            >
              {linearGradientColor?.linear?.map((item) => (
                <stop
                  offset={item.offset}
                  stopColor={item.color}
                  stopOpacity={item.stopOpacity}
                  key={`area_chart_${item.offset}-${item.color}`}
                />
              ))}
            </linearGradient>
          </defs>

          <CartesianGrid strokeDasharray="3 3" vertical={false} />

          <XAxis
            dataKey="name"
            tick={customTickOfXAxis}
            interval={0}
            scale="point"
            padding={{left: 10, right: 10}}
            label={{
              value: labels.x,
              position: "insideBottom",
              offset: -25,
              style: {fill: "#707070"},
            }}
          />

          <YAxis
            domain={[0, "dataMax"]}
            label={{
              value: labels.y,
              angle: -90,
              position: "insideLeft",
              style: {fill: "#707070"},
            }}
            tickFormatter={(val) => `${shortenNumber(val)}`}
          />

          <Tooltip
            formatter={(value) => [
              shortenNumber((value || 0) as number),
              labels.y,
            ]}
            labelFormatter={(label) => `${labels.x}: ${label}`}
          />

          <Area
            type="monotone"
            dataKey="value"
            stroke={linearGradientColor?.linear?.[0].color}
            strokeWidth={2}
            fill={`url(#${linearGradientColor?.id})`}
            fillOpacity={1}
            activeDot={{r: 5}}
            dot={{
              r: 4,
              stroke: linearGradientColor?.linear?.[0].color,
              fill: "#fff",
            }}
          />
        </AreaChart>
      </ResponsiveContainer>
    </div>
  );
}

export default AreaChartCustom;
