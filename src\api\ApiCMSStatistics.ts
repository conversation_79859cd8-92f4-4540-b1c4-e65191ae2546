import {
  IParamsDefault,
  IPlaylist,
  ISong,
  EStatisticsDateRangeType,
} from "src/types";
import {fetcher, fetcherWithMetadata, IDataWithMeta} from "./Fetcher";

const path = {
  getTotal: "/cms/statistics/total",
  getSystemVisit: "/cms/statistics/user-access",
  getSystemListen: "/cms/statistics/total-listens",
  getSongListens: "/cms/statistics/song-listens",
  getPlaylistListens: "/cms/statistics/playlist-listens",
};

export interface ISystemVisitAndListenParams extends IParamsDefault {
  typeStatistic: EStatisticsDateRangeType;
}

interface IGetStatisticsTotalResponse {
  totalAlbums: number;
  totalArtists: number;
  totalPlaylists: number;
  totalSongs: number;
  totalTop100: number;
  totalUsers: number;
  totalUsersOnline: number;
}

type ISystemVisitAndListenResponse = Array<{date: string; counts: number}>;

function getTotal() {
  return fetcher<IGetStatisticsTotalResponse>({
    url: path.getTotal,
  });
}

function getSystemVisit(
  params: ISystemVisitAndListenParams,
): Promise<IDataWithMeta<ISystemVisitAndListenResponse>> {
  return fetcherWithMetadata({
    url: path.getSystemVisit,
    params,
  });
}

function getSystemListen(
  params: ISystemVisitAndListenParams,
): Promise<IDataWithMeta<ISystemVisitAndListenResponse>> {
  return fetcherWithMetadata({
    url: path.getSystemListen,
    params,
  });
}

function getSongListens(
  params: IParamsDefault,
): Promise<IDataWithMeta<ISong[]>> {
  return fetcherWithMetadata({
    url: path.getSongListens,
    method: "get",
    params,
  });
}

function getPlaylistListens(
  params: IParamsDefault,
): Promise<IDataWithMeta<IPlaylist[]>> {
  return fetcherWithMetadata({
    url: path.getPlaylistListens,
    method: "get",
    params,
  });
}

export default {
  getTotal,
  getSystemListen,
  getSystemVisit,
  getSongListens,
  getPlaylistListens,
};
