import Slider from "@components/Slider";
import {SwiperSlide} from "swiper/react";
import {IPlaylist} from "src/types";
import CommonAlbumCard from "@components/CommonAlbumCard";

interface IAllViewPlaylistClickProp {
  data: IPlaylist[];
  haveLayer?: boolean;
}

export function PlaylistAllView({
  data,
  haveLayer = true,
}: IAllViewPlaylistClickProp) {
  return (
    <Slider slidesPerView={5} spaceBetween={16}>
      {data?.map((item, index) => {
        return (
          <SwiperSlide
            key={`search_playlist_all_${item.id}`}
            virtualIndex={index}
          >
            <CommonAlbumCard data={item} haveLayer={haveLayer} />
          </SwiperSlide>
        );
      })}
    </Slider>
  );
}
