import {IUserInfo} from "src/types";
import {fetcher, fetcherWithMetadata, IDataWithMeta} from "./Fetcher";

export interface IGetUserCmsParams {
  search?: string;
  updatedAt?: string;
  sortField?: string;
  sortDirection?: string;
  page?: number;
  pageSize?: number;
  status?: string;
  role?: string;
  artistType?: string;
  lastLoggedIn?: string;
}

export interface IBodyUpdateUser {
  id?: string;
  username?: string;
  phone?: string;
  fullName?: string;
  email?: string;
  identificationNumber?: string;
  gender?: string;
  role?: string;
  birthday?: string;
  status?: string;
}

const path = {
  getUserList: "cms/user",
};

function getUserCms(
  params: IGetUserCmsParams,
): Promise<IDataWithMeta<IUserInfo[]>> {
  return fetcherWithMetadata<IUserInfo[]>(
    {
      url: path.getUserList,
      method: "get",
      params,
    },
    {
      displayError: false,
    },
  );
}

function updateUser(body: IBodyUpdateUser): Promise<any> {
  return fetcher(
    {
      url: `${path.getUserList}/${body?.id}`,
      method: "patch",
      data: body,
    },
    {
      displayError: false,
    },
  );
}

export default {
  getUserCms,
  updateUser,
};
