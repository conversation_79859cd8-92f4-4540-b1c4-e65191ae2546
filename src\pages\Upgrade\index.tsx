import {IMusicPackage} from "src/types";
import CardUpgrade from "./component/CardUpgrade";
import {useTranslation} from "react-i18next";
import ApiMusicPackage from "src/api/ApiMusicPackage";
import {useEffect, useMemo, useRef, useState} from "react";
import {useDispatch, useSelector} from "react-redux";
import {updateUserInfo} from "@redux/slices/UserSlice";
import {IRootState} from "@redux/store";

export default function UpgradePage() {
  const {t} = useTranslation();
  const [openPopup, setOpenPopup] = useState(false);
  const popupRef = useRef<Window | null>(null);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const dispatch = useDispatch();
  const {userInfo} = useSelector((state: IRootState) => state.user);

  const windowWidth = useMemo(() => {
    return window.innerWidth
      ? window.innerWidth
      : document.documentElement.clientWidth
        ? document.documentElement.clientWidth
        : screen.width;
  }, []);

  const windowHeight = useMemo(() => {
    return window.innerHeight
      ? window.innerHeight
      : document.documentElement.clientHeight
        ? document.documentElement.clientHeight
        : screen.height;
  }, []);

  const popupWidth = Math.min(1200, windowWidth - 20);
  const popupHeight = Math.min(800, windowHeight - 20);

  const left = windowWidth / 2 - popupWidth / 2;
  const top = windowHeight / 2 - popupHeight / 2;

  const handleClickUpgradeButton = () => {
    ApiMusicPackage.getUrlRegister().then((res) => {
      if (res.data?.url) {
        setOpenPopup(true);
        popupRef.current = window.open(
          res.data.url,
          "_blank",
          `width=${popupWidth},height=${popupHeight},top=${top},left=${left},resizable=yes,scrollbars=yes`,
        );
      }
    });
  };

  useEffect(() => {
    if (openPopup) {
      intervalRef.current = setInterval(() => {
        if (popupRef.current && popupRef.current.closed) {
          setOpenPopup(false);
          ApiMusicPackage.checkRegister().then((res) => {
            if (res.allow) {
              dispatch(
                updateUserInfo({
                  userInfo: {
                    ...userInfo,
                    accountType: 1,
                  },
                }),
              );
            }
          });
        }
      }, 1000);
    }
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [openPopup]);

  const musicPackages: IMusicPackage[] = [
    {
      id: "1",
      type: 0,
      price: 1000,
      specialPrice: 500,
      features: [`${t("common.upgrade_page.no_data")}`],
      priceUnit: "kip",
    },
    {
      id: "2",
      type: 1,
      price: 5000,
      features: [`${t("common.upgrade_page.no_data")}`],
      priceUnit: "kip",
    },
    {
      id: "3",
      type: 2,
      price: 20000,
      features: [`${t("common.upgrade_page.no_data")}`],
      priceUnit: "kip",
    },
  ];
  return (
    <>
      <div className="bg-transparent relative h-full flex flex-col items-center justify-center md:gap-20 gap-10 py-6">
        <div className="relative z-10 flex flex-col items-center justify-center gap-5 px-2">
          <div className="w-fit h-fit lg:text-[32px] text-3xl font-semibold text-white tracking-[-0.21px] text-center">
            {t("common.upgrade_page.title_upgrade_page")}
          </div>
          <div className="w-fit h-fit lg:text-xl text-lg font-normal text-white tracking-[-0.21px] text-center">
            {t("common.upgrade_page.subtitle_upgrade_page")}
          </div>
        </div>
        <div className="relative w-[80%] z-10 grid items-center justify-items-center md:grid-cols-[repeat(auto-fit,minmax(400px,1fr))] grid-cols-1 gap-6 px-4">
          {musicPackages.map((item) => (
            <CardUpgrade
              data={item}
              key={item.id}
              onClick={handleClickUpgradeButton}
            />
          ))}
        </div>
        <div className="absolute md:-top-[69px] -top-20 left-2/4 -translate-x-2/4 w-full h-full bg-cover bg-center z-0">
          <img
            src="/image/background_login.png"
            alt="Background"
            className="object-cover w-full h-full"
          />
        </div>
        <div
          className="absolute -bottom-24 right-0 z-0 md:w-[45%] sm:w-[55%] w-[60%] md:aspect-[1/1] aspect-[1/2]"
          style={{
            background:
              "radial-gradient(90% 100% at 100% 100%, rgba(198, 0, 0, 0.52) 9%, rgba(96, 0, 0, 0) 100%)",
          }}
        ></div>
      </div>
    </>
  );
}
