import {SVGProps} from "react";

function IconCmsSend({
  width = "24",
  height = "24",
  ...props
}: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox={`0 0 ${width} ${height}`}
      className={props.className}
      fill="none"
      {...props}
    >
      <path
        d="M20.04 2.32246C21.056 1.96746 22.032 2.94346 21.677 3.95946L15.752 20.8895C15.367 21.9875 13.837 22.0495 13.365 20.9865L10.506 14.5545L14.53 10.5295C14.6625 10.3873 14.7346 10.1992 14.7312 10.0049C14.7277 9.81064 14.649 9.62526 14.5116 9.48784C14.3742 9.35043 14.1888 9.27172 13.9945 9.26829C13.8002 9.26486 13.6122 9.33698 13.47 9.46946L9.44498 13.4935L3.01298 10.6345C1.94998 10.1615 2.01298 8.63246 3.10998 8.24746L20.04 2.32246Z"
        fill={props.fill || "#039C00"}
      />
    </svg>
  );
}

export default IconCmsSend;
