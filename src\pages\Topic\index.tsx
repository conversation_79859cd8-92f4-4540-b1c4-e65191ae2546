import {PlaylistType} from "src/types";
import ThemesList from "./components/ThemesList";
import "./index.scss";
import TopGenres from "@components/TopGenres";
import HeaderTitle from "@components/HeaderTitle";
import {useTranslation} from "react-i18next";

const genres = ["Hot", "Mood", "Country"];

export default function Topic() {
  const {t} = useTranslation();
  return (
    <div className="flex flex-col gap-2 sm:gap-4 md:gap-6 lg:gap-8 pt-5 px-4 sm:px-6 md:px-8 pb-10">
      <HeaderTitle title={t("common.home_sidebar.topic")} />
      {genres?.map((genre) => <ThemesList key={genre} genreSlug={genre} />)}

      <TopGenres page={0} pageSize={5} playlistType={PlaylistType.PLAYLIST} />
    </div>
  );
}
