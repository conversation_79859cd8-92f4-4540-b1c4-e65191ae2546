import {useTranslation} from "react-i18next";
import {SwiperSlide} from "swiper/react";
import {useQuery} from "@tanstack/react-query";
import ApiHome from "@api/ApiHome";
import QUERY_KEY from "@api/QueryKey";
import Slider from "@components/Slider";
import Subtitle from "@components/Subtitle";
import SubTitleSkeleton from "@components/SubTitleSkeleton";
import {ISong} from "src/types";
import CommonYoutubeCard from "@components/CommonYoutubeCard";
import YoutubeCardSkeleton from "@components/YoutubeCardSkeleton";
import {useDispatch} from "react-redux";
import {playSingleSong} from "@redux/slices/PlayerSlice";

export default function TrendingYTB() {
  const {t} = useTranslation();
  const dispatch = useDispatch();

  const {data, isLoading} = useQuery({
    queryKey: [QUERY_KEY.SONG.GET_TRENDING_YOUTUBE],
    queryFn: () => ApiHome.getTopTrendingYoutube(),
    // staleTime: 5 * 60 * 1000,
  });

  return (
    <>
      {isLoading && (
        <div className="flex flex-col gap-2 sm:gap-3 md:gap-4 lg:gap-5">
          <SubTitleSkeleton />
          <Slider slidesPerView={5} spaceBetween={16}>
            {[...Array(5)].map((_, index) => (
              <SwiperSlide key={index} virtualIndex={index}>
                <YoutubeCardSkeleton />
              </SwiperSlide>
            ))}
          </Slider>
        </div>
      )}
      {data?.data && data?.data?.length > 0 && (
        <div className="flex flex-col gap-2 sm:gap-3 md:gap-4 lg:gap-5">
          <Subtitle subtitle={t("common.trending_ytb")} seeMore={false} />
          <Slider slidesPerView={5} spaceBetween={16}>
            {data?.data?.map((item: ISong, index: number) => {
              return (
                <SwiperSlide
                  key={`trending_youtube_${item.id}`}
                  virtualIndex={index}
                >
                  <CommonYoutubeCard
                    data={item}
                    handlePlayMusic={() => dispatch(playSingleSong(item))}
                  />
                </SwiperSlide>
              );
            })}
          </Slider>
        </div>
      )}
    </>
  );
}
