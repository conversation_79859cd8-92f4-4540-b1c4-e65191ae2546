import IconStar from "@components/Icon/IconStar";
import {Box, MenuItem, Select} from "@mui/material";
import {GridColDef, GridSortModel} from "@mui/x-data-grid";
import {AdapterDayjs} from "@mui/x-date-pickers/AdapterDayjs";
import {DatePicker} from "@mui/x-date-pickers/DatePicker";
import {LocalizationProvider} from "@mui/x-date-pickers/LocalizationProvider/LocalizationProvider";
import CmsTable from "@pages/Cms/components/CmsTable";
import SearchInput from "@pages/Cms/components/SearchInput";
import {useState} from "react";
import {useTranslation} from "react-i18next";
import {getPackageInfo, getStatusInfo} from "src/utils/global";
import {convertDateTime} from "src/utils/timeUtils";
import fakeData from "../fakeData";
import AccountDetailModal from "./AccountDetailModal";

export default function CmsAccountPremium() {
  const {t} = useTranslation();
  const [page, setPage] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const [searchText, setSearchText] = useState("");
  const [status] = useState("");
  const [, setSortModel] = useState<GridSortModel>([]);
  const [, setUpdateTime] = useState("");
  const [selectedUser, setSelectedUser] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleRowClick = (user: any) => {
    setSelectedUser(user);
    setIsModalOpen(true);
  };

  const packageOptions = [
    {value: "", label: t("cms.premium.package_type")},
    {value: 1, label: t("cms.premium.experience_package")},
    {value: 2, label: t("cms.premium.year_package")},
    {value: 3, label: t("cms.premium.promotion_package")},
  ];

  const getCmsUsers = fakeData;

  const tableRows = getCmsUsers?.map((item, index) => ({
    id: item?.id, // Using to get id to handle onclick
    ids: index + 1, // Using to show in the table
    username: item?.account,
    email: item?.email,
    status: item?.status,
    device: item?.device,
    report: item?.report,
    premiumType: item?.premiumType,
    extensionPeriod: item?.extensionPeriod,
    listDevice: item?.listDevice,
    statusHistory: item?.statusHistory,
  }));

  const columns: GridColDef[] = [
    {
      field: "ids",
      headerName: t("common.id"),
      width: 46,
      sortable: false,
      disableColumnMenu: true,
      headerAlign: "center",
      align: "center",
    },
    {
      field: "username",
      headerName: t("common.account"),
      flex: 1,
      minWidth: 150,
      sortable: false,
    },
    {
      field: "email",
      headerName: t("common.email"),
      flex: 1,
      minWidth: 150,
      sortable: false,
    },
    {
      field: "premiumType",
      headerName: t("cms.premium.package_type"),
      flex: 1,
      minWidth: 150,
      sortable: false,
      renderCell: (params) => {
        const {text, color} = getPackageInfo(params?.value);

        return (
          <div className="flex items-center gap-2">
            <IconStar color={color} />
            <div className="flex items-center leading-none my-auto">{text}</div>
          </div>
        );
      },
    },
    {
      field: "extensionPeriod",
      headerName: t("cms.premium.extension_period"),
      flex: 1,
      minWidth: 150,
      sortable: false,
      renderCell: (params) => <span>{convertDateTime(params?.value)}</span>,
    },
    {
      field: "status",
      headerName: t("common.account_status"),
      flex: 1,
      minWidth: 150,
      sortable: true,
      headerClassName: "sticky-header",
      cellClassName: "sticky-cell",
      renderCell: (params) => {
        const {colorStatus, textStatus} = getStatusInfo(params?.value);

        return (
          <div className="font-semibold">
            <span
              style={{color: colorStatus}}
              className="flex gap-1 items-center"
            >
              <span
                className="rounded-full w-1.5 aspect-square inline-block"
                style={{
                  backgroundColor: colorStatus,
                }}
              />
              &ensp;{textStatus}
            </span>
          </div>
        );
      },
    },
  ];

  return (
    <div className="pt-2 px-5 bg-white">
      <Box sx={{width: "100%"}}>
        <div className="flex flex-wrap gap-2 my-5">
          <SearchInput
            placeholder={t("common.account_email")}
            searchText={searchText}
            className="py-[5px]"
            onChange={(v) => setSearchText(v)}
          />
          <Select
            value={status}
            size="small"
            displayEmpty
            sx={{borderRadius: "8px", fontWeight: "600", fontSize: "14px"}}
            className="bg-custom-background custom-select h-10 w-[160px]"
            inputProps={{"aria-label": "Without label"}}
          >
            {packageOptions.map((option) => (
              <MenuItem key={option.value} value={option.value}>
                {option.label}
              </MenuItem>
            ))}
          </Select>
          <LocalizationProvider dateAdapter={AdapterDayjs}>
            <DatePicker
              disableFuture
              slotProps={{
                textField: {
                  size: "small",
                  placeholder: t("common.updated_time"),
                },
                field: {
                  clearable: true,
                  onClear: () => setUpdateTime(""),
                },
              }}
              className="cms-datepicker-gray"
            />
          </LocalizationProvider>
        </div>
      </Box>
      <CmsTable
        rows={tableRows}
        columns={columns}
        currentPage={page || 0}
        onPageChange={(page) => setPage(page)}
        rowsPerPage={pageSize || 10}
        onRowsPerPageChange={(rowsPerPage) => setPageSize(rowsPerPage)}
        onSortModelChange={(model) => setSortModel(model)}
        onRowClick={(params) => handleRowClick(params.row)}
        hideFooter
      />
      <AccountDetailModal
        open={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        userData={selectedUser}
      />
    </div>
  );
}
