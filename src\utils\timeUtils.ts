import moment from "moment";
import i18n from "@i18n/index";
import {t} from "i18next";
moment.locale(i18n.language);

export function convertDuration(durationInSeconds?: number) {
  if (!durationInSeconds) return "-";
  const totalMinutes = Math.floor(durationInSeconds / 60);
  if (totalMinutes < 60) {
    return t("duration.minute_only", {count: totalMinutes});
  }
  const hours = Math.floor(totalMinutes / 60);
  const remainingMinutes = totalMinutes % 60;
  if (remainingMinutes === 0) {
    return t("duration.hour_only", {count: hours});
  }
  return t("duration.hour_and_minute", {
    hour: hours,
    minute: remainingMinutes,
  });
}

export const convertPlayerTime = (time?: number) => {
  if (!time) return "0:00";

  const hours = Math.floor(time / 3600);
  const minutes = Math.floor((time % 3600) / 60);
  const seconds = Math.floor(time % 60);

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, "0")}:${seconds
      .toString()
      .padStart(2, "0")}`;
  } else {
    return `${minutes}:${seconds.toString().padStart(2, "0")}`;
  }
};

export function convertSongDuration(duration?: number) {
  if (!duration) return "-";
  const minutes = Math.ceil((duration % 3600) / 60);
  const seconds = Math.ceil(duration % 60);
  return `${minutes}:${seconds < 10 ? "0" : ""}${seconds}`;
}

export function convertDate(date?: string | Date) {
  if (!date) return "-";
  const format = t("common.date_format_full", {defaultValue: "LL"});
  return moment(date).format(format);
}

export function convertDateSong(date?: string | Date) {
  if (!date) return "-";
  const format = t("common.date_format", {defaultValue: "LL"});
  return moment(date).format(format);
}

export function convertNumber(num?: number): string {
  if (typeof num !== "number" || isNaN(num)) return "-";
  return new Intl.NumberFormat(i18n.language).format(num);
}

export function convertDateTime(date?: string | Date) {
  if (!date) return "-";
  const format = t("common.date_time_format", {defaultValue: "HH:mm - LL"});
  return moment(date).format(format);
}
