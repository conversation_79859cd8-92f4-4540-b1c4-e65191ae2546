import ApiArtistDetail from "@api/ApiArtistDetail";
import AuthButtonWrapper from "@components/AuthButton/AuthButtonWrapper";
import GlobalButton from "@components/ButtonGlobal";
import IconAdd from "@components/Icon/IconAdd";
import IconVector from "@components/Icon/IconVector";
import IconVerify from "@components/Icon/IconVerify";
import IconVerify2 from "@components/Icon/IconVerify2";
import {IRootState} from "@redux/store";
import {useMutation} from "@tanstack/react-query";
import clsx from "clsx";
import {useEffect, useState} from "react";
import {useTranslation} from "react-i18next";
import {useSelector} from "react-redux";
import {toast} from "react-toastify";
import {ESharePlatform, IArtist} from "src/types";
import {shortenNumber} from "src/utils/numberUtils";
import {logEvent} from "src/utils/firebase";
import {IconButton} from "@mui/material";
import IconShare from "@components/Icon/IconShare";
import ModalShare from "@components/ModalShare";
import {generateShareLink} from "src/utils/global";
import PlayArrowRounded from "@mui/icons-material/PlayArrowRounded";
import PauseRounded from "@mui/icons-material/PauseRounded";
import PlayerUtil from "src/core/PlayerUtil";

interface ArtistBannerProps {
  data: IArtist | undefined;
  refetch?: () => void;
  handlePlayAllSongs: () => void;
}

export default function ArtistBanner({
  data,
  refetch,
  handlePlayAllSongs,
}: ArtistBannerProps): JSX.Element {
  const {t} = useTranslation();
  const {currentSong, currentArtistId, paused} = useSelector(
    (state: IRootState) => state.player,
  );
  const [isLike, setIsLike] = useState<boolean>(data?.isLiked ?? false);
  const isOpen = useSelector((state: IRootState) => state.wishlist.isOpen);
  const [openModalShare, setOpenModalShare] = useState<boolean>(false);

  const shareArtistMutate = useMutation({
    mutationFn: ApiArtistDetail.shareArtist,
    onSuccess: () => {
      toast.success(t("common.copy_link_success"));
      const shareUrl = generateShareLink({type: "artist", data});
      navigator.clipboard.writeText(shareUrl);
    },
    onError: () => {
      toast.error(t("common.copy_link_failed"));
    },
  });

  useEffect(() => {
    setIsLike(data?.isLiked ?? false);
  }, [data]);

  const likeArtistMutate = useMutation({
    mutationFn: ApiArtistDetail.likeArtist,
    onSuccess: () => {
      setIsLike((prev) => !prev);
      refetch?.();
    },
    onError: () => {
      toast.error(t("common.operation_failed"));
    },
  });

  const likeOrDislikeArtist = (artistId: string) => {
    likeArtistMutate.mutateAsync(artistId);
    if (!isLike) {
      logEvent("favorite_artist", {
        artist_id: artistId,
        artist_name: data?.name,
        current_song_id: currentSong?.id,
        current_song_name: currentSong?.name,
        current_song_artist: currentSong?.artists
          ?.map((artist) => artist?.stageName ?? artist?.name)
          .join(", "),
      });
    } else {
      logEvent("unfavorite_artist", {
        artist_id: artistId,
        artist_name: data?.name,
        current_song_id: currentSong?.id,
        current_song_name: currentSong?.name,
        current_song_artist: currentSong?.artists
          ?.map((artist) => artist?.stageName ?? artist?.name)
          .join(", "),
      });
    }
  };

  const handleOpenModalShare = () => {
    setOpenModalShare(true);
  };

  const handleCloseModalShare = () => {
    setOpenModalShare(false);
  };

  const handleShareClick = () => {
    shareArtistMutate.mutateAsync(data?.id ?? "");
    logEvent("share_artist", {
      artist_id: data?.id,
      artist_name: data?.name,
      share_platform: ESharePlatform.NONE,
    });
  };

  return (
    <>
      <div
        className={clsx(
          "w-full flex items-end sm:pl-8 pb-0 sm:pb-5",
          "bg-[url(/image/default-cover.png)] bg-cover",
        )}
      >
        <div
          className={clsx(
            "flex mx-auto flex-col xl:mx-0 xl:flex-row md:flex-row md:mx-0 items-center p-3 sm:p-6 gap-2 sm:gap-4 relative z-10 sm:pt-10 ",
            isOpen ? "lg:flex-col lg:mx-auto" : " lg:flex-row lg:mx-0",
          )}
        >
          <img
            src={
              data?.images?.DEFAULT ||
              data?.images?.SMALL ||
              "/image/default-avatar.png"
            }
            alt={t("common.artist_avatar")}
            className="w-24 h-24 sm:w-32 sm:h-32 border border-gray-900 md:w-40 md:h-40 object-cover rounded-full md:mr-5"
          />

          <div
            className={clsx(
              "text-center gap-2 sm:gap-4 md:text-left md:flex-row md:mx-0 md:items-center md:gap-8 flex-col xl:flex-row flex  xl:gap-10 xl:items-center xl:text-left",
              isOpen
                ? "lg:flex-col lg:gap-4 lg:items-center lg:justify-center lg:text-center"
                : " lg:flex-row lg:gap-10 lg:items-center",
            )}
          >
            <div>
              <span
                className={clsx(
                  "text-white max-md:justify-center rounded-full flex items-center xl:justify-normal",
                  isOpen ? "lg:justify-center" : "",
                )}
              >
                {data?.isVerify ? (
                  <IconVerify />
                ) : (
                  <IconVerify2 className="w-[18px] h-[18px]" />
                )}
                <p className="text-[10px] sm:text-sm ml-2 uppercase">
                  {data?.isVerify ? (
                    <>{t("common.verified")}</>
                  ) : (
                    <>{t("common.no_verified")}</>
                  )}
                </p>
              </span>

              <h1 className="text-white text-base sm:text-2xl md:text-3xl font-bold my-1">
                {data?.stageName ?? data?.name}
              </h1>

              <div className="flex gap-2 sm:gap-0 flex-row sm:flex-col justify-center md:justify-start">
                <p className="text-white text-sm md:text-base flex gap-1 justify-center md:justify-start">
                  {shortenNumber(data?.totalLikes)}
                  <span className="text-gray-400">{t("common.favorites")}</span>
                </p>
                <p className="text-white text-sm md:text-base flex gap-1 justify-center md:justify-start">
                  {shortenNumber(data?.totalSongs)}
                  <span className="text-gray-400">{t("common.songs")}</span>
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2 lg:gap-3">
              <IconButton
                className={`!bg-orange-500 !text-white shadow-[4px_4px_20px_0px_#B112005C,_-4px_-4px_20px_0px_#A708004A] ${
                  data?.totalSongs === 0 ? "opacity-50 cursor-not-allowed" : ""
                }`}
                onClick={() => {
                  if (currentArtistId === data?.id) {
                    if (paused) {
                      PlayerUtil.instance.play();
                    } else {
                      PlayerUtil.instance.pause();
                    }
                  } else {
                    handlePlayAllSongs();
                  }
                }}
                disabled={data?.totalSongs === 0}
              >
                {!currentArtistId || currentArtistId !== data?.id || paused ? (
                  <PlayArrowRounded className="!fill-white" />
                ) : (
                  <PauseRounded className="!fill-white" />
                )}
              </IconButton>
              <AuthButtonWrapper
                action={() => likeOrDislikeArtist(data?.id ?? "")}
              >
                <GlobalButton
                  startIcon={isLike ? <IconVector /> : <IconAdd />}
                  text={isLike ? t("common.favorited") : t("common.favorite")}
                  isLoading={likeArtistMutate.isPending}
                  disabled={likeArtistMutate.isPending}
                  className={clsx(
                    "!rounded-3xl min-w-[80px] sm:min-w-[130px] flex",
                    isLike
                      ? "!border !border-[#FFFFFF33] !text-[#FBFDFF] !bg-transparent"
                      : "",
                  )}
                  textClassName="text-lg text-[#FBFDFF]"
                />
              </AuthButtonWrapper>
              <div className="h-9 w-9 rounded-full flex items-center justify-center">
                <IconButton onClick={handleOpenModalShare}>
                  <IconShare className="h-6 w-6" />
                </IconButton>
              </div>
            </div>
          </div>
        </div>
      </div>
      <ModalShare
        open={openModalShare}
        onCancel={handleCloseModalShare}
        handleCopyLink={handleShareClick}
        image={data?.images?.DEFAULT || data?.images?.SMALL}
        name={data?.name}
        shareUrl={generateShareLink({type: "artist", data})}
      />
    </>
  );
}
