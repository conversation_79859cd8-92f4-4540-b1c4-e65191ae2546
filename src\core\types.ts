import {ISong} from "src/types";

export interface IBasePlayer {
  readonly type: EPlayerType;
  ready: boolean;
  paused: boolean;
  currentTime: number;
  duration: number;
  volume: number;
  muted: boolean;

  isSongPlaying(data?: ISong): boolean;
  loadSong: (songData: ISong) => void;
  play: () => void;
  pause: () => void;
  seek: (time: number) => void;
  setVolume: (volume: number) => void;
  mute: () => void;
  unMute: () => void;
  clear: () => void;
}

export enum EPlayerType {
  CORE,
  YOUTUBE,
}
