import GlobalSwitch from "@components/SwitchCustom";
import {Box, MenuItem, Select} from "@mui/material";
import {
  setMusicQuality,
  setRemoveSilent,
  setTimeToNextSong,
} from "@redux/slices/GlobalSettingSlice";
import {IRootState} from "@redux/store";
import {useTranslation} from "react-i18next";
import {useDispatch, useSelector} from "react-redux";
import PlayerUtil from "src/core/PlayerUtil";
import {AudioQualityTypeEnum} from "src/types";
import {logEvent} from "src/utils/firebase";

export default function ListenMode() {
  const {t} = useTranslation();
  const dispatch = useDispatch();
  const {currentSong} = useSelector((state: IRootState) => state.player);

  const {timeToNextSong, removeSilent, musicQuality} = useSelector(
    (state: IRootState) => state?.settings,
  );

  const handleMusicQualityChange = (value: AudioQualityTypeEnum) => {
    dispatch(setMusicQuality(value));
    logEvent("change_audio_settings", {
      song_id: currentSong?.id,
      song_name: currentSong?.name,
      setting_type: "music_quality",
      play_position: PlayerUtil.instance.currentTime,
      new_value: value,
    });
  };

  return (
    <div className="flex flex-col gap-[15px] items-start text-white">
      <span className="text-base font-medium">
        {t("common.setting.advanced_setting")}
      </span>
      <Box
        sx={{
          width: "100%",
          backgroundColor: "#FFFFFF12",
          color: "#fff",
          borderRadius: "4px",
          overflow: "hidden",
          padding: "10px 15px",
        }}
      >
        <div className="flex justify-between items-center align-middle">
          <div className="flex flex-col">
            <div className="font-normal text-sm text-white">
              {t("common.setting.time_to_next_song")}
            </div>
            <div className="text-[11px] text-[#8F8F8F]">
              {`${timeToNextSong} ${t("common.second")}${
                timeToNextSong === 3 ? ` (${t("common.default")})` : ""
              }`}
            </div>
          </div>
          <div>
            <Select
              value={timeToNextSong}
              onChange={(e) =>
                dispatch(setTimeToNextSong(Number(e.target.value)))
              }
              disabled={removeSilent}
              size="small"
              displayEmpty
              sx={{
                "borderRadius": "10px",
                "fontWeight": "500",
                "fontSize": "14px",
                "color": "#fff",
                "& .MuiSelect-select": {
                  color: "#fff",
                  padding: "4px 28px 4px 8px",
                  textAlign: "center",
                },
                "& .MuiOutlinedInput-notchedOutline": {
                  borderColor: "transparent",
                },
                "&:hover .MuiOutlinedInput-notchedOutline": {
                  borderColor: "transparent",
                },
                "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                  borderColor: "transparent",
                },
                "& .MuiSelect-icon": {color: "#FF0000"},
              }}
              className="bg-[#3C3C3C] custom-select h-8 w-[65px] py-1"
              inputProps={{
                "aria-label": "Without label",
                "style": {backgroundColor: "#3C3C3C", padding: "0"},
              }}
              MenuProps={{
                PaperProps: {
                  sx: {
                    "marginTop": "3px",
                    "color": "#fff",
                    "backgroundColor": "#3C3C3C",
                    "borderRadius": "10px",
                    "& .MuiMenuItem-root": {fontSize: "14px"},
                    "& .MuiMenuItem-root.Mui-selected:not(:first-of-type)": {
                      backgroundColor: "#3C3C3C",
                      color: "#fff",
                      fontSize: "14px",
                    },
                    "& .MuiMenuItem-root.Mui-selected:first-of-type": {
                      color: "#fff",
                      backgroundColor: "transparent",
                      fontSize: "14px",
                    },
                  },
                },
              }}
            >
              <MenuItem value={3}>
                {t("common.setting.sec", {second: 3})}
              </MenuItem>
              <MenuItem value={5}>
                {t("common.setting.sec", {second: 5})}
              </MenuItem>
            </Select>
          </div>
        </div>
      </Box>
      <Box
        sx={{
          width: "100%",
          backgroundColor: "#FFFFFF12",
          color: "#fff",
          borderRadius: "4px",
          overflow: "hidden",
          padding: "10px 15px",
        }}
      >
        <div className="flex justify-between items-center align-middle">
          <div className="flex flex-col">
            <div className="font-normal text-sm text-white">
              {t("common.setting.remove_slient")}
            </div>
            <div className="text-[11px] text-[#8F8F8F]">
              {removeSilent ? t("common.on") : t("common.off")}
            </div>
          </div>
          <div>
            <GlobalSwitch
              checked={removeSilent}
              onChange={(e) => dispatch(setRemoveSilent(e.target.checked))}
            />
          </div>
        </div>
      </Box>
      <Box
        sx={{
          width: "100%",
          backgroundColor: "#FFFFFF12",
          color: "#fff",
          borderRadius: "4px",
          overflow: "hidden",
          padding: "10px 15px",
        }}
      >
        <div className="flex justify-between items-center align-middle">
          <div className="flex flex-col">
            <div className="font-normal text-sm text-white">
              {t("common.setting.music_quality")}
            </div>
            <div className="text-[11px] text-[#8F8F8F]">
              {t("common.quality")}{" "}
              {musicQuality === AudioQualityTypeEnum.KBPS_320
                ? `${t("common.setting.high")}`
                : `${t("common.setting.low")}`}
            </div>
          </div>
          <div>
            <Select
              value={musicQuality}
              onChange={(e) =>
                handleMusicQualityChange(
                  e.target.value as
                    | AudioQualityTypeEnum.KBPS_320
                    | AudioQualityTypeEnum.KBPS_128,
                )
              }
              size="small"
              displayEmpty
              sx={{
                "borderRadius": "10px",
                "fontWeight": "500",
                "fontSize": "14px",
                "color": "#fff",
                "& .MuiSelect-select": {
                  color: "#fff",
                  padding: "4px 28px 4px 8px",
                  textAlign: "center",
                },
                "& .MuiOutlinedInput-notchedOutline": {
                  borderColor: "transparent",
                },
                "&:hover .MuiOutlinedInput-notchedOutline": {
                  borderColor: "transparent",
                },
                "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                  borderColor: "transparent",
                },
                "& .MuiSelect-icon": {color: "#FF0000"},
              }}
              className="bg-[#3C3C3C] custom-select h-8 w-auto py-1"
              inputProps={{
                "aria-label": "Without label",
                "style": {backgroundColor: "#3C3C3C", padding: "0"},
              }}
              MenuProps={{
                PaperProps: {
                  sx: {
                    "marginTop": "3px",
                    "color": "#fff",
                    "backgroundColor": "#3C3C3C",
                    "borderRadius": "10px",
                    "& .MuiMenuItem-root": {fontSize: "14px"},
                    "& .MuiMenuItem-root.Mui-selected:not(:first-of-type)": {
                      backgroundColor: "#3C3C3C",
                      color: "#fff",
                      fontSize: "14px",
                    },
                    "& .MuiMenuItem-root.Mui-selected:first-of-type": {
                      color: "#fff",
                      backgroundColor: "transparent",
                      fontSize: "14px",
                    },
                  },
                },
              }}
            >
              <MenuItem value={AudioQualityTypeEnum.KBPS_320}>
                {t("common.high")}
              </MenuItem>
              <MenuItem value={AudioQualityTypeEnum.KBPS_128}>
                {t("common.low")}
              </MenuItem>
            </Select>
          </div>
        </div>
      </Box>
    </div>
  );
}
