@use "../../../styles/global.scss" as globals;

.player-slider-wrapper {
  input.player-slider[type="range"] {
    position: absolute;
    left: 0;
    z-index: 2;
    background: transparent;
    appearance: none;
    width: 100%;
    height: 100%;
    border-radius: 12px;
    cursor: pointer;

    &::-webkit-slider-thumb {
      -webkit-appearance: none;
      background: transparent;
      width: 12px;
      height: 12px;
      cursor: pointer;
      border-radius: 50%;
    }

    &::-moz-range-thumb {
      -moz-appearance: none;
      background: transparent;
      width: 12px;
      height: 12px;
      cursor: pointer;
      border-radius: 50%;
    }

    &::-ms-thumb {
      appearance: none;
      background: transparent;
      width: 12px;
      height: 12px;
      cursor: pointer;
    }
  }

  &:hover {
    .player-track {
      background-color: globals.$orange-500;
    }

    input.player-slider[type="range"] {
      &::-webkit-slider-thumb {
        -webkit-appearance: unset;
        background-color: white;
      }

      &::-moz-range-thumb {
        -moz-appearance: unset;
        background-color: white;
      }

      &::-ms-thumb {
        appearance: unset;
        background-color: white;
      }
    }
  }
}
