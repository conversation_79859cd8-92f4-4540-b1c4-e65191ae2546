import i18n from "i18next";
import {t} from "i18next";

export const shortenNumber = (value?: number): string => {
  if (typeof value !== "number" || isNaN(value)) {
    return "0";
  }

  let res: number = value;
  let unit: string = "";

  if (value >= 1_000_000_000) {
    res = value / 1_000_000_000;
    unit = t("shorten.billion");
  } else if (value >= 1_000_000) {
    res = value / 1_000_000;
    unit = t("shorten.million");
  } else if (value >= 1_000) {
    res = value / 1_000;
    unit = t("shorten.thousand");
  }

  return res.toLocaleString(i18n.language, {maximumFractionDigits: 1}) + unit;
};

export const formatNumberWithCommas = (value?: number): string => {
  if (typeof value !== "number" || isNaN(value)) {
    return "0";
  }
  return value.toLocaleString(i18n.language, {});
};
