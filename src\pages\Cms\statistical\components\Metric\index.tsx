import {ReactNode} from "react";
export interface MetricCardProps {
  title: string;
  value: number | string;
  length?: number;
}

const MetricCard: React.FC<MetricCardProps> = ({title, value, length}) => {
  return (
    <div
      className={`shadow-lg bg-white transition-all duration-300 h-full rounded-lg py-3 px-4 group`}
      style={{
        width: length ? `${length}px` : "auto",
      }}
    >
      <div className="flex gap-2 items-center justify-between">
        <div className="font-[600] text-sm mb-2">{title}</div>
      </div>
      <div className="text-2xl font-semibold mb-1 transition-all duration-300 group-hover:text-blue-600">
        {value.toLocaleString()}
      </div>
    </div>
  );
};

export default function Metric({
  metrics,
  vertical = false,
  gap = 0,
  col,
}: {
  metrics: MetricCardProps[];
  vertical?: boolean;
  gap?: number;
  col?: number;
}): ReactNode {
  return (
    <div>
      <div
        className={`animate-[fadeIn_0.5s_ease-in-out] ${
          vertical ? "flex flex-col" : "grid"
        }`}
        style={
          vertical
            ? {gap: gap}
            : {gap: gap, gridTemplateColumns: `repeat(${col}, minmax(0, 1fr))`}
        }
      >
        {metrics?.map((metric, index) => (
          <div
            key={index}
            className="animate-[slideIn_0.5s_ease-in-out]"
            style={{
              animationDelay: `${index * 0.1}s`,
            }}
          >
            <MetricCard
              title={metric?.title}
              value={metric?.value}
              length={metric?.length}
            />
          </div>
        ))}
      </div>
    </div>
  );
}
