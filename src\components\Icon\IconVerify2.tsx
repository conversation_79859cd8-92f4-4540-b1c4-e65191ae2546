import {SVGProps} from "react";

function IconVerify2(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      width="13"
      height="12"
      viewBox="0 0 13 12"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M11.86 5.17515L10.94 4.43515C10.8662 4.37626 10.8089 4.29922 10.7739 4.21157C10.7388 4.12392 10.7271 4.02867 10.74 3.93515L10.865 2.76515C10.8823 2.60968 10.8646 2.4523 10.813 2.30462C10.7614 2.15694 10.6773 2.02272 10.5669 1.91186C10.4566 1.80101 10.3227 1.71635 10.1753 1.66413C10.0278 1.61191 9.8705 1.59346 9.71496 1.61015L8.53996 1.73515C8.44643 1.74797 8.35118 1.73628 8.26353 1.70122C8.17588 1.66616 8.09884 1.60893 8.03996 1.53515L7.31496 0.630146C7.21358 0.513552 7.08837 0.420067 6.94777 0.356004C6.80717 0.29194 6.65446 0.258789 6.49996 0.258789C6.34545 0.258789 6.19275 0.29194 6.05215 0.356004C5.91155 0.420067 5.78634 0.513552 5.68496 0.630146L4.94496 1.55015C4.88608 1.62393 4.80904 1.68116 4.72139 1.71622C4.63374 1.75128 4.53849 1.76297 4.44496 1.75015L3.28496 1.62515C3.12942 1.60846 2.97212 1.62691 2.82466 1.67913C2.6772 1.73135 2.54335 1.81601 2.43298 1.92686C2.3226 2.03772 2.23853 2.17194 2.18695 2.31962C2.13537 2.4673 2.1176 2.62468 2.13496 2.78015L2.25996 3.95015C2.27278 4.04367 2.26109 4.13892 2.22603 4.22657C2.19097 4.31422 2.13374 4.39126 2.05996 4.45015L1.13996 5.17515C1.01795 5.27307 0.919475 5.39713 0.851813 5.53819C0.78415 5.67925 0.749023 5.8337 0.749023 5.99015C0.749023 6.14659 0.78415 6.30104 0.851813 6.4421C0.919475 6.58316 1.01795 6.70723 1.13996 6.80515L2.05996 7.54515C2.13337 7.60435 2.19032 7.68143 2.22534 7.76899C2.26036 7.85655 2.27229 7.95165 2.25996 8.04515L2.13496 9.22015C2.11763 9.37528 2.13561 9.53232 2.18757 9.67952C2.23952 9.82672 2.32409 9.96026 2.43496 10.0701C2.54327 10.1832 2.6766 10.2692 2.82424 10.3213C2.97188 10.3735 3.12968 10.3902 3.28496 10.3701L4.45996 10.2451C4.55349 10.2323 4.64874 10.244 4.73639 10.2791C4.82404 10.3141 4.90108 10.3714 4.95996 10.4451L5.69996 11.3651C5.79788 11.4872 5.92194 11.5856 6.063 11.6533C6.20406 11.721 6.35851 11.7561 6.51496 11.7561C6.67141 11.7561 6.82586 11.721 6.96691 11.6533C7.10797 11.5856 7.23204 11.4872 7.32996 11.3651L8.06996 10.4451C8.12884 10.3714 8.20588 10.3141 8.29353 10.2791C8.38118 10.244 8.47643 10.2323 8.56996 10.2451L9.74496 10.3701C9.90023 10.3902 10.058 10.3735 10.2057 10.3213C10.3533 10.2692 10.4867 10.1832 10.595 10.0701C10.7058 9.96026 10.7904 9.82672 10.8424 9.67952C10.8943 9.53232 10.9123 9.37528 10.895 9.22015L10.77 8.04515C10.7576 7.95165 10.7696 7.85655 10.8046 7.76899C10.8396 7.68143 10.8966 7.60435 10.97 7.54515L11.89 6.80515C12.012 6.70723 12.1104 6.58316 12.1781 6.4421C12.2458 6.30104 12.2809 6.14659 12.2809 5.99015C12.2809 5.8337 12.2458 5.67925 12.1781 5.53819C12.1104 5.39713 12.012 5.27307 11.89 5.17515H11.86ZM6.69996 7.52515C6.62291 7.62542 6.52549 7.70822 6.41411 7.76811C6.30274 7.82799 6.17994 7.86359 6.0538 7.87257C5.92767 7.88155 5.80106 7.86369 5.68233 7.82018C5.56359 7.77667 5.45543 7.7085 5.36496 7.62015L4.14496 6.40015C4.09834 6.35353 4.06136 6.29818 4.03613 6.23727C4.0109 6.17636 3.99791 6.11108 3.99791 6.04515C3.99791 5.97922 4.0109 5.91393 4.03613 5.85302C4.06136 5.79211 4.09834 5.73677 4.14496 5.69015C4.19158 5.64353 4.24692 5.60655 4.30783 5.58132C4.36875 5.55609 4.43403 5.5431 4.49996 5.5431C4.56589 5.5431 4.63117 5.55609 4.69208 5.58132C4.753 5.60655 4.80834 5.64353 4.85496 5.69015L5.99996 6.81515L7.95496 4.21015C8.03452 4.10406 8.15297 4.03392 8.28425 4.01517C8.34925 4.00589 8.41544 4.00949 8.47905 4.02579C8.54266 4.04209 8.60243 4.07075 8.65496 4.11015C8.70749 4.14954 8.75174 4.1989 8.7852 4.2554C8.81865 4.3119 8.84065 4.37443 8.84993 4.43944C8.85922 4.50444 8.85561 4.57063 8.83932 4.63424C8.82302 4.69784 8.79436 4.75762 8.75496 4.81015L6.69996 7.52515Z"
        fill="#FF4319"
      />
    </svg>
  );
}

export default IconVerify2;
