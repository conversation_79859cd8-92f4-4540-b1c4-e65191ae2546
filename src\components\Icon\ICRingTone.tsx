import React from "react";

export default function ICRingbackTone({
  ...props
}: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M9.6 15.025C9.2 15.025 8.85 14.875 8.55 14.575C8.25 14.275 8.1 13.925 8.1 13.525V4.5C8.1 4.1 8.25 3.75 8.55 3.45C8.85 3.15 9.2 3 9.6 3H14.425C14.825 3 15.175 3.15 15.475 3.45C15.775 3.75 15.925 4.1 15.925 4.5V13.525C15.925 13.925 15.775 14.275 15.475 14.575C15.175 14.875 14.825 15.025 14.425 15.025H9.6ZM7.775 22C5.90835 22 4.3125 21.3458 2.9875 20.0375C1.6625 18.7292 1 17.1416 1 15.275C1 15.0583 1.07234 14.8792 1.217 14.7375C1.3615 14.5958 1.54067 14.525 1.7545 14.525C1.96817 14.525 2.14583 14.5969 2.2875 14.7405C2.42916 14.8844 2.5 15.0625 2.5 15.275C2.5 16.7416 3.01434 17.9791 4.043 18.9875C5.0715 19.9958 6.3155 20.5 7.775 20.5C7.9875 20.5 8.16565 20.5723 8.3095 20.717C8.45315 20.8615 8.525 21.0406 8.525 21.2545C8.525 21.4681 8.45415 21.6459 8.3125 21.7875C8.17085 21.9291 7.99165 22 7.775 22ZM16.225 22C16.0084 22 15.8292 21.9277 15.6875 21.783C15.5458 21.6385 15.475 21.4594 15.475 21.2455C15.475 21.0319 15.5469 20.8541 15.6908 20.7125C15.8344 20.5709 16.0125 20.5 16.225 20.5C17.6845 20.5 18.9285 19.9958 19.957 18.9875C20.9857 17.9791 21.5 16.7416 21.5 15.275C21.5 15.0625 21.5723 14.8844 21.717 14.7405C21.8615 14.5969 22.0406 14.525 22.2545 14.525C22.4681 14.525 22.6459 14.5969 22.7875 14.7405C22.9291 14.8844 23 15.0625 23 15.275C23 17.1416 22.3375 18.7292 21.0125 20.0375C19.6875 21.3458 18.0917 22 16.225 22ZM7.775 18.5C6.875 18.5 6.10415 18.1875 5.4625 17.5625C4.82084 16.9375 4.5 16.175 4.5 15.275C4.5 15.0583 4.57234 14.8792 4.717 14.7375C4.8615 14.5958 5.04065 14.525 5.2545 14.525C5.46815 14.525 5.64585 14.5969 5.7875 14.7405C5.92915 14.8844 6 15.0625 6 15.275C6 15.5025 6.04475 15.7205 6.13425 15.929C6.2236 16.1375 6.35275 16.3222 6.52175 16.4832C6.6906 16.6444 6.87915 16.7708 7.0875 16.8625C7.29585 16.9541 7.525 17 7.775 17C7.9875 17 8.16565 17.0723 8.3095 17.217C8.45315 17.3615 8.525 17.5406 8.525 17.7545C8.525 17.9681 8.45415 18.1459 8.3125 18.2875C8.17085 18.4292 7.99165 18.5 7.775 18.5ZM16.225 18.5C16.0084 18.5 15.8292 18.4277 15.6875 18.283C15.5458 18.1385 15.475 17.9594 15.475 17.7455C15.475 17.5319 15.5469 17.3541 15.6908 17.2125C15.8344 17.0708 16.0125 17 16.225 17C16.475 17 16.7042 16.9541 16.9125 16.8625C17.1208 16.7708 17.3094 16.6444 17.4783 16.4832C17.6473 16.3222 17.7764 16.1375 17.8657 15.929C17.9552 15.7205 18 15.5025 18 15.275C18 15.0625 18.0723 14.8844 18.217 14.7405C18.3615 14.5969 18.5406 14.525 18.7545 14.525C18.9681 14.525 19.1459 14.5969 19.2875 14.7405C19.4291 14.8844 19.5 15.0625 19.5 15.275C19.5 16.175 19.1791 16.9375 18.5375 17.5625C17.8959 18.1875 17.125 18.5 16.225 18.5Z"
        fill="currentColor"
      />
    </svg>
  );
}
