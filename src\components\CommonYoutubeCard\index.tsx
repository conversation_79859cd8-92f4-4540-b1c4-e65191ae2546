import ApiSong from "@api/ApiSong";
import LikeButton from "@components/AuthButton/LikeButton";
import IconAdd24px from "@components/Icon/IconAdd24px";
import IconMoreHorizontal from "@components/Icon/IconMoreHorizontal";
import ModalAddToPlaylist from "@components/ModalAddToPlaylist";
import ModalShare from "@components/ModalShare";
import PopupMenu from "@components/PopupMenu";
import {PlayArrowRounded} from "@mui/icons-material";
import {IconButton} from "@mui/material";
import {addSongsToQueue} from "@redux/slices/PlayerSlice";
import {IRootState} from "@redux/store";
import {useMutation} from "@tanstack/react-query";
import clsx from "clsx";
import {useState} from "react";
import {useTranslation} from "react-i18next";
import {useDispatch, useSelector} from "react-redux";
import {toast} from "react-toastify";
import PlayerUtil from "src/core/PlayerUtil";
import {ISong} from "src/types";
import {generateShareLink} from "src/utils/global";
import {handleLikeSong} from "src/utils/like";
import {convertPlayerTime} from "src/utils/timeUtils";

interface IAlbumCardProps {
  className?: string;
  data: ISong;
  isSearch?: boolean;
  handlePlayMusic: () => void;
}

export default function CommonYoutubeCard({
  className,
  data,
  isSearch,
  handlePlayMusic,
}: IAlbumCardProps): JSX.Element {
  const {t} = useTranslation();
  const dispatch = useDispatch();
  const [hover, setHover] = useState(false);
  const [menuAnchor, setMenuAnchor] = useState<null | HTMLElement>(null);
  const [addToPlaylistOpen, setAddToPlaylistOpen] = useState(false);
  const [openModalShare, setOpenModalShare] = useState<boolean>(false);

  const {currentSong, queueList} = useSelector(
    (state: IRootState) => state.player,
  );
  const isPaused = useSelector((state: IRootState) => state.player.paused);
  const shareMutate = useMutation({
    mutationFn: ApiSong.shareSong,
    onSuccess: () => {
      const link = generateShareLink({type: "song", data});
      navigator.clipboard.writeText(link).then(() => {
        toast.success(t("common.copy_link_success"));
      });
    },
    onError: () => {
      toast.error(t("common.copy_link_failed"));
    },
  });

  const isPlaying = currentSong?.id === data?.id && !isPaused;

  const handleOpenMenu = (event: React.MouseEvent<HTMLButtonElement>) => {
    setMenuAnchor(event?.currentTarget);
  };

  const handleCloseMenu = () => {
    setMenuAnchor(null);
  };

  const openAddToPlaylist = () => {
    setAddToPlaylistOpen(true);
  };

  const handleCloseModalShare = () => {
    setOpenModalShare(false);
  };

  const handleCopyLink = () => {
    shareMutate.mutateAsync(data?.id ?? "");
  };

  const handlePlayPause = () => {
    if (currentSong?.id === data?.id) {
      if (isPaused) {
        PlayerUtil.instance.play();
      } else {
        PlayerUtil.instance.pause();
      }
    } else {
      handlePlayMusic();
    }
  };

  const handleAddSingleSongToQueue = (song: ISong) => {
    if (!song) {
      toast.info(t("common.song_not_found"));
      return;
    }
    const isExisted = queueList?.some((qSong) => qSong?.id === song?.id);

    if (isExisted) {
      toast.info(t("common.song_exist_queue"));
    } else {
      dispatch(addSongsToQueue([song]));
      toast.success(`${t("common.add_to_successfully")}`);
    }
  };

  return (
    <>
      <div
        className={`${className} group w-full aspect-video rounded-lg p-3 hover:bg-[#FFFFFF0F] cursor-pointer flex flex-col gap-4`}
        onMouseEnter={() => setHover(true)}
        onMouseLeave={() => setHover(false)}
        onClick={() => {
          if (!menuAnchor) {
            handlePlayPause();
          }
        }}
      >
        <div className="relative">
          <div className="relative flex w-full overflow-hidden rounded-[10px] border-[1.2px] border-[#FFFFFF12]">
            <img
              src={
                data?.images?.DEFAULT ||
                data?.images?.SMALL ||
                "/image/default-music.png"
              }
              alt={data?.name}
              className="w-full aspect-video object-cover transform transition-transform duration-700 ease-in-out group-hover:scale-110"
            />
            {!isSearch && (
              <div
                className=" absolute z-20"
                onClick={(e) => e.stopPropagation()}
              >
                <LikeButton
                  className={clsx("song-action p-2", "hover:rounded-full")}
                  action={() => handleLikeSong(data)}
                  isLiked={data?.isLiked}
                  songId={data?.id}
                />
              </div>
            )}
            <span className="absolute text-white z-10 right-[5px] bottom-[5px] px-1.5 py-1 rounded text-xs bg-[#000000B3]">
              {convertPlayerTime(data?.duration)}
            </span>
          </div>
          {(hover || currentSong?.id === data?.id) && (
            <div className="z-10 absolute flex top-0 left-0 w-full h-full justify-center items-center space-x-3">
              {isPlaying ? (
                <img
                  src="/image/animation_play.gif"
                  style={{filter: "grayscale(100%) brightness(0) invert(1)"}}
                  alt="Playing"
                />
              ) : (
                <span className="bg-orange-500 text-white flex rounded-full items-center justify-center h-10 w-10 shadow-[4px_4px_20px_0px_#B112004A,_-4px_-4px_20px_0px_#A708004A]">
                  <PlayArrowRounded />
                </span>
              )}
            </div>
          )}
        </div>
        <div className="flex gap-2 items-center justify-center w-full">
          <img
            src={
              data?.artists?.[0]?.images?.SMALL ||
              data?.artists?.[0]?.images?.DEFAULT ||
              "/image/default-avatar.png"
            }
            className="aspect-square rounded-full object-cover w-[13%]"
          />
          <div className="flex flex-col gap-0.5 justify-start w-[87%]">
            <span className="text-base font-medium text-white line-clamp-1">
              {data?.name}
            </span>
            <span className="text-sm font-normal text-[#FFFFFF80] line-clamp-1">
              {data?.artists?.[0]?.name}
            </span>
          </div>
          <IconButton
            className="song-action !p-0"
            onClick={(e) => {
              handleOpenMenu(e);
              e.stopPropagation();
            }}
          >
            <IconMoreHorizontal className="rotate-90" />
          </IconButton>
          <PopupMenu
            data={data}
            menuArray={[
              {
                icon: <IconAdd24px />,
                label: t("common.menu.add_to_playlist"),
                action: openAddToPlaylist,
                isAuth: true,
              },
              {
                icon: <IconAdd24px />,
                label: t("common.add_to_queue"),
                action: () => handleAddSingleSongToQueue(data),
              },
            ]}
            anchorEl={menuAnchor}
            open={Boolean(menuAnchor)}
            onClose={handleCloseMenu}
          ></PopupMenu>
        </div>
      </div>
      <ModalAddToPlaylist
        open={addToPlaylistOpen}
        onClose={() => setAddToPlaylistOpen(false)}
        songData={data}
      />
      <ModalShare
        open={openModalShare}
        onCancel={handleCloseModalShare}
        handleCopyLink={handleCopyLink}
        image={data?.images?.SMALL || data?.images?.DEFAULT}
        name={data?.name}
        artists={data?.artists}
        shareUrl={generateShareLink({type: "song", data})}
      />
    </>
  );
}
