import {useState} from "react";
import SearchInput from "../components/SearchInput";
import {GridColDef} from "@mui/x-data-grid";
import {convertDate, convertDuration, convertNumber} from "src/utils/timeUtils";
import CmsTable from "../components/CmsTable";
import {keepPreviousData, useMutation, useQuery} from "@tanstack/react-query";
import {useTranslation} from "react-i18next";
import QUERY_KEY from "@api/QueryKey";
import {
  EThemeAndGenreType,
  IArtist,
  IPlaylist,
  IThemeAndGenre,
  PlaylistType,
} from "src/types";
import ApiAutofill from "@api/ApiAutofill";
import {LocalizationProvider} from "@mui/x-date-pickers/LocalizationProvider/LocalizationProvider";
import {DatePicker} from "@mui/x-date-pickers/DatePicker";
import {AdapterDayjs} from "@mui/x-date-pickers/AdapterDayjs";
import dayjs from "dayjs";
import GlobalButton from "@components/ButtonGlobal";
import IconAdd from "@components/Icon/IconAdd";
import ModalAddEditAlbum from "./components/ModalAddEditAlbum";
import ApiPlaylist from "@api/ApiCMSPlaylist";
import {Avatar, AvatarGroup, IconButton, Tooltip} from "@mui/material";
import IconCmsEdit from "@components/Icon/IconCmsEdit";
import IconCmsDelete from "@components/Icon/IconCmsDelete";
import AutoCompleteAutofill from "@components/AutoCompleteAutofill";
import ModalDetailAlbum from "./components/ModalDetailAlbum";
import useDebounce from "src/hooks/useDebounce";
import ModalComfirm from "@components/ModalConfirm";
import {toast} from "react-toastify";

export default function CmsAlbum() {
  const {t} = useTranslation();
  const [page, setPage] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const [searchText, setSearchText] = useState("");
  const [themeId, setthemeId] = useState("");
  const [genreId, setGenreId] = useState("");
  const debounceSearchText = useDebounce(searchText);
  const [updateTime, setUpdateTime] = useState("");
  const [openModalAlbum, setOpenModalAlbum] = useState(false);
  const [selectedAlbum, setSelectedAlbum] = useState<IPlaylist | null>(null);
  const [isModalDetailOpen, setIsModalDetailOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState<IPlaylist | undefined>();
  const [deleteConfirmModalOpen, setDeleteConfirmModalOpen] = useState(false);
  const columns: GridColDef<IPlaylist>[] = [
    {
      field: "images",
      headerName: t("common.cover"),
      width: 136,
      renderCell: (params) => (
        <div className="w-full h-full flex justify-center items-center">
          <img
            src={
              params.value.SMALL ||
              params.value.DEFAULT ||
              "/image/default-music.png"
            }
            className="h-12 w-12 rounded-[4px] object-cover"
          />
        </div>
      ),
      disableColumnMenu: true,
      sortable: false,
    },
    {
      field: "name",
      headerName: t("common.album_name"),
      width: 190,
      sortable: false,
    },
    {
      field: "artists",
      headerName: t("common.performing_artist_composer"),
      sortable: false,
      width: 250,
      renderCell: (params) => (
        <div className="w-full h-full flex justify-start items-center space-x-1">
          <AvatarGroup
            max={3}
            sx={{
              "& .MuiAvatar-root": {
                width: 24,
                height: 24,
                fontSize: 12,
              },
            }}
          >
            {params.value.map((artist: IArtist, index: number) => (
              <Avatar
                key={`avatar_${index}`}
                src={
                  artist?.images?.SMALL ||
                  artist.images?.DEFAULT ||
                  "/image/default-avatar.png"
                }
              />
            ))}
          </AvatarGroup>
          <span className="font-semibold text-sm text-[#242728]">
            {params.value
              ?.slice(0, 2)
              .map((artist: IArtist) => artist?.stageName ?? artist?.name)
              .join(", ")}
            {params.value?.length > 2 && ` & ${t("common.lots_artist")}`}
          </span>
        </div>
      ),
    },
    {
      field: "totalSongs",
      headerName: t("common.quantity_song"),
      width: 160,
      sortable: false,
      renderCell: (params) => <span>{convertNumber(params?.value)}</span>,
    },
    {
      field: "themes",
      headerName: t("common.theme"),
      sortable: false,
      width: 160,
      renderCell: (params) => {
        return (
          <Tooltip
            placement="left"
            arrow
            title={params?.value?.map((item: IThemeAndGenre) => (
              <span key={item?.id}>
                {item?.name}
                {params?.value?.length > 1 &&
                params?.value?.indexOf(item) < params?.value?.length - 1
                  ? ", "
                  : ""}
              </span>
            ))}
          >
            <div className="line-clamp-3">
              {params?.value?.map((item: IThemeAndGenre) => (
                <span key={item?.id}>
                  {item?.name}
                  {params?.value?.length > 1 &&
                  params?.value?.indexOf(item) < params?.value?.length - 1
                    ? ", "
                    : ""}
                </span>
              ))}
            </div>
          </Tooltip>
        );
      },
    },
    {
      field: "genres",
      headerName: t("common.genre"),
      sortable: false,
      width: 160,
      renderCell: (params) => {
        return (
          <div className="flex flex-wrap gap-2 max-h-[94px] overflow-hidden">
            {params?.value?.map((item: IThemeAndGenre) => (
              <span
                className="rounded-lg  py-0.5 px-2 bg-[#F2F2F3] border border-[#DCDCDC]"
                key={item?.id}
              >
                {item?.name}
              </span>
            ))}
          </div>
        );
      },
    },
    {
      field: "description",
      headerName: t("common.description_album"),
      sortable: false,
      width: 448,
      renderCell: (params) => (
        <span className="line-clamp-3">{params.value}</span>
      ),
    },
    {
      field: "totalLikes",
      headerName: t("common.favorite_count"),
      width: 160,
      sortable: false,
      renderCell: (params) => (
        <span className="text-[#FF4319]">{convertNumber(params?.value)}</span>
      ),
    },
    {
      field: "totalDownloads",
      headerName: t("common.download_count"),
      width: 120,
      sortable: false,
      renderCell: (params) => <span>{convertNumber(params?.value)}</span>,
    },
    {
      field: "totalShares",
      headerName: t("common.share_count"),
      sortable: false,
      width: 130,
      renderCell: (params) => <span>{convertNumber(params?.value)}</span>,
    },
    {
      field: "totalListens",
      headerName: t("common.listen_count"),
      sortable: false,
      width: 130,
      renderCell: (params) => <span>{convertNumber(params?.value)}</span>,
    },
    {
      field: "totalDurations",
      headerName: t("common.duration"),
      width: 140,
      sortable: false,
      valueGetter: (value) => {
        return convertDuration(value);
      },
    },
    {
      field: "releaseDate",
      headerName: t("common.release_date"),
      width: 200,
      sortable: false,
      renderCell: (params) => {
        return convertDate(params?.value);
      },
    },
    {
      field: "updatedAt",
      headerName: t("common.updated_time"),
      width: 200,
      sortable: false,
      renderCell: (params) => {
        return convertDate(params?.value);
      },
    },
    {
      field: "actions",
      headerName: t("common.actions"),
      minWidth: 30,
      width: 120,
      sortable: false,
      disableColumnMenu: true,
      align: "center",
      headerAlign: "center",
      headerClassName: "sticky-header",
      cellClassName: "sticky-cell",
      renderCell: (param) => (
        <div className="flex justify-center items-center">
          <IconButton
            onClick={() => {
              setSelectedItem(param.row);
              setOpenModalAlbum(true);
            }}
          >
            <IconCmsEdit />
          </IconButton>
          <IconButton
            onClick={() => {
              setSelectedItem(param.row);
              setDeleteConfirmModalOpen(true);
            }}
          >
            <IconCmsDelete />
          </IconButton>
        </div>
      ),
    },
  ];

  const getListAlbumCms = useQuery({
    queryKey: [
      QUERY_KEY.ALBUM.GET_LIST_ALBUM_CMS,
      genreId,
      themeId,
      updateTime,
      page,
      pageSize,
      debounceSearchText,
      PlaylistType.ALBUM,
    ],
    placeholderData: keepPreviousData,
    queryFn: () =>
      ApiPlaylist.getListPlaylist({
        page: page,
        pageSize: pageSize,
        type: PlaylistType.ALBUM,
        keyword: debounceSearchText,
        genreId: genreId,
        themeId: themeId,
        updatedAt: updateTime,
      }),
  });

  const {mutateAsync: deletePlaylistMutaionAsync, isPending: deleting} =
    useMutation({
      mutationFn: ApiPlaylist.deletePlaylist,
      onSuccess: () => {
        toast.success(t("common.delete_successfully"));
        setSelectedItem(undefined);
        getListAlbumCms.refetch();
      },
    });

  const tableRows = getListAlbumCms?.data?.data?.map((item: IPlaylist) => ({
    id: item?.id,
    images: item?.images,
    name: item?.name ?? "-",
    totalDurations: item?.totalDurations,
    artists: item?.artists || "",
    totalSongs: item?.totalSongs,
    themes: item?.genres?.filter(
      (item) => item?.type === EThemeAndGenreType.THEME,
    ),
    genres: item?.genres?.filter(
      (item) => item?.type === EThemeAndGenreType.GENRE,
    ),
    songs: item?.songs,
    releaseDate: item?.releaseDate,
    updatedAt: item?.updatedAt,
    description: item?.description,
    totalLikes: item?.totalLikes,
    totalDownloads: item?.totalDownloads,
    totalShares: item?.totalShares,
    totalListens: item?.totalListens,
    language: item?.language,
  }));

  const handleRowClick = (album: IPlaylist) => {
    setSelectedAlbum(album);
    setIsModalDetailOpen(true);
  };

  return (
    <>
      <div className="p-5 bg-white space-y-4 rounded-[20px]">
        <div className="flex justify-between sm:items-center sm:flex-row flex-col sm:gap-0 gap-3 items-start">
          <div className="flex gap-3 flex-wrap w-full">
            <SearchInput
              placeholder={t("cms.album.album_artist_name")}
              searchText={searchText}
              className="py-[5px]"
              onChange={(v) => setSearchText(v)}
            />
            <AutoCompleteAutofill
              className="border-lg placeholder-dark-600 gray-theme"
              name="selectTheme"
              placeHolder={t("common.theme")}
              suggestionAPI={ApiAutofill.autoTheme}
              onChange={(val) => {
                if (!Array.isArray(val)) {
                  setthemeId(val && val.id);
                }
              }}
            />
            <AutoCompleteAutofill
              className="border-lg placeholder-dark-600 gray-theme"
              name="selectGenre"
              placeHolder={t("common.genre")}
              suggestionAPI={ApiAutofill.autoGenre}
              onChange={(val) => {
                if (!Array.isArray(val)) {
                  setGenreId(val && val.id);
                }
              }}
            />
            <LocalizationProvider dateAdapter={AdapterDayjs}>
              <DatePicker
                disableFuture
                onChange={(v) => {
                  if (v) {
                    setUpdateTime(dayjs(v).format("YYYY-MM-DD"));
                  }
                }}
                slotProps={{
                  textField: {
                    size: "small",
                    placeholder: t("common.updated_time"),
                  },
                  field: {
                    clearable: true,
                    onClear: () => setUpdateTime(""),
                  },
                }}
                className="cms-datepicker-gray"
              />
            </LocalizationProvider>
          </div>
          <GlobalButton
            text={t("common.add_new")}
            startIcon={<IconAdd />}
            className="w-auto whitespace-nowrap"
            onClick={() => setOpenModalAlbum(true)}
          />
        </div>
        <CmsTable
          ordinalColumn
          rows={tableRows}
          columns={columns}
          loading={getListAlbumCms?.isLoading}
          totalItems={getListAlbumCms?.data?.meta?.totalItems || 0}
          onPageChange={(page) => setPage(page)}
          rowsPerPage={pageSize}
          onRowsPerPageChange={(pageSize) => setPageSize(pageSize)}
          currentPage={page}
          onRowDoubleClick={(params) => handleRowClick(params.row)}
        />
      </div>
      <ModalAddEditAlbum
        open={openModalAlbum}
        refetch={getListAlbumCms.refetch}
        initValue={selectedItem || undefined}
        onClose={() => {
          setOpenModalAlbum(false);
          setSelectedItem(undefined);
        }}
      />
      {selectedAlbum && (
        <ModalDetailAlbum
          open={isModalDetailOpen}
          onClose={() => setIsModalDetailOpen(false)}
          albumId={selectedAlbum?.id ?? ""}
        />
      )}
      <ModalComfirm
        title={t("common.delete_confirm")}
        open={deleteConfirmModalOpen}
        onConfirm={() => {
          setDeleteConfirmModalOpen(false);
          if (selectedItem) {
            deletePlaylistMutaionAsync(selectedItem.id);
          }
        }}
        onCancel={() => setDeleteConfirmModalOpen(false)}
        loading={deleting}
      >
        {selectedItem && (
          <div className="flex flex-col gap-y-2">
            <img
              src={
                selectedItem?.images?.SMALL ||
                selectedItem?.images?.DEFAULT ||
                "/image/default-music.png"
              }
              className="w-12 h-12 border-[1.5px] border-solid border-[#262626] rounded-full object-cover"
            />
            <div>
              {t("common.album")}: {selectedItem.name}
            </div>
          </div>
        )}
      </ModalComfirm>
    </>
  );
}
