import ApiCMSSong from "@api/ApiCMSSong";
import QUERY_KEY from "@api/QueryKey";
import IconClose from "@components/Icon/IconClose";
import {CircularProgress, Modal} from "@mui/material";
import {GridRowId} from "@mui/x-data-grid";
import {useQuery} from "@tanstack/react-query";
import {useTranslation} from "react-i18next";
import {convertDate} from "src/utils/timeUtils";
import BasicInfo from "./tabs/BasicInfo";
import CopyrightInfo from "./tabs/CopyrightInfo";
import OtherInfo from "./tabs/OtherInfo";
import TabContext from "@mui/lab/TabContext";
import TabList from "@mui/lab/TabList";
import TabPanel from "@mui/lab/TabPanel";
import {Tab} from "@mui/material";
import {useState} from "react";

interface IModalDetailSong {
  open: boolean;
  onClose: () => void;
  songId: GridRowId;
}

export default function ModalDetailSong({
  open,
  onClose,
  songId,
}: IModalDetailSong) {
  const {t} = useTranslation();
  const [tabValue, setTabValue] = useState("1");

  const getSongById = useQuery({
    queryKey: [QUERY_KEY.SONG.GET_SONG_BY_ID],
    queryFn: () => ApiCMSSong.getCmsSongById(songId),
    enabled: !!songId,
  });

  const tabComponentArray = [
    {
      value: "1",
      component: <BasicInfo songData={getSongById?.data || undefined} />,
    },
    {
      value: "2",
      component: <CopyrightInfo songData={getSongById?.data || undefined} />,
    },
    {
      value: "3",
      component: <OtherInfo songData={getSongById?.data || undefined} />,
    },
  ];

  const tabPanelArray = [
    {value: "1", label: t("cms.song.basic_info")},
    {value: "2", label: t("cms.song.copyright_info")},
    {value: "3", label: t("cms.song.other_info")},
  ];

  return (
    <Modal sx={{outline: 0}} open={open} onClose={onClose}>
      <div className="absolute top-1/2 left-1/2 bg-white transform -translate-x-1/2 -translate-y-1/2 rounded-lg min-w-[367px] max-w-[932px] w-full">
        <div className="py-4 px-6 flex justify-between items-center">
          <span className="text-base font-bold">
            {t("cms.song.song_detail")}
          </span>
          <IconClose className="cursor-pointer" onClick={onClose} />
        </div>
        <hr className="bg-[#F0F0F0]" />
        <div className="flex flex-col p-6">
          {getSongById?.isFetching ? (
            <div className="flex justify-center items-center h-[50vh]">
              <CircularProgress />
            </div>
          ) : (
            <div className="flex flex-col gap-1">
              <div className="flex items-start justify-between">
                <div className="flex items-center gap-3">
                  <img
                    src={
                      getSongById?.data?.images?.SMALL ||
                      getSongById?.data?.images?.DEFAULT ||
                      "/image/default-music.png"
                    }
                    className="w-10 aspect-square rounded object-cover"
                    alt=""
                  />
                  <span className="text-gray-default text-xl font-semibold">
                    {getSongById?.data?.name || "-"}
                  </span>
                </div>
                <span className="text-sm text-[#656565] font-semibold">
                  {convertDate(getSongById?.data?.releaseDate)}
                </span>
              </div>
              <div className="flex flex-col gap-3">
                <TabContext value={tabValue}>
                  <TabList
                    className="cms-tabs border-b border-[#D9D9D9]"
                    onChange={(
                      _event: React.SyntheticEvent,
                      newValue: string,
                    ) => setTabValue(newValue)}
                  >
                    {tabPanelArray?.map((item) => (
                      <Tab
                        sx={{
                          textTransform: "none",
                        }}
                        label={item.label}
                        value={item.value}
                        key={item.value}
                      />
                    ))}
                  </TabList>
                  {tabComponentArray?.map((item) => (
                    <TabPanel
                      sx={{padding: 0}}
                      value={item.value}
                      key={item.value}
                    >
                      {item.component}
                    </TabPanel>
                  ))}
                </TabContext>
              </div>
            </div>
          )}
        </div>
      </div>
    </Modal>
  );
}
