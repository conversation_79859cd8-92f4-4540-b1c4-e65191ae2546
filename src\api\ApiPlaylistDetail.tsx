import {IArtist, IParamsDefault, IPlaylist, ISong} from "src/types";
import {fetcher, fetcherWithMetadata, IDataWithMeta} from "./Fetcher";

export interface IPlaylistSongParam {
  page: number;
  pageSize: number;
  order?: string;
  direction?: string;
  keyword?: string;
}

interface ICreatePlaylist {
  name: string;
  isPublic: boolean;
  image?: File | string;
}

const path = {
  playlist: "playlists",
  likePlaylist: "interaction-playlist/like",
  listenPlaylist: "interaction-playlist/listen",
  sharePlaylist: "interaction-playlist/share",
  myPlaylist: "playlists/my-playlists",
};

function getPlaylistDetail(urlSlug: string): Promise<IDataWithMeta<IPlaylist>> {
  return fetcherWithMetadata<IPlaylist>(
    {
      url: `/${path.playlist}/${urlSlug}`,
      method: "get",
    },
    {
      displayError: false,
      withMetadata: true,
    },
  );
}

function getPlaylistSongs(
  urlSlug: string,
  params: IParamsDefault,
): Promise<IDataWithMeta<ISong[]>> {
  return fetcherWithMetadata<ISong[]>(
    {
      url: `/${path.playlist}/${urlSlug}/songs-list`,
      method: "get",
      params,
    },
    {
      displayError: false,
      withMetadata: true,
    },
  );
}

function getArtistsList(
  urlSlug: string,
  params: IParamsDefault,
): Promise<IDataWithMeta<IArtist[]>> {
  return fetcherWithMetadata<IArtist[]>(
    {
      url: `/${path.playlist}/${urlSlug}/artists-list`,
      method: "get",
      params,
    },
    {
      displayError: false,
      withMetadata: true,
    },
  );
}

function getRecommendedPlaylists(
  params: IParamsDefault,
): Promise<IDataWithMeta<IPlaylist[]>> {
  return fetcherWithMetadata<IPlaylist[]>(
    {
      url: `/${path.playlist}/recommended`,
      method: "get",
      params,
    },
    {
      displayError: false,
      withMetadata: true,
    },
  );
}

function likePlaylist(id: string): Promise<IPlaylist> {
  return fetcher(
    {
      url: path.likePlaylist,
      method: "post",
      data: {
        playlistId: id,
      },
    },
    {
      displayError: false,
    },
  );
}

function listenPlaylist(id: string): Promise<IPlaylist> {
  return fetcher(
    {
      url: path.listenPlaylist,
      method: "post",
      data: {playlistId: id},
    },
    {
      displayError: false,
    },
  );
}

function updateMyPlaylist({
  urlSlug,
  ...restData
}: ICreatePlaylist & {
  urlSlug: string;
}): Promise<void> {
  return fetcher(
    {
      url: `${path.myPlaylist}/${urlSlug}`,
      method: "patch",
      data: restData,
    },
    {
      displayError: true,
      isFormData: true,
    },
  );
}

function deletePlaylist(urlSlug: string) {
  return fetcher({
    url: `${path.myPlaylist}/${urlSlug}`,
    method: "delete",
  });
}

function sharePlaylist(id: string): Promise<ISong> {
  return fetcher(
    {
      url: `${path.sharePlaylist}`,
      method: "post",
      data: {playlistId: id},
    },
    {
      displayError: false,
    },
  );
}

function importYoutubePlaylist(urlSlug: string) {
  return fetcher(
    {
      url: `${path.playlist}/${urlSlug}/detail-playlist-youtube`,
    },
    {displayError: true},
  );
}

export default {
  getPlaylistDetail,
  getPlaylistSongs,
  getRecommendedPlaylists,
  getArtistsList,
  likePlaylist,
  listenPlaylist,
  updateMyPlaylist,
  deletePlaylist,
  sharePlaylist,
  importYoutubePlaylist,
};
