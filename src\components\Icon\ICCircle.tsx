import {SVGProps} from "react";

export default function ICCircle({
  width = 206,
  height = 206,
  ...props
}: SVGProps<SVGSVGElement>): JSX.Element {
  return (
    <svg
      width={width}
      height={height}
      viewBox={`0 0 ${width} ${height}`}
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <circle
        cx="18.0998"
        cy="44.6501"
        r="44.4958"
        fill="white"
        fillOpacity="0.07"
      />
    </svg>
  );
}
