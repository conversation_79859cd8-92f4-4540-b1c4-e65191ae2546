import ApiLibrary from "@api/ApiLibrary";
import QUERY_KEY from "@api/QueryKey";
import ArtistCardSkeleton from "@components/ArtistCardSkeleton";
import CommonArtistCard from "@components/CommonArtistCard";
import IconNoData from "@components/Icon/IconNoData";
import Slider from "@components/Slider";
import {useQuery} from "@tanstack/react-query";
import {useTranslation} from "react-i18next";
import {SwiperSlide} from "swiper/react";
import TabBar from "./components/Tabbar";
import HeaderTitle from "@components/HeaderTitle";

export default function MusicLibrary(): JSX.Element {
  const {t} = useTranslation();
  const {data: artists, isLoading: isArtistLoading} = useQuery({
    queryKey: [QUERY_KEY.LIBRARY.GET_MY_FAVORITE_ARTISTS],
    queryFn: () => ApiLibrary.getMyFavoriteArtist({page: 0, pageSize: 12}),
  });

  return (
    <div
      className="@container/box-library w-full py-5 px-8 @sm/layout:px-4 max-lg:px-4 flex flex-col gap-[52px] max-md:gap-9 max-sm:gap-6 max-[524px]:gap-5 max-[412]: max-sm:text-[18px]"
      style={{userSelect: "none"}}
    >
      <HeaderTitle title={t("common.home_sidebar.favorite")} />
      <div className="flex flex-col gap-2 sm:gap-3 md:gap-4 lg:gap-5">
        <div className="max-sm:text-[18px] font-bold text-white text-[22px]">
          {t("playlist.favorite_artist")}
        </div>
        {isArtistLoading ? (
          <Slider slidesPerView={6}>
            {[...Array(6)].map((_, index) => (
              <SwiperSlide key={index} virtualIndex={index}>
                <ArtistCardSkeleton />
              </SwiperSlide>
            ))}
          </Slider>
        ) : artists?.data?.length ? (
          <Slider slidesPerView={6}>
            {artists?.data?.map((artist, index) => (
              <SwiperSlide key={index} virtualIndex={index}>
                <CommonArtistCard data={artist} />
              </SwiperSlide>
            ))}
          </Slider>
        ) : (
          <div className="text-white w-full h-fit flex-col gap-4 flex justify-center items-center text-lg">
            <IconNoData />
            {t("playlist.not_find_favorite_artist")}
          </div>
        )}
      </div>
      <div className="flex flex-col gap-6 w-full">
        <TabBar />
      </div>
    </div>
  );
}
