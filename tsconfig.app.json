{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,
    /* Bundler mode */
    "moduleResolution": "Bundler",
    "allowImportingTsExtensions": true,
    "isolatedModules": true,
    "moduleDetection": "force",
    "noEmit": true,
    "jsx": "react-jsx",
    /* Linting */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "baseUrl": ".",
    "paths": {
      "@api/*": ["./src/api/*"],
      "@components/*": ["./src/components/*"],
      "@config": ["./src/config"],
      "@i18n/*": ["./src/i18n/*"],
      "@module/*": ["./src/module/*"],
      "@pages/*": ["./src/pages/*"],
      "@redux/*": ["./src/redux/*"],
      "@styles/*": ["./src/styles/*"]
    }
  },
  "include": ["src", "src/assets"]
}
