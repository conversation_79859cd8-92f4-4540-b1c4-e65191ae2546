import {Tab as BaseTab, tabClasses} from "@mui/base/Tab";
import {TabPanel as BaseTabPanel} from "@mui/base/TabPanel";
import {Tabs} from "@mui/base/Tabs";
import {TabsList as BaseTabsList} from "@mui/base/TabsList";
import {styled} from "@mui/system";
import {useRef, useState} from "react";
import {useTranslation} from "react-i18next";
import FavoriteAlbums from "../FavoriteAblums";
import FavoritePlaylist from "../FavoritePlaylist";
import FavoriteSongs from "../FavoriteSongs";
import FavoriteYoutube from "../FavoriteYoutube";

export default function TabBar(): JSX.Element {
  const {t} = useTranslation();
  const favoriteRef = useRef<HTMLDivElement | null>(null);
  const [value, setValue] = useState(1);

  return (
    <Tabs
      value={value}
      onChange={(_e, newValue) => {
        setValue(Number(newValue));
        if (favoriteRef?.current) {
          favoriteRef?.current.scrollIntoView({
            block: "start",
            behavior: "smooth",
          });
        }
      }}
      ref={favoriteRef}
    >
      <TabsList>
        <Tab value={1}>{t("playlist.favorite_song")}</Tab>
        <Tab value={2}>{t("common.playlist")}</Tab>
        <Tab value={3}>{t("common.album")}</Tab>
        <Tab value={4}>Youtube MV</Tab>
      </TabsList>
      <TabPanel value={1}>
        <FavoriteSongs />
      </TabPanel>
      <TabPanel value={2}>
        <FavoritePlaylist />
      </TabPanel>
      <TabPanel value={3}>
        <FavoriteAlbums />
      </TabPanel>
      <TabPanel value={4}>
        <FavoriteYoutube />
      </TabPanel>
    </Tabs>
  );
}

const Tab = styled(BaseTab)`
  color: #e3e3e3;
  cursor: pointer;
  font-size: 16px;
  padding: 8px 12px;
  margin: 6px;
  border-bottom: 2px solid transparent;
  white-space: nowrap;

  &:hover {
    background-color: #ffffff14;
  }

  &.${tabClasses.selected} {
    border-bottom: 2px solid #ff4319;
    font-weight: 500;
    color: #ffffff;
  }
`;

const TabPanel = styled(BaseTabPanel)`
  width: 100%;
  font-size: 0.875rem;
  color: #ffffff;
`;

const TabsList = styled(BaseTabsList)(
  () => `
  margin-bottom: 16px;
  display: flex;
  align-items: flex-start;
  width: 100%;
  overflow-x: scroll;
  scrollbar-width: none;
  `,
);
