import LikeButton from "@components/AuthButton/LikeButton";
import LyricsSong from "@components/LyricsSong";
import {IconButton} from "@mui/material";
import Card from "@mui/material/Card";
import CardActions from "@mui/material/CardActions";
import CardContent from "@mui/material/CardContent";
import CardMedia from "@mui/material/CardMedia";
import Typography from "@mui/material/Typography";
import {toggleLyrics} from "@redux/slices/PlayerSlice";
import {IRootState} from "@redux/store";
import {useTranslation} from "react-i18next";
import {useDispatch, useSelector} from "react-redux";
import {useNavigate} from "react-router-dom";
import {IPlaylist} from "src/types";
import {useMemo} from "react";
import Controller from "src/layout/components/BottomPlayer/Controller";
import IconShare from "@components/Icon/IconShare";
import {handleLikeSong} from "src/utils/like";
import {shortenNumber} from "src/utils/numberUtils";
import IconListen from "@components/Icon/IconListen";

interface DesktopDetailProps {
  handleOpenModalShare: () => void;
}

export default function DesktopDetail({
  handleOpenModalShare,
}: DesktopDetailProps): JSX.Element {
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const {t} = useTranslation();
  const {currentSong, currentPlaylistId} = useSelector(
    (state: IRootState) => state?.player,
  );

  const playlistUrl = useMemo(() => {
    return currentPlaylistId
      ? currentSong?.playlists?.find(
          (playlist: IPlaylist) => playlist?.id === currentPlaylistId,
        )?.urlSlug || currentSong?.playlists?.[0]?.urlSlug
      : currentSong?.playlists?.[0]?.urlSlug || "unknown-playlist";
  }, [currentSong, currentPlaylistId]);

  return (
    <div className="flex flex-col flex-1 items-center pt-4 justify-evenly">
      <div className="flex flex-row items-start justify-center w-fit min-w-full h-[calc(100vh-270px)] gap-[10vh] box-border">
        <Card
          sx={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            maxWidth: "30vw",
            width: "480px",
            background: "#FFFFFF12",
            color: "white",
            paddingTop: "20px",
            paddingRight: "20px",
            paddingLeft: "20px",
            borderRadius: "12px",
          }}
        >
          <CardMedia
            component="img"
            alt="green iguana"
            sx={{
              borderRadius: "16px",
              aspectRatio: "1/1",
              ["@media (max-height: 834px)"]: {
                width: "25vh",
              },
            }}
            image={
              currentSong?.images?.DEFAULT ||
              currentSong?.images?.SMALL ||
              "/image/default-music.png"
            }
          />
          <CardContent
            sx={{
              paddingRight: "0px",
              paddingLeft: "0px",
              paddingBottom: "0px",
            }}
          >
            <Typography
              gutterBottom
              variant="h5"
              component="div"
              className="cursor-pointer line-clamp-2"
              onClick={() => {
                navigate(`/playlist/${playlistUrl}`);
                dispatch(toggleLyrics());
              }}
            >
              {currentSong?.name}
            </Typography>
            <Typography variant="body2" sx={{color: "#FFFFFF80"}}>
              {Array.isArray(currentSong?.artists) &&
              currentSong?.artists?.length > 0
                ? currentSong?.artists?.map((artist, index) => (
                    <span key={artist.id}>
                      <span
                        onClick={() => {
                          dispatch(toggleLyrics());
                          navigate(`/artist/${artist.urlSlug || "unknown"}`);
                        }}
                        className="cursor-pointer hover:text-sky-500 hover:underline"
                      >
                        {artist?.stageName ?? artist?.name}
                      </span>
                      {index < (currentSong?.artists?.length || 0) - 1 && ", "}
                    </span>
                  ))
                : t("common.not_info_artist")}
            </Typography>
          </CardContent>
          <CardActions
            sx={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              gap: 1,
            }}
          >
            <div className="flex items-center gap-x-1">
              <IconListen />
              <span className="text-[#FFFFFF80]">
                {shortenNumber(currentSong?.totalListens ?? 0)}
              </span>
            </div>
            <div className="flex items-center gap-x-1">
              <LikeButton
                isLiked={currentSong?.isLiked}
                songId={currentSong?.id}
                className="song-action hover:rounded-full"
                action={() => handleLikeSong(currentSong)}
              />
              <span
                className={`text-[#FFFFFF80] total-likes song-${currentSong?.id}-liking`}
              >
                {shortenNumber(currentSong?.totalLikes ?? 0)}
              </span>
            </div>
            <div className="flex items-center gap-x-1">
              <IconButton
                className="cursor-pointer !p-0"
                onClick={handleOpenModalShare}
              >
                <IconShare />
              </IconButton>
              <span className="text-[#FFFFFF80]">
                {shortenNumber(currentSong?.totalShares ?? 0)}
              </span>
            </div>
          </CardActions>
        </Card>
        {currentSong && (
          <div className="flex-1">
            <LyricsSong className="flex-1 overflow-y-scroll max-md:w-full h-[calc(100vh-300px)] text-start max-md:text-center" />
          </div>
        )}
      </div>
      {currentSong && (
        <div className="text-white py-1 line-clamp-3 mb-4">
          {currentSong?.name} -{" "}
          {currentSong?.artists
            ?.map((artist) => artist?.stageName ?? artist?.name)
            .join(", ") || t("common.not_info_artist")}
        </div>
      )}
      <Controller className="w-full md:w-5/6 text-white" inLyricDetail />
    </div>
  );
}
