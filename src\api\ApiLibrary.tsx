import {IArtist, IPlaylist, IPlaylistParam, ISong} from "src/types";
import {fetcher, fetcherWithMetadata, IDataWithMeta} from "./Fetcher";

const path = {
  createPlaylist: "/playlists",
  myPlaylists: "/playlists/my-playlists",
  myFavoritePlaylist: "/playlists/liked",
  myArtistsFavorite: "/artists/liked",
  myFavoriteSong: "/songs/liked",
};

function getMyPlaylists(
  params: IPlaylistParam,
): Promise<IDataWithMeta<IPlaylist[]>> {
  return fetcherWithMetadata(
    {
      url: path.myPlaylists,
      method: "get",
      params,
    },
    {
      displayError: false,
    },
  );
}

function getMyFavoriteArtist(
  params: IPlaylistParam,
): Promise<IDataWithMeta<IArtist[]>> {
  return fetcherWithMetadata(
    {
      url: path.myArtistsFavorite,
      method: "get",
      params,
    },
    {
      displayError: false,
    },
  );
}

function getMyFavoritePlaylist(
  params: IPlaylistParam,
): Promise<IDataWithMeta<IPlaylist[]>> {
  return fetcherWithMetadata(
    {
      url: path.myFavoritePlaylist,
      method: "get",
      params,
    },
    {
      displayError: false,
    },
  );
}

function getMyFavoriteSong(
  params: IPlaylistParam,
): Promise<IDataWithMeta<ISong[]>> {
  return fetcherWithMetadata(
    {
      url: path.myFavoriteSong,
      method: "get",
      params,
    },
    {
      displayError: false,
    },
  );
}

function createNewPlaylist(body: {
  name: string;
  isPublic: boolean;
}): Promise<IPlaylist> {
  return fetcher(
    {
      url: path.createPlaylist,
      method: "post",
      data: body,
    },
    {
      displayError: false,
    },
  );
}

export default {
  getMyPlaylists,
  getMyFavoriteArtist,
  getMyFavoritePlaylist,
  getMyFavoriteSong,
  createNewPlaylist,
};
