import {SVGProps} from "react";
const IconCopy = ({...props}: SVGProps<SVGSVGElement>) => (
  <svg
    className={props.className}
    width={props.width || "24"}
    height={props.height || "25"}
    viewBox="0 0 24 25"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M20 8.36035H10C8.89543 8.36035 8 9.25578 8 10.3604V20.3604C8 21.4649 8.89543 22.3604 10 22.3604H20C21.1046 22.3604 22 21.4649 22 20.3604V10.3604C22 9.25578 21.1046 8.36035 20 8.36035Z"
      stroke="white"
      strokeOpacity="0.63"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M4 16.3604C2.9 16.3604 2 15.4604 2 14.3604V4.36035C2 3.26035 2.9 2.36035 4 2.36035H14C15.1 2.36035 16 3.26035 16 4.36035"
      stroke="white"
      strokeOpacity="0.63"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);
export default IconCopy;
