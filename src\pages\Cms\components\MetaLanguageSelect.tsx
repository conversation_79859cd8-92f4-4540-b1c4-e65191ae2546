import CommonSelect from "@components/CommonSelect";
import {useTranslation} from "react-i18next";

interface IMetaLanguageSelectProps {
  onChange: (value: string) => void;
  initValue?: string;
}

const MetaLanguageSelect = ({
  onChange,
  initValue,
}: IMetaLanguageSelectProps) => {
  const {t} = useTranslation();
  const languages = [
    {id: "vi", name: t("common.language.vietnamese")},
    {id: "en", name: t("common.language.english")},
    {id: "lo", name: t("common.language.lao")},
  ];
  return (
    <CommonSelect
      selectProps={{
        size: "small",
      }}
      values={languages}
      onChange={onChange}
      placeholder={t("common.language.language")}
      initValue={initValue}
    />
  );
};

export default MetaLanguageSelect;
