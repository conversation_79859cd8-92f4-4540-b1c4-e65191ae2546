import {useTranslation} from "react-i18next";
import {useQuery} from "@tanstack/react-query";
import {Grid} from "@mui/material";
import QUERY_KEY from "@api/QueryKey";
import ApiRecent from "@api/ApiRecent";
import {ESongType} from "src/types";
import Subtitle from "@components/Subtitle";
import CommonYoutubeCard from "@components/CommonYoutubeCard";
import IconNoData from "@components/Icon/IconNoData";
import YoutubeCardSkeleton from "@components/YoutubeCardSkeleton";
import {useDispatch} from "react-redux";
import {playSongFromList} from "@redux/slices/PlayerSlice";

export default function YoutubeRecent(): JSX.Element {
  const {t} = useTranslation();
  const dispatch = useDispatch();

  const {data, isLoading} = useQuery({
    queryKey: [
      QUERY_KEY.RECENT.GET_RECENT_YOUTUBE,
      {
        page: 0,
        pageSize: 15,
        type: ESongType.YOUTUBE,
      },
    ],
    queryFn: () =>
      ApiRecent.recentSongs({
        page: 0,
        pageSize: 15,
        type: ESongType.YOUTUBE,
      }),
  });

  return (
    <div className="w-full flex flex-col gap-1 sm:gap-2 md:gap-3 lg:gap-4 mb-[3vh]">
      <Subtitle subtitle="Youtube MV" seeMore={false} />
      {isLoading && !data && (
        <Grid
          container
          spacing={{xs: 2, md: 3}}
          columns={{xs: 2, sm: 6, md: 12, lg: 15}}
        >
          {[...Array(5)].map((_, index) => (
            <Grid item xs={1} sm={2} md={3} lg={3} key={index}>
              <YoutubeCardSkeleton />
            </Grid>
          ))}
        </Grid>
      )}
      {!isLoading && data?.data?.length === 0 && (
        <div className=" flex justify-center items-center flex-col lg:gap-2.5 gap-1">
          <IconNoData />
          <span className="text-[#E3E3E3] w-full text-base font-normal flex justify-center">
            {t("common.song_list_not_found")}
          </span>
        </div>
      )}
      <div>
        <Grid
          container
          spacing={{xs: 2, md: 3}}
          columns={{xs: 2, sm: 6, md: 12, lg: 15}}
        >
          {!isLoading &&
            data?.data?.map((item, index) => (
              <Grid item xs={1} sm={2} md={3} lg={3} key={index}>
                <CommonYoutubeCard
                  data={item}
                  handlePlayMusic={() =>
                    dispatch(
                      playSongFromList({song: item, songList: data.data}),
                    )
                  }
                />
              </Grid>
            ))}
        </Grid>
      </div>
    </div>
  );
}
