import {SVGProps} from "react";

function IconCancel({
  width = "14",
  height = "14",
  ...props
}: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox={`0 0 ${width} ${height}`}
      className={props.className}
      fill="none"
      {...props}
    >
      <path
        fill={props.fill || "currentColor"}
        d="m7.81 7 4.1-4.89a.124.124 0 0 0-.095-.204H10.57a.255.255 0 0 0-.193.089L6.994 6.027 3.61 1.995a.25.25 0 0 0-.192-.09H2.172a.124.124 0 0 0-.096.205L6.178 7l-4.102 4.888a.124.124 0 0 0 .096.205h1.247a.255.255 0 0 0 .192-.089l3.383-4.033 3.382 4.033a.25.25 0 0 0 .193.089h1.246a.124.124 0 0 0 .096-.205L7.809 7Z"
      />
    </svg>
  );
}

export default IconCancel;
