import ApiRecent from "@api/ApiRecent";
import {IDataWithMeta} from "@api/Fetcher";
import QUERY_KEY from "@api/QueryKey";
import IconNoData from "@components/Icon/IconNoData";
import SongCardSkeleton from "@components/SongCardSkeleton";
import SongItem from "@components/SongItem";
import Subtitle from "@components/Subtitle";
import {playSongFromList} from "@redux/slices/PlayerSlice";
import {useInfiniteQuery} from "@tanstack/react-query";
import {useEffect, useMemo, useRef} from "react";
import {useTranslation} from "react-i18next";
import {useDispatch} from "react-redux";
import {ESongType, ISong} from "src/types";

export default function SongRecent(): JSX.Element {
  const {t} = useTranslation();

  const observer = useRef<IntersectionObserver | null>(null);
  const dispatch = useDispatch();
  const lastElementRef = useRef<HTMLDivElement | null>(null);

  const {data, fetchNextPage, isLoading, isError, hasNextPage} =
    useInfiniteQuery<IDataWithMeta<ISong[]>, Error>({
      queryKey: [QUERY_KEY.RECENT.GET_RECENT_SONGS],
      queryFn: ({pageParam = 0}) =>
        ApiRecent.recentSongs({
          pageSize: 20,
          page: pageParam as number,
          type: ESongType.SONG,
        }),
      getNextPageParam: (lastPage) =>
        lastPage?.meta?.totalPage - 1 > lastPage?.meta?.currentPage
          ? (lastPage?.meta?.currentPage || 0) + 1
          : undefined,
      initialPageParam: 0,
    });

  const songList = useMemo(() => {
    return data?.pages.flatMap((page) => page.data) || [];
  }, [data]);

  useEffect(() => {
    if (isLoading || isError) return;

    if (observer.current) observer.current.disconnect();

    observer.current = new IntersectionObserver((entries) => {
      if (entries[0].isIntersecting && hasNextPage) {
        fetchNextPage();
      }
    });

    if (lastElementRef?.current) {
      observer.current.observe(lastElementRef.current);
    }

    return () => {
      if (observer.current) {
        observer.current.disconnect();
      }
    };
  }, [isLoading, isError, data, hasNextPage]);

  const isEmpty =
    !data || !data.pages || data.pages.every((page) => page.data.length === 0);

  return (
    <div className="w-full flex flex-col gap-1 sm:gap-2 md:gap-3 lg:gap-4">
      <Subtitle subtitle={t("common.songs")} seeMore={false} />
      {isLoading && !data && (
        <div className="flex flex-col">
          {[...Array(5)].map((_, index) => (
            <SongCardSkeleton key={`skeleton_${index}`} />
          ))}
        </div>
      )}
      {!isLoading && (isError || isEmpty) && (
        <div className="flex justify-center items-center flex-col gap-2.5">
          <IconNoData />
          <span className="text-[#E3E3E3] w-full text-base font-normal flex justify-center">
            {t("common.song_list_not_found")}
          </span>
        </div>
      )}
      <div>
        {!isLoading &&
          !(isError || isEmpty) &&
          songList?.map((song, index) => {
            return (
              <SongItem
                song={song}
                key={`recent_song_${index}`}
                left={
                  <div className="flex gap-x-6 items-center mr-6">
                    <div className="text-base text-center w-10 text-white font-normal">
                      {index + 1}
                    </div>
                  </div>
                }
                handlePlayMusic={() => {
                  dispatch(playSongFromList({song, songList}));
                }}
              />
            );
          })}
        {hasNextPage && (
          <div className="flex flex-col">
            <SongCardSkeleton />
          </div>
        )}
      </div>
      {!isLoading && <div ref={lastElementRef} className="h-1"></div>}
    </div>
  );
}
