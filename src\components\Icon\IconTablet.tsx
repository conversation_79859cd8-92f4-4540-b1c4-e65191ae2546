import {SVGProps} from "react";

function IconTablet(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={25}
      height={24}
      viewBox="0 0 25 24"
      fill="none"
      {...props}
    >
      <path
        d="M3.8125 6.375C3.8125 5.87772 4.01004 5.40081 4.36167 5.04917C4.71331 4.69754 5.19022 4.5 5.6875 4.5H18.8125C19.3098 4.5 19.7867 4.69754 20.1383 5.04917C20.49 5.40081 20.6875 5.87772 20.6875 6.375V17.625C20.6875 18.1223 20.49 18.5992 20.1383 18.9508C19.7867 19.3025 19.3098 19.5 18.8125 19.5H5.6875C5.19022 19.5 4.71331 19.3025 4.36167 18.9508C4.01004 18.5992 3.8125 18.1223 3.8125 17.625V6.375Z"
        stroke="black"
        strokeWidth="1.8"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M9.4375 16.6875H15.0625"
        stroke="black"
        strokeWidth="1.8"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}

export default IconTablet;
