import Subtitle from "@components/Subtitle";
import {Grid} from "@mui/material";
import {useTranslation} from "react-i18next";
import {useLocation} from "react-router-dom";
import {useQuery} from "@tanstack/react-query";
import ApiSearch from "@api/ApiSearch";
import QUERY_KEY from "@api/QueryKey";
import YoutubeCardSkeleton from "@components/YoutubeCardSkeleton";
import IconNoData from "@components/Icon/IconNoData";
import CommonPlaylistYTBCard from "@components/CommonPlaylistYTBCard";

export function YoutubePlaylistView() {
  const {t} = useTranslation();

  const location = useLocation();
  const query = new URLSearchParams(location.search);
  const searchValue = query.get("q");

  const {data, isLoading} = useQuery({
    queryKey: [
      QUERY_KEY.SEARCH.GET_SEARCH_YOUTUBE_PLAYLIST,
      {
        keyword: searchValue || "",
        page: 0,
        pageSize: 15,
      },
    ],
    queryFn: () =>
      ApiSearch.searchYoutubePlaylist({
        keyword: searchValue || "",
        page: 0,
        pageSize: 15,
      }),
    enabled: !!searchValue,
  });

  return (
    <div className="w-full flex flex-col gap-1 sm:gap-2 md:gap-3 lg:gap-4 mb-[3vh]">
      <Subtitle subtitle={t("common.youtube_playlist")} seeMore={false} />
      {isLoading && !data && (
        <Grid
          container
          spacing={{xs: 2, md: 3}}
          columns={{xs: 2, sm: 6, md: 12, lg: 15}}
        >
          {[...Array(5)].map((_, index) => (
            <Grid xs={1} sm={2} md={3} lg={3} key={index}>
              <YoutubeCardSkeleton />
            </Grid>
          ))}
        </Grid>
      )}
      {!isLoading && data?.data?.length === 0 && (
        <div className=" flex justify-center items-center flex-col lg:gap-2.5 gap-1">
          <IconNoData />
          <span className="text-[#E3E3E3] w-full text-base font-normal flex justify-center">
            {t("common.mv_list_not_found")}
          </span>
        </div>
      )}
      <div>
        <Grid
          container
          spacing={{xs: 2, md: 3}}
          columns={{xs: 2, sm: 6, md: 12, lg: 15}}
        >
          {!isLoading &&
            data?.data?.map((item, index) => (
              <Grid item xs={1} sm={2} md={3} lg={3} key={index}>
                <CommonPlaylistYTBCard data={item} />
              </Grid>
            ))}
        </Grid>
      </div>
    </div>
  );
}
