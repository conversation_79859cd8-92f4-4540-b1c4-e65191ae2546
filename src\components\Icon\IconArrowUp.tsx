import {SVGProps} from "react";

function IconArrowUp({
  width = "20",
  height = "20",
  ...props
}: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox={`0 0 ${width} ${height}`}
      className={props.className}
      fill="none"
      {...props}
    >
      <path
        d="M13.7929 12.5C14.2383 12.5 14.4614 11.9614 14.1464 11.6464L10.3536 7.85355C10.1583 7.65829 9.84171 7.65829 9.64645 7.85355L5.85355 11.6464C5.53857 11.9614 5.76165 12.5 6.20711 12.5L13.7929 12.5Z"
        fill={props.fill || "#ffffff"}
      />
    </svg>
  );
}

export default IconArrowUp;
