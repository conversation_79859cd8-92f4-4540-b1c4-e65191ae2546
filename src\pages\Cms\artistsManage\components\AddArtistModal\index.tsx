import {
  Dialog,
  DialogContent,
  DialogTitle,
  MenuItem,
  Select,
  IconButton,
} from "@mui/material";
import {Form, Formik} from "formik";
import {toast} from "react-toastify";
import * as Yup from "yup";
import CloseIcon from "@mui/icons-material/Close";
import ImageCropper from "@components/ImageCropper";
import {useTranslation} from "react-i18next";
import {useMutation, useQueryClient} from "@tanstack/react-query";
import ApiArtist from "@api/ApiArtist";
import {
  useArtistTypeOptions,
  useCountryOptions,
  useGenderOptions,
} from "src/utils/global";
import dayjs from "dayjs";
import {CountryEnum, EArtistType, GenderEnum} from "src/types";
import {LocalizationProvider} from "@mui/x-date-pickers/LocalizationProvider/LocalizationProvider";
import {AdapterDayjs} from "@mui/x-date-pickers/AdapterDayjs";
import {DatePicker} from "@mui/x-date-pickers/DatePicker";
import TextFieldCustomer from "@components/TextFieldCustomer";

interface AddArtistModalProps {
  isOpen: boolean;
  onClose: () => void;
  refetchList: () => void;
}

export default function AddArtistModal({
  isOpen,
  onClose,
  refetchList,
}: AddArtistModalProps) {
  const {t} = useTranslation();
  const queryClient = useQueryClient();
  const genderOptions = useGenderOptions();
  const artistTypeOptions = useArtistTypeOptions();
  const countryOptions = useCountryOptions();

  // Validation schema
  const validationSchema = Yup.object({
    image: Yup.mixed()
      .test(
        "size",
        t("validation.file_size_limit_exceed", {size: "10MB"}),
        (file) => {
          return file && (file as File).size < 10 * 1024 * 1024;
        },
      )
      .required(t("validation.field_is_require", {field: t("common.cover")})),
    name: Yup.string().required(
      t("validation.field_is_require", {field: t("cms.artist.artist_name")}),
    ),
    gender: Yup.string().required(
      t("validation.field_is_require", {field: t("common.gender")}),
    ),
    country: Yup.string().required(
      t("validation.field_is_require", {field: t("common.country")}),
    ),
  });

  const initialValues = {
    image: "",
    name: "",
    stageName: "",
    dateOfBirth: "",
    gender: "",
    type: "",
    company: "",
    country: "",
    biography: "",
  };

  const handleSubmit = async (values: typeof initialValues) => {
    await addArtistMutation.mutate({
      ...values,
      dateOfBirth:
        values.dateOfBirth && dayjs(values.dateOfBirth).isValid()
          ? dayjs(values.dateOfBirth).format("YYYY-MM-DD")
          : "",
      gender: values.gender as unknown as GenderEnum,
      type:
        values.type != null ? (Number(values.type) as EArtistType) : undefined,
    });
  };

  const addArtistMutation = useMutation({
    mutationFn: ApiArtist.addArtist,
    onSuccess: () => {
      queryClient.invalidateQueries({queryKey: ["artists"]});
      toast.success(t("cms.artist.add_artist_success"));
      refetchList();
      onClose();
    },
  });

  return (
    <Dialog open={isOpen} onClose={onClose} maxWidth="sm" fullWidth>
      <div className="flex items-center justify-between border-b border-gray-300 pr-4">
        <DialogTitle className="p-0 !text-base !font-bold text-[#000000D9]">
          {t("cms.artist.add_new_artist")}
        </DialogTitle>
        <IconButton
          aria-label="close"
          onClick={onClose}
          className="text-gray-500 hover:text-[#000000D9]"
          size="small"
        >
          <CloseIcon />
        </IconButton>
      </div>

      <DialogContent className="!pb-0">
        <Formik
          enableReinitialize={true}
          initialValues={initialValues}
          validationSchema={validationSchema}
          onSubmit={handleSubmit}
        >
          {({
            values,
            errors,
            touched,
            handleChange,
            handleBlur,
            setFieldValue,
          }) => (
            <Form className="flex flex-col gap-4">
              {/* Image Upload */}
              <div className="flex flex-col gap-2">
                <label className="text-sm font-bold text-gray-700">
                  {t("common.cover")}
                  <span className="text-red-600"> *</span>
                </label>
                <div>
                  <ImageCropper
                    onChange={(file) => {
                      setFieldValue("image", file);
                    }}
                    className="w-[120px] aspect-square object-cover"
                  />
                  {errors.image && touched.image && (
                    <div className="text-red-500 text-sm">{errors.image}</div>
                  )}
                </div>
              </div>
              {/* Artist Name */}
              <TextFieldCustomer
                name="name"
                required
                value={values.name}
                onChange={handleChange("name")}
                label={t("cms.artist.artist_name")}
                placeholder={t("common.full_name")}
              />
              {/* Stage Name */}
              <TextFieldCustomer
                name="stageName"
                value={values.stageName}
                onChange={handleChange("stageName")}
                label={t("cms.artist.stage_name")}
                placeholder={t("cms.artist.stage_name")}
              />

              <div className="flex gap-4 w-full">
                <div className="flex flex-col gap-2 w-1/2">
                  <label className="text-sm font-bold text-gray-700">
                    {t("common.birthday")}
                  </label>
                  <LocalizationProvider dateAdapter={AdapterDayjs}>
                    <DatePicker
                      name="dateOfBirth"
                      disableFuture
                      onChange={(event) => setFieldValue("dateOfBirth", event)}
                      slotProps={{
                        textField: {
                          size: "small",
                          placeholder: t("common.birthday"),
                        },
                        field: {
                          clearable: true,
                        },
                      }}
                    />
                  </LocalizationProvider>
                </div>

                <div className="flex flex-col gap-2 w-1/2">
                  <label className="text-sm font-bold text-gray-700">
                    {t("common.gender")}
                    <span className="text-red-600"> *</span>
                  </label>
                  <div>
                    <Select
                      name="gender"
                      value={values.gender}
                      onChange={(event) =>
                        setFieldValue("gender", Number(event.target.value))
                      }
                      onBlur={handleBlur}
                      size="small"
                      displayEmpty
                      className="rounded text-sm w-full"
                      renderValue={(selected) => {
                        if (selected !== "") {
                          return (
                            <span className="text-sm">
                              {genderOptions.find(
                                (option) =>
                                  option.value ===
                                  (selected as unknown as GenderEnum),
                              )?.label ?? ""}
                            </span>
                          );
                        }
                        return (
                          <span className="text-[#BFBFBF] font-normal text-sm">
                            {t("common.select_gender")}
                          </span>
                        );
                      }}
                    >
                      {genderOptions.map((status) => (
                        <MenuItem key={status.value} value={status.value}>
                          {status.label}
                        </MenuItem>
                      ))}
                    </Select>
                    {errors.gender && touched.gender && (
                      <div className="text-red-500 text-sm">
                        {errors.gender}
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Artist Type */}
              <div className="flex flex-col gap-2">
                <label className="text-sm font-bold text-gray-700">
                  {t("cms.artist.artist_type")}
                </label>
                <Select
                  name="type"
                  value={values.type}
                  onChange={(event) =>
                    setFieldValue("type", Number(event.target.value))
                  }
                  displayEmpty
                  size="small"
                  className="w-full text-sm"
                  renderValue={(selected) => {
                    if (
                      selected === "" ||
                      selected === undefined ||
                      selected === null
                    ) {
                      return (
                        <span className="text-[#acb1bc] font-normal text-sm">
                          {t("cms.artist.artist_type")}
                        </span>
                      );
                    }

                    const selectedArray = Array.isArray(selected)
                      ? selected
                      : [selected];

                    const selectedLabels = selectedArray
                      .map(
                        (value) =>
                          artistTypeOptions.find(
                            (option) => option.value === Number(value),
                          )?.label || value,
                      )
                      .join(", ");

                    return <span className="text-sm">{selectedLabels}</span>;
                  }}
                >
                  {artistTypeOptions.map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </Select>
              </div>

              {/* Company */}
              <TextFieldCustomer
                name="company"
                value={values.company}
                onChange={handleChange("company")}
                label={t("cms.artist.management_company")}
                placeholder={t("cms.artist.management_company")}
              />

              <div className="flex flex-col gap-2">
                <label className="text-sm font-bold text-gray-700">
                  {t("common.country")}
                  <span className="text-red-600"> *</span>
                </label>
                <div>
                  <Select
                    name="country"
                    value={values.country}
                    onChange={(event) => {
                      setFieldValue("country", event.target.value);
                    }}
                    onBlur={handleBlur}
                    size="small"
                    displayEmpty
                    className="rounded text-sm w-full"
                    renderValue={(selected) => {
                      if (selected !== "" && selected !== undefined) {
                        return (
                          <span className="text-sm">
                            {countryOptions.find(
                              (option) =>
                                option.value ===
                                (selected as unknown as CountryEnum),
                            )?.label || selected}
                          </span>
                        );
                      }
                      return (
                        <span className="text-[#BFBFBF] font-normal text-sm">
                          {t("common.country")}
                        </span>
                      );
                    }}
                  >
                    {countryOptions.map((option) => (
                      <MenuItem key={option.value} value={option.value}>
                        {option.label}
                      </MenuItem>
                    ))}
                  </Select>
                  {touched.country && errors.country && (
                    <div className="text-red-500 text-sm">{errors.country}</div>
                  )}
                </div>
              </div>
              <div className="flex flex-col gap-2">
                <label className="text-sm font-bold text-gray-700">
                  {t("common.biographical_description")}
                </label>
                <textarea
                  name="biography"
                  value={values.biography}
                  onChange={handleChange}
                  placeholder={t("cms.artist.bio_artist")}
                  className="border border-gray-300 rounded p-2 text-sm w-full"
                />
              </div>

              <div className="flex justify-end space-x-2 border-t border-gray-300 py-2.5">
                <button
                  type="button"
                  onClick={onClose}
                  className="rounded-lg px-4 py-2 text-sm text-gray-600 hover:bg-gray-100"
                >
                  {t("common.cancel")}
                </button>
                <IconButton
                  type="submit"
                  loading={addArtistMutation.isPending}
                  disabled={addArtistMutation.isPending}
                  className={`!rounded-lg !px-4 !text-base !text-white w-[102px]
                ${addArtistMutation.isPending ? "!bg-gray-400 cursor-not-allowed" : "!bg-orange-500 hover:bg-red-600"}
              `}
                >
                  {t("common.confirm")}
                </IconButton>
              </div>
            </Form>
          )}
        </Formik>
      </DialogContent>
    </Dialog>
  );
}
