import IconClose from "@components/Icon/IconClose";
import {
  Button,
  Dialog,
  DialogContent,
  DialogTitle,
  IconButton,
} from "@mui/material";
import {styled} from "@mui/system";
import {Tabs as BaseTabs} from "@mui/base/Tabs";
import {TabsList as BaseTabsList} from "@mui/base/TabsList";
import {TabPanel as BaseTabPanel} from "@mui/base/TabPanel";
import {buttonClasses} from "@mui/base/Button";
import {Tab as BaseTab, tabClasses} from "@mui/base/Tab";
import {useTranslation} from "react-i18next";
import MultipleLanguage from "./component/MultipleLanguage";
import {useState} from "react";
import ListenMode from "./component/ListenMode";

interface ModalSettingGeneralProps {
  open: boolean;
  onCancel: () => void;
}

export default function ModalSettingGeneral({
  open,
  onCancel,
}: ModalSettingGeneralProps) {
  const {t} = useTranslation();
  const [value, setValue] = useState(0);

  const handleChange = (
    _event: React.SyntheticEvent | null,
    newValue: string | number | null,
  ) => {
    if (newValue !== null) {
      setValue(newValue as number);
    }
  };

  return (
    <Dialog
      open={open}
      onClose={onCancel}
      maxWidth="md"
      fullWidth
      classes={{paper: "rounded-lg"}}
      PaperProps={{
        style: {
          background: "linear-gradient(180deg, #141414 0%, #1C1C1C 100%)",
          color: "white",
          boxShadow: "0px 0px 6px 0px #1B28360D, 0px 10px 15px 0px #1B28361A",
          borderRadius: "12px",
          fontSize: "16px",
          fontWeight: "400",
          minHeight: "420px",
          margin: 0,
          width: "calc(100% - 16px)",
        },
      }}
    >
      <div className="flex items-center justify-between border-b border-[#FFFFFF12]">
        <DialogTitle className="text-base font-bold">
          {t("common.general_setting")}
        </DialogTitle>
        <div className="pr-5">
          <IconButton aria-label="close" onClick={onCancel} size="small">
            <IconClose className="h-6 w-6" fill="#FFFFFF" />
          </IconButton>
        </div>
      </div>

      <DialogContent className="!p-0">
        <Tabs
          value={value}
          onChange={handleChange}
          orientation="vertical"
          sx={{
            ["@media (max-width: 568px)"]: {
              display: "flex",
              flexDirection: "column",
            },
          }}
        >
          <TabsList>
            <Tab>{t("common.setting.listening_mode")}</Tab>
            <Tab>{t("common.language.language")}</Tab>
          </TabsList>
          <TabPanel value={0}>
            <ListenMode />
          </TabPanel>
          <TabPanel value={1}>
            <MultipleLanguage />
          </TabPanel>
        </Tabs>
      </DialogContent>

      <div className="flex justify-end border-t border-[#FFFFFF12] px-5 py-4 gap-2">
        <Button
          className="!bg-orange-500 !py-2.5 !px-8 !rounded-lg !text-[#FBFDFF] !w-[126px]"
          sx={{textTransform: "none"}}
          onClick={() => onCancel()}
        >
          {t("common.confirm")}
        </Button>
      </div>
    </Dialog>
  );
}

const Tab = styled(BaseTab)`
  color: #ffffffa1;
  cursor: pointer;
  font-size: 14px;
  font-weight: 400;
  width: 100%;
  padding: 20px;
  border-top: none;
  border-right: 1px solid #ffffff12;
  border-bottom: 1px solid #ffffff12;
  border-left: none;
  display: flex;
  justify-content: flex-start !important;
  align-items: flex-start !important;
  text-align: left;

  @media (max-width: 568px) {
    padding-inline: 10px;
    justify-content: center !important;
    align-items: center !important;
  }

  &:hover {
    background-color: #ffffff12;
  }

  &:focus {
    color: #ffffff;
  }

  &.${tabClasses.selected} {
    color: #ffffff;
  }

  &.${buttonClasses.disabled} {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const TabPanel = styled(BaseTabPanel)`
  width: 100%;
  font-size: 0.875rem;
  color: #ffffff;
  padding: 20px;

  @media (max-width: 568px) {
    padding: 10px;
  }
`;

const Tabs = styled(BaseTabs)`
  display: flex;
`;

const TabsList = styled(BaseTabsList)`
  display: flex;
  flex-direction: column;
  width: 32%;
  justify-content: flex-start !important;
  align-items: flex-start !important;
  border-right: 1px solid #ffffff12;

  @media (max-width: 568px) {
    width: 100%;
    flex-direction: row;
  }
`;
