import {
  Box,
  Dialog,
  DialogContent,
  DialogTitle,
  IconButton,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import InformationArtist from "./component/Information";
import ListSongArtist from "./component/ListSong";
import {useTranslation} from "react-i18next";
import {IArtist} from "src/types";
import ListAlbumArtist from "./component/ListAlbum";
import TabContext from "@mui/lab/TabContext";
import TabList from "@mui/lab/TabList";
import TabPanel from "@mui/lab/TabPanel";
import {Tab} from "@mui/material";
import {useState} from "react";

interface ArtistDetailModalProps {
  isOpen: boolean;
  onClose: () => void;
  dataArtist: IArtist;
  artistId: string | null;
  fetcherDataArtist: boolean;
}

export default function ArtistDetailModal({
  isOpen,
  onClose,
  dataArtist,
  artistId,
  fetcherDataArtist,
}: ArtistDetailModalProps) {
  const {t} = useTranslation();
  const [tabValue, setTabValue] = useState("1");

  const tabPanelArray = [
    {value: "1", label: t("cms.artist.basic_information")},
    {value: "2", label: t("cms.artist.song_list")},
    {value: "3", label: t("cms.artist.album_list")},
  ];

  const tabComponentArray = [
    {
      value: "1",
      component: (
        <InformationArtist
          dataArtist={dataArtist}
          fetcherDataArtist={fetcherDataArtist}
        />
      ),
    },
    {
      value: "2",
      component: <ListSongArtist artistId={artistId} />,
    },
    {
      value: "3",
      component: <ListAlbumArtist artistId={artistId} />,
    },
  ];

  return (
    <Dialog open={isOpen} onClose={onClose} maxWidth="lg" fullWidth>
      <div className="flex items-center justify-between border-b border-gray-300 pr-4">
        <DialogTitle className="p-0 !text-base !font-bold text-[#242728]">
          {t("cms.artist.artist_details")}
        </DialogTitle>
        <IconButton
          aria-label="close"
          onClick={onClose}
          className="text-gray-500 hover:text-gray-800"
          size="small"
        >
          <CloseIcon />
        </IconButton>
      </div>
      <DialogContent>
        <Box sx={{width: "100%", typography: "body1"}}>
          <div className="flex flex-col gap-3">
            <TabContext value={tabValue}>
              <TabList
                className="cms-tabs border-b border-[#D9D9D9]"
                onChange={(_event: React.SyntheticEvent, newValue: string) =>
                  setTabValue(newValue)
                }
              >
                {tabPanelArray?.map((item) => (
                  <Tab
                    sx={{
                      textTransform: "none",
                    }}
                    label={item.label}
                    value={item.value}
                    key={item.value}
                  />
                ))}
              </TabList>
              {tabComponentArray?.map((item) => (
                <TabPanel sx={{padding: 0}} value={item.value} key={item.value}>
                  {item.component}
                </TabPanel>
              ))}
            </TabContext>
          </div>
        </Box>
      </DialogContent>
    </Dialog>
  );
}
