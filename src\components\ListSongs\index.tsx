import {useState} from "react";
import {useTranslation} from "react-i18next";
import IconArrowRight2 from "@components/Icon/IconArrowRight2";
import SongItem from "@components/SongItem";
import {ISong} from "src/types";
import {useDispatch} from "react-redux";
import {playSongFromList} from "@redux/slices/PlayerSlice";
import SongCardSkeleton from "@components/SongCardSkeleton";

interface IListSongProps {
  data?: ISong[];
  isLoading?: boolean;
}

export default function ListSongs({
  data,
  isLoading,
}: IListSongProps): JSX.Element {
  const {t} = useTranslation();
  const dispatch = useDispatch();
  const [isExpanded, setIsExpanded] = useState<boolean>(false);

  const displayedSongs = isExpanded ? data : data?.slice(0, 10);

  if (isLoading) {
    return (
      <div className="overflow-y-auto flex flex-col gap-3">
        {Array.from({length: 5}).map((_, index) => (
          <SongCardSkeleton key={index} />
        ))}
      </div>
    );
  }

  return (
    <div className="w-full flex flex-col gap-3">
      <h2 className="text-xl md:text-2xl font-semibold text-white">
        {t("topic.featured_songs", {
          length: (data?.length ?? 0) > 20 ? 100 : 20,
        })}
      </h2>
      {!displayedSongs?.length && (
        <div className="text-white w-full h-[20vh] flex justify-center items-center text-xl">
          {t("common.list_is_empty")}
        </div>
      )}
      <div>
        {displayedSongs?.map((song, index) => (
          <SongItem
            song={song}
            key={song.id}
            className="pl-4"
            showReleaseDate={false}
            left={
              <span className="text-white text-base font-semibold mr-5 flex justify-end w-6">
                {index + 1}
              </span>
            }
            handlePlayMusic={() => {
              dispatch(playSongFromList({song, songList: data}));
            }}
          />
        ))}
      </div>
      {data && data?.length > 10 && (
        <button
          className="mt-2 inline-flex items-center gap-2 text-[#FF4319] text-xs md:text-sm lg:text-base font-normal transition-all duration-300 mx-auto"
          onClick={() => setIsExpanded((prev) => !prev)}
        >
          {isExpanded ? (
            <>
              {t("common.see_less")}
              <IconArrowRight2 className="-rotate-90" />
            </>
          ) : (
            <>
              {t("common.see_more")}
              <IconArrowRight2 className="rotate-90" />
            </>
          )}
        </button>
      )}
    </div>
  );
}
