import {fetcher} from "./Fetcher";

export interface IAutoFill {
  id?: string;
  name?: string;
}

export interface IParamsAutofill {
  keyword?: string;
  page?: number;
  pageSize?: number;
  artistIds?: string[];
}

const path = {
  autoArtist: "autofill/artists",
  autoSinger: "autofill/singer",
  autoTheme: "autofill/theme",
  autoGenre: "autofill/genre",
  autoGenres: "autofill/genres", // Get all genres and theme
  autoCountry: "autofill/country",
  autoSongs: "autofill/songs",
  autoPlaylists: "autofill/playlists", // Get all album, playlist, top100
  autoPlaylist: "autofill/playlist",
  autoAlbum: "autofill/album",
  autoTop100: "autofill/top100",
};

function autoArtist(params: IParamsAutofill): Promise<IAutoFill[]> {
  return fetcher(
    {url: path.autoArtist, method: "get", params: params},
    {
      displayError: true,
    },
  );
}

function autoSinger(params: IParamsAutofill): Promise<IAutoFill[]> {
  return fetcher(
    {url: path.autoSinger, method: "get", params: params},
    {
      displayError: true,
    },
  );
}

function autoTheme(params: IParamsAutofill): Promise<IAutoFill[]> {
  return fetcher(
    {url: path.autoTheme, method: "get", params: params},
    {
      displayError: true,
    },
  );
}

function autoGenre(params: IParamsAutofill): Promise<IAutoFill[]> {
  return fetcher(
    {url: path.autoGenre, method: "get", params: params},
    {
      displayError: true,
    },
  );
}

function autoAlbum(params: IParamsAutofill): Promise<IAutoFill[]> {
  return fetcher(
    {
      url: path.autoAlbum,
      method: "get",
      params: params,
      paramsSerializer: {indexes: null},
    },
    {
      displayError: true,
    },
  );
}

function autoCountry(params: IParamsAutofill): Promise<IAutoFill[]> {
  return fetcher(
    {url: path.autoCountry, method: "get", params: params},
    {
      displayError: true,
    },
  );
}

function autoSong(params: IParamsAutofill): Promise<IAutoFill[]> {
  return fetcher(
    {url: path.autoSongs, method: "get", params: params},
    {
      displayError: true,
    },
  );
}

export default {
  autoArtist,
  autoTheme,
  autoGenre,
  autoAlbum,
  autoCountry,
  autoSinger,
  autoSong,
};
