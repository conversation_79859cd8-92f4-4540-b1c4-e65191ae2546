import {InputBase, InputBaseProps} from "@mui/material";
import clsx from "clsx";
import IconSearch from "src/components/Icon/IconSearch";

interface SearchInputProps {
  searchText: string;
  className?: string;
  onChange: (value: string) => void;
  inputBaseProps?: InputBaseProps;
  placeholder?: string;
}

export default function SearchInput({
  className,
  searchText,
  inputBaseProps,
  onChange,
  placeholder,
}: SearchInputProps) {
  return (
    <div
      className={clsx(
        "bg-[#F2F2F3] border border-[#DCDEE0] rounded-lg w-[360px] px-3 flex items-center gap-x-1.5",
        className,
      )}
    >
      <IconSearch stroke="#878D92" />
      <InputBase
        {...inputBaseProps}
        className="rounded-lg w-full"
        placeholder={placeholder ? placeholder : "Tìm kiếm..."}
        inputProps={{className: "!text-sm"}}
        value={searchText}
        onChange={(v) => onChange(v.target.value)}
      />
    </div>
  );
}
